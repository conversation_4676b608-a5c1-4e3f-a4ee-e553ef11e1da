/*
 * Copyright (C) 2015 Synopsys, Inc. All rights reserved.
 *
 * SPDX-License-Identifier:	GPL-2.0+
 */
/dts-v1/;

#include "skeleton.dtsi"

/ {
	#address-cells = <1>;
	#size-cells = <1>;

	aliases {
		console = &uart0;
	};

	clocks {
		apbclk: apbclk {
			compatible = "fixed-clock";
			clock-frequency = <50000000>;
			#clock-cells = <0>;
		};
	};

	uart0: serial0@e0022000 {
		compatible = "snps,dw-apb-uart";
		reg = <0xe0022000 0x1000>;
		reg-shift = <2>;
		reg-io-width = <4>;
	};

	ethernet@e0018000 {
		#interrupt-cells = <1>;
		compatible = "altr,socfpga-stmmac";
		reg = < 0xe0018000 0x2000 >;
		interrupts = < 25 >;
		interrupt-names = "macirq";
		phy-mode = "gmii";
		snps,pbl = < 32 >;
		clocks = <&apbclk>;
		clock-names = "stmmaceth";
		max-speed = <100>;
	};

	ehci@0xe0040000 {
		compatible = "generic-ehci";
		reg = < 0xe0040000 0x100 >;
		interrupts = < 8 >;
	};

	ohci@0xe0060000 {
		compatible = "generic-ohci";
		reg = < 0xe0060000 0x100 >;
		interrupts = < 8 >;
	};
};
