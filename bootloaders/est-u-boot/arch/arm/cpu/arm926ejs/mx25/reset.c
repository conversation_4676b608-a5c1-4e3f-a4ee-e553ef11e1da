/*
 * (C) Copyright 2002
 * Sysgo Real-Time Solutions, GmbH <www.elinos.com>
 * <PERSON> <<EMAIL>>
 *
 * (C) Copyright 2002
 * Sysgo Real-Time Solutions, GmbH <www.elinos.com>
 * <PERSON> <<EMAIL>>
 *
 * (C) Copyright 2002
 * <PERSON>, DENX Software Engineering, <<EMAIL>>
 *
 * (C) Copyright 2009
 * <PERSON>ya <PERSON>, Emcraft Systems Ltd, <<EMAIL>>
 *
 * SPDX-License-Identifier:	GPL-2.0+
 */

#include <common.h>
#include <asm/io.h>
#include <asm/arch/imx-regs.h>

/*
 * Reset the cpu by setting up the watchdog timer and let it time out
 */
void reset_cpu(ulong ignored)
{
	struct wdog_regs *regs = (struct wdog_regs *)IMX_WDT_BASE;
	/* Disable watchdog and set Time-Out field to 0 */
	writew(0, &regs->wcr);

	/* Write Service Sequence */
	writew(WSR_UNLOCK1, &regs->wsr);
	writew(WSR_UNLOCK2, &regs->wsr);

	/* Enable watchdog */
	writew(WCR_WDE, &regs->wcr);

	while (1) ;
}
