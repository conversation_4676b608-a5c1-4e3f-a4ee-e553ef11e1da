/*
 *  armboot - Startup Code for ARM926EJS CPU-core
 *
 *  Copyright (c) 2003  Texas Instruments
 *
 *  ----- Adapted for OMAP1610 OMAP730 from ARM925t code ------
 *
 *  Copyright (c) 2001	<PERSON> <<EMAIL>>
 *  Copyright (c) 2002	<PERSON> <<EMAIL>>
 *  Copyright (c) 2002	<PERSON> <<EMAIL>>
 *  Copyright (c) 2003	<PERSON> <<EMAIL>>
 *  Copyright (c) 2003	Kshitij <<EMAIL>>
 *
 * SPDX-License-Identifier:	GPL-2.0+
 */

	.align	5
.globl reset_cpu
reset_cpu:
	ldr	r1, rstctl1	/* get clkm1 reset ctl */
	mov	r3, #0x0
	strh	r3, [r1]	/* clear it */
	mov	r3, #0x8
	strh	r3, [r1]	/* force dsp+arm reset */
_loop_forever:
	b	_loop_forever

rstctl1:
	.word	0xfffece10
