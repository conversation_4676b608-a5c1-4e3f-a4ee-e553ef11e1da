if ARCH_MX6

config MX6
	bool
	default y

config MX6D
	bool

config MX6DL
	bool

config MX6Q
	bool

config MX6QDL
	bool

config MX6S
	bool

config MX6SL
	bool

config MX6SX
	select ROM_UNIFIED_SECTIONS
	bool

config MX6<PERSON><PERSON>
	select SYS_L2CACHE_OFF
	select ROM_UNIFIED_SECTIONS
	bool

choice
	prompt "MX6 board select"
	optional

config TARGET_ARISTAINETOS
	bool "aristainetos"

config TARGET_ARISTAINETOS2
	bool "aristainetos2"

config TARGET_ARISTAINETOS2B
	bool "Support aristainetos2-revB"

config TARGET_CGTQMX6EVAL
	bool "cgtqmx6eval"
	select SUPPORT_SPL
	select DM
	select DM_THERMAL

config TARGET_CM_FX6
	bool "CM-FX6"
	select SUPPORT_SPL
	select DM
	select DM_SERIAL
	select DM_GPIO

config TARGET_EMBESTMX6BOARDS
	bool "embestmx6boards"

config TARGET_GW_VENTANA
	bool "gw_ventana"
	select SUPPORT_SPL

config TARGET_KOSAGI_NOVENA
	bool "Kosagi Novena"
	select SUPPORT_SPL

config TARGET_MX6CUBOXI
	bool "Solid-run mx6 boards"
	select SUPPORT_SPL

config TARGET_MX6QARM2
	bool "mx6qarm2"

config TARGET_MX6QSABREAUTO
	bool "mx6qsabreauto"
	select DM
	select DM_THERMAL

config TARGET_MX6SABRESD
	bool "mx6sabresd"
	select SUPPORT_SPL
	select DM
	select DM_THERMAL

config TARGET_MX6SLEVK
	bool "mx6slevk"
	select SUPPORT_SPL

config TARGET_MX6SXSABRESD
	bool "mx6sxsabresd"
	select SUPPORT_SPL
	select DM
	select DM_THERMAL

config TARGET_MX6UL_9X9_EVK
	bool "mx6ul_9x9_evk"
	select MX6UL
	select DM
	select DM_THERMAL
	select SUPPORT_SPL

config TARGET_MX6UL_14X14_EVK
	bool "mx6ul_14x14_evk"
	select MX6UL
	select DM
	select DM_THERMAL
	select SUPPORT_SPL

config TARGET_NITROGEN6X
	bool "nitrogen6x"

config TARGET_OT1200
	bool "Bachmann OT1200"
	select SUPPORT_SPL

config TARGET_PLATINUM_PICON
	bool "platinum-picon"
	select SUPPORT_SPL

config TARGET_PLATINUM_TITANIUM
	bool "platinum-titanium"
	select SUPPORT_SPL

config TARGET_SECOMX6
	bool "secomx6 boards"

config TARGET_TBS2910
	bool "TBS2910 Matrix ARM mini PC"

config TARGET_TITANIUM
	bool "titanium"

config TARGET_TQMA6
	bool "TQ Systems TQMa6 board"

config TARGET_UDOO
	bool "udoo"
	select SUPPORT_SPL

config TARGET_WANDBOARD
	bool "wandboard"
	select SUPPORT_SPL

config TARGET_WARP
	bool "WaRP"

endchoice

config SYS_SOC
	default "mx6"

source "board/aristainetos/Kconfig"
source "board/bachmann/ot1200/Kconfig"
source "board/barco/platinum/Kconfig"
source "board/barco/titanium/Kconfig"
source "board/boundary/nitrogen6x/Kconfig"
source "board/compulab/cm_fx6/Kconfig"
source "board/congatec/cgtqmx6eval/Kconfig"
source "board/embest/mx6boards/Kconfig"
source "board/freescale/mx6qarm2/Kconfig"
source "board/freescale/mx6qsabreauto/Kconfig"
source "board/freescale/mx6sabresd/Kconfig"
source "board/freescale/mx6slevk/Kconfig"
source "board/freescale/mx6sxsabresd/Kconfig"
source "board/freescale/mx6ul_14x14_evk/Kconfig"
source "board/gateworks/gw_ventana/Kconfig"
source "board/kosagi/novena/Kconfig"
source "board/seco/Kconfig"
source "board/solidrun/mx6cuboxi/Kconfig"
source "board/tbs/tbs2910/Kconfig"
source "board/tqc/tqma6/Kconfig"
source "board/udoo/Kconfig"
source "board/wandboard/Kconfig"
source "board/warp/Kconfig"

endif
