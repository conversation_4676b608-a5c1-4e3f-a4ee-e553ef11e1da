/*
 *
 * DaVinci EMAC initialization.
 *
 * (C) Copyright 2011, <PERSON><PERSON>, Emcraft Systems
 *
 * SPDX-License-Identifier:	GPL-2.0+
 */

#include <common.h>
#include <netdev.h>
#include <asm/io.h>
#include <asm/arch/am35x_def.h>

/*
 * Initializes on-chip ethernet controllers.
 * to override, implement board_eth_init()
 */
int cpu_eth_init(bd_t *bis)
{
	u32 reset;

	/* ensure that the module is out of reset */
	reset = readl(&am35x_scm_general_regs->ip_sw_reset);
	reset &= ~CPGMACSS_SW_RST;
	writel(reset, &am35x_scm_general_regs->ip_sw_reset);

	return davinci_emac_initialize();
}
