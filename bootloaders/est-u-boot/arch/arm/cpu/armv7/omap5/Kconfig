if OMAP54XX

choice
	prompt "OMAP5 board select"
	optional

config TARGET_CM_T54
	bool "CompuLab CM-T54"

config TARGET_OMAP5_UEVM
	bool "TI OMAP5 uEVM board"

config TARGET_DRA7XX_EVM
	bool "TI DRA7XX"

config TARGET_BEAGLE_X15
	bool "BeagleBoard X15"

endchoice

config SYS_SOC
	default "omap5"

source "board/compulab/cm_t54/Kconfig"
source "board/ti/omap5_uevm/Kconfig"
source "board/ti/dra7xx/Kconfig"
source "board/ti/am57xx/Kconfig"

endif
