if RMOBILE

choice
	prompt "Renesus ARM SoCs board select"
	optional

config TARGET_ARMADILLO_800EVA
	bool "armadillo 800 eva board"

config TARGET_GOSE
	bool "Gose board"
	select DM
	select DM_SERIAL

config TARGET_KOELSCH
	bool "Koelsch board"
	select DM
	select DM_SERIAL

config TARGET_LAGER
	bool "Lager board"
	select DM
	select DM_SERIAL

config TARGET_KZM9G
	bool "KZM9D board"

config TARGET_ALT
	bool "Alt board"
	select DM
	select DM_SERIAL

config TARGET_SILK
	bool "Silk board"
	select DM
	select DM_SERIAL

config TARGET_PORTER
	bool "Porter board"
	select DM
	select DM_SERIAL

config TARGET_STOUT
	bool "Stout board"
	select DM
	select DM_SERIAL

endchoice

config SYS_SOC
	default "rmobile"

config RMOBILE_EXTRAM_BOOT
	bool "Enable boot from RAM"
	depends on TARGET_ALT || TARGET_KOELSCH || TARGET_LAGER || TARGET_PORTER || TARGET_SILK || TARGET_STOUT
	default n

choice
	prompt "Qos setting primary"
	depends on TARGET_ALT || TARGET_GOSE || TARGET_KOELSCH || TARGET_LAGER
	default QOS_PRI_NORMAL

config QOS_PRI_NORMAL
	bool "Non primary"
	help
	   Select normal mode for QoS setting.

config QOS_PRI_MEDIA
	bool "Media primary"
	help
	   Select multimedia primary mode for QoS setting.

config QOS_PRI_GFX
	bool "GFX primary"
	help
	   Select GFX(graphics) primary mode for QoS setting.

endchoice

source "board/atmark-techno/armadillo-800eva/Kconfig"
source "board/renesas/gose/Kconfig"
source "board/renesas/koelsch/Kconfig"
source "board/renesas/lager/Kconfig"
source "board/kmc/kzm9g/Kconfig"
source "board/renesas/alt/Kconfig"
source "board/renesas/silk/Kconfig"
source "board/renesas/porter/Kconfig"
source "board/renesas/stout/Kconfig"

endif
