/*
 * arch/arm/cpu/armv7/rmobile/pfc-r8a7790.c
 *     This file is r8a7790 processor support - PFC hardware block.
 *
 * Copy from linux-kernel:drivers/pinctrl/sh-pfc/pfc-r8a7790.c
 *
 * Copyright (C) 2013 Renesas Electronics Corporation
 * Copyright (C) 2013 Magnus Damm
 * Copyright (C) 2012 Renesas Solutions Corp.
 * Copyright (C) 2012 <PERSON>ninori Mo<PERSON>oto <<EMAIL>>
 *
 * SPDX-License-Identifier: GPL-2.0
 */

#include <common.h>
#include <sh_pfc.h>
#include <asm/gpio.h>
#include "pfc-r8a7790.h"

enum {
	PINMUX_RESERVED = 0,

	PINMUX_DATA_BEGIN,
	GP_ALL(DATA),
	PINMUX_DATA_END,

	PINMUX_INPUT_BEGIN,
	GP_ALL(IN),
	PINMUX_INPUT_END,

	PINMUX_OUTPUT_BEGIN,
	GP_<PERSON><PERSON>(OUT),
	PINMUX_OUTPUT_END,

	PINMUX_FUNCTION_BEGIN,
	GP_ALL(FN),

	/* GPSR0 */
	FN_IP0_2_0, FN_IP0_5_3, FN_IP0_8_6, FN_IP0_11_9, FN_IP0_15_12,
	FN_IP0_19_16, FN_IP0_22_20, FN_IP0_26_23, FN_IP0_30_27,
	FN_IP1_3_0, FN_IP1_7_4, FN_IP1_11_8, FN_IP1_14_12,
	FN_IP1_17_15, FN_IP1_21_18, FN_IP1_25_22, FN_IP1_27_26,
	FN_IP1_29_28, FN_IP2_2_0, FN_IP2_5_3, FN_IP2_8_6, FN_IP2_11_9,
	FN_IP2_14_12, FN_IP2_17_15, FN_IP2_21_18, FN_IP2_25_22,
	FN_IP2_28_26, FN_IP3_3_0, FN_IP3_7_4, FN_IP3_11_8,
	FN_IP3_14_12, FN_IP3_17_15,

	/* GPSR1 */
	FN_IP3_19_18, FN_IP3_22_20, FN_IP3_25_23, FN_IP3_28_26,
	FN_IP3_31_29, FN_IP4_2_0, FN_IP4_5_3, FN_IP4_8_6, FN_IP4_11_9,
	FN_IP4_14_12, FN_IP4_17_15, FN_IP4_20_18, FN_IP4_23_21,
	FN_IP4_26_24, FN_IP4_29_27, FN_IP5_2_0, FN_IP5_5_3, FN_IP5_9_6,
	FN_IP5_12_10, FN_IP5_14_13, FN_IP5_17_15, FN_IP5_20_18,
	FN_IP5_23_21, FN_IP5_26_24, FN_IP5_29_27, FN_IP6_2_0,
	FN_IP6_5_3, FN_IP6_8_6, FN_IP6_10_9, FN_IP6_13_11,

	/* GPSR2 */
	FN_IP7_28_27, FN_IP7_30_29, FN_IP8_1_0, FN_IP8_3_2, FN_IP8_5_4,
	FN_IP8_7_6, FN_IP8_9_8, FN_IP8_11_10, FN_IP8_13_12, FN_IP8_15_14,
	FN_IP8_17_16, FN_IP8_19_18, FN_IP8_21_20, FN_IP8_23_22,
	FN_IP8_25_24, FN_IP8_26, FN_IP8_27, FN_VI1_DATA7_VI1_B7,
	FN_IP6_16_14, FN_IP6_19_17, FN_IP6_22_20, FN_IP6_25_23,
	FN_IP6_28_26, FN_IP6_31_29, FN_IP7_2_0, FN_IP7_5_3, FN_IP7_7_6,
	FN_IP7_9_8, FN_IP7_12_10, FN_IP7_15_13,

	/* GPSR3 */
	FN_IP8_28, FN_IP8_30_29, FN_IP9_1_0, FN_IP9_3_2, FN_IP9_5_4,
	FN_IP9_7_6, FN_IP9_11_8, FN_IP9_15_12, FN_IP9_17_16, FN_IP9_19_18,
	FN_IP9_21_20, FN_IP9_23_22, FN_IP9_25_24, FN_IP9_27_26,
	FN_IP9_31_28, FN_IP10_3_0, FN_IP10_6_4, FN_IP10_10_7, FN_IP10_14_11,
	FN_IP10_18_15, FN_IP10_22_19, FN_IP10_25_23, FN_IP10_29_26,
	FN_IP11_3_0, FN_IP11_4, FN_IP11_6_5, FN_IP11_8_7, FN_IP11_10_9,
	FN_IP11_12_11, FN_IP11_14_13, FN_IP11_17_15, FN_IP11_21_18,

	/* GPSR4 */
	FN_IP11_23_22, FN_IP11_26_24, FN_IP11_29_27, FN_IP11_31_30,
	FN_IP12_1_0, FN_IP12_3_2, FN_IP12_5_4, FN_IP12_7_6, FN_IP12_10_8,
	FN_IP12_13_11, FN_IP12_16_14, FN_IP12_19_17, FN_IP12_22_20,
	FN_IP12_24_23, FN_IP12_27_25, FN_IP12_30_28, FN_IP13_2_0,
	FN_IP13_6_3, FN_IP13_9_7, FN_IP13_12_10, FN_IP13_15_13,
	FN_IP13_18_16, FN_IP13_22_19, FN_IP13_25_23, FN_IP13_28_26,
	FN_IP13_30_29, FN_IP14_2_0, FN_IP14_5_3, FN_IP14_8_6, FN_IP14_11_9,
	FN_IP14_15_12, FN_IP14_18_16,

	/* GPSR5 */
	FN_IP14_21_19, FN_IP14_24_22, FN_IP14_27_25, FN_IP14_30_28,
	FN_IP15_2_0, FN_IP15_5_3, FN_IP15_8_6, FN_IP15_11_9, FN_IP15_13_12,
	FN_IP15_15_14, FN_IP15_17_16, FN_IP15_19_18, FN_IP15_22_20,
	FN_IP15_25_23, FN_IP15_27_26, FN_IP15_29_28, FN_IP16_2_0,
	FN_IP16_5_3, FN_USB0_PWEN, FN_USB0_OVC_VBUS, FN_IP16_6, FN_IP16_7,
	FN_USB2_PWEN, FN_USB2_OVC, FN_AVS1, FN_AVS2, FN_DU_DOTCLKIN0,
	FN_IP7_26_25, FN_DU_DOTCLKIN2, FN_IP7_18_16, FN_IP7_21_19, FN_IP7_24_22,

	/* IPSR0 - IPSR5 */
	/* IPSR6 */
	FN_DACK0, FN_IRQ0, FN_INTC_IRQ0_N, FN_SSI_SCK6_B,
	FN_VI1_VSYNC_N, FN_VI1_VSYNC_N_B, FN_SSI_WS78_C,
	FN_DREQ1_N, FN_VI1_CLKENB, FN_VI1_CLKENB_B,
	FN_SSI_SDATA7_C, FN_SSI_SCK78_B, FN_DACK1, FN_IRQ1,
	FN_INTC_IRQ1_N, FN_SSI_WS6_B, FN_SSI_SDATA8_C,
	FN_DREQ2_N, FN_HSCK1_B, FN_HCTS0_N_B,
	FN_MSIOF0_TXD_B, FN_DACK2, FN_IRQ2, FN_INTC_IRQ2_N,
	FN_SSI_SDATA6_B, FN_HRTS0_N_B, FN_MSIOF0_RXD_B,
	FN_ETH_CRS_DV, FN_RMII_CRS_DV, FN_STP_ISCLK_0_B,
	FN_TS_SDEN0_D, FN_GLO_Q0_C, FN_SCL2_E,
	FN_SCL2_CIS_E, FN_ETH_RX_ER, FN_RMII_RX_ER,
	FN_STP_ISD_0_B, FN_TS_SPSYNC0_D, FN_GLO_Q1_C,
	FN_SDA2_E, FN_SDA2_CIS_E, FN_ETH_RXD0, FN_RMII_RXD0,
	FN_STP_ISEN_0_B, FN_TS_SDAT0_D, FN_GLO_I0_C,
	FN_SCIFB1_SCK_G, FN_SCK1_E, FN_ETH_RXD1,
	FN_RMII_RXD1, FN_HRX0_E, FN_STP_ISSYNC_0_B,
	FN_TS_SCK0_D, FN_GLO_I1_C, FN_SCIFB1_RXD_G,
	FN_RX1_E, FN_ETH_LINK, FN_RMII_LINK, FN_HTX0_E,
	FN_STP_IVCXO27_0_B, FN_SCIFB1_TXD_G, FN_TX1_E,
	FN_ETH_REF_CLK, FN_RMII_REF_CLK, FN_HCTS0_N_E,
	FN_STP_IVCXO27_1_B, FN_HRX0_F,

	/* IPSR7 */
	FN_ETH_MDIO, FN_RMII_MDIO, FN_HRTS0_N_E,
	FN_SIM0_D_C, FN_HCTS0_N_F, FN_ETH_TXD1,
	FN_RMII_TXD1, FN_HTX0_F, FN_BPFCLK_G, FN_RDS_CLK_F,
	FN_ETH_TX_EN, FN_RMII_TX_EN, FN_SIM0_CLK_C,
	FN_HRTS0_N_F, FN_ETH_MAGIC, FN_RMII_MAGIC,
	FN_SIM0_RST_C, FN_ETH_TXD0, FN_RMII_TXD0,
	FN_STP_ISCLK_1_B, FN_TS_SDEN1_C, FN_GLO_SCLK_C,
	FN_ETH_MDC, FN_RMII_MDC, FN_STP_ISD_1_B,
	FN_TS_SPSYNC1_C, FN_GLO_SDATA_C, FN_PWM0,
	FN_SCIFA2_SCK_C, FN_STP_ISEN_1_B, FN_TS_SDAT1_C,
	FN_GLO_SS_C, FN_PWM1, FN_SCIFA2_TXD_C,
	FN_STP_ISSYNC_1_B, FN_TS_SCK1_C, FN_GLO_RFON_C,
	FN_PCMOE_N, FN_PWM2, FN_PWMFSW0, FN_SCIFA2_RXD_C,
	FN_PCMWE_N, FN_IECLK_C, FN_DU1_DOTCLKIN,
	FN_AUDIO_CLKC, FN_AUDIO_CLKOUT_C, FN_VI0_CLK,
	FN_ATACS00_N, FN_AVB_RXD1, FN_MII_RXD1,
	FN_VI0_DATA0_VI0_B0, FN_ATACS10_N, FN_AVB_RXD2,
	FN_MII_RXD2,

	/* IPSR8 */
	FN_VI0_DATA1_VI0_B1, FN_ATARD0_N, FN_AVB_RXD3,
	FN_MII_RXD3, FN_VI0_DATA2_VI0_B2, FN_ATAWR0_N,
	FN_AVB_RXD4, FN_VI0_DATA3_VI0_B3, FN_ATADIR0_N,
	FN_AVB_RXD5, FN_VI0_DATA4_VI0_B4, FN_ATAG0_N,
	FN_AVB_RXD6, FN_VI0_DATA5_VI0_B5, FN_EX_WAIT1,
	FN_AVB_RXD7, FN_VI0_DATA6_VI0_B6, FN_AVB_RX_ER,
	FN_MII_RX_ER, FN_VI0_DATA7_VI0_B7, FN_AVB_RX_CLK,
	FN_MII_RX_CLK, FN_VI1_CLK, FN_AVB_RX_DV,
	FN_MII_RX_DV, FN_VI1_DATA0_VI1_B0, FN_SCIFA1_SCK_D,
	FN_AVB_CRS, FN_MII_CRS, FN_VI1_DATA1_VI1_B1,
	FN_SCIFA1_RXD_D, FN_AVB_MDC, FN_MII_MDC,
	FN_VI1_DATA2_VI1_B2, FN_SCIFA1_TXD_D, FN_AVB_MDIO,
	FN_MII_MDIO, FN_VI1_DATA3_VI1_B3, FN_SCIFA1_CTS_N_D,
	FN_AVB_GTX_CLK, FN_VI1_DATA4_VI1_B4, FN_SCIFA1_RTS_N_D,
	FN_AVB_MAGIC, FN_MII_MAGIC, FN_VI1_DATA5_VI1_B5,
	FN_AVB_PHY_INT, FN_VI1_DATA6_VI1_B6, FN_AVB_GTXREFCLK,
	FN_SD0_CLK, FN_VI1_DATA0_VI1_B0_B, FN_SD0_CMD,
	FN_SCIFB1_SCK_B, FN_VI1_DATA1_VI1_B1_B,

	/* IPSR9 */
	FN_SD0_DAT0, FN_SCIFB1_RXD_B, FN_VI1_DATA2_VI1_B2_B,
	FN_SD0_DAT1, FN_SCIFB1_TXD_B, FN_VI1_DATA3_VI1_B3_B,
	FN_SD0_DAT2, FN_SCIFB1_CTS_N_B, FN_VI1_DATA4_VI1_B4_B,
	FN_SD0_DAT3, FN_SCIFB1_RTS_N_B, FN_VI1_DATA5_VI1_B5_B,
	FN_SD0_CD, FN_MMC0_D6, FN_TS_SDEN0_B, FN_USB0_EXTP,
	FN_GLO_SCLK, FN_VI1_DATA6_VI1_B6_B, FN_SCL1_B,
	FN_SCL1_CIS_B, FN_VI2_DATA6_VI2_B6_B, FN_SD0_WP,
	FN_MMC0_D7, FN_TS_SPSYNC0_B, FN_USB0_IDIN,
	FN_GLO_SDATA, FN_VI1_DATA7_VI1_B7_B, FN_SDA1_B,
	FN_SDA1_CIS_B, FN_VI2_DATA7_VI2_B7_B, FN_SD1_CLK,
	FN_AVB_TX_EN, FN_MII_TX_EN, FN_SD1_CMD,
	FN_AVB_TX_ER, FN_MII_TX_ER, FN_SCIFB0_SCK_B,
	FN_SD1_DAT0, FN_AVB_TX_CLK, FN_MII_TX_CLK,
	FN_SCIFB0_RXD_B, FN_SD1_DAT1, FN_AVB_LINK,
	FN_MII_LINK, FN_SCIFB0_TXD_B, FN_SD1_DAT2,
	FN_AVB_COL, FN_MII_COL, FN_SCIFB0_CTS_N_B,
	FN_SD1_DAT3, FN_AVB_RXD0, FN_MII_RXD0,
	FN_SCIFB0_RTS_N_B, FN_SD1_CD, FN_MMC1_D6,
	FN_TS_SDEN1, FN_USB1_EXTP, FN_GLO_SS, FN_VI0_CLK_B,
	FN_SCL2_D, FN_SCL2_CIS_D, FN_SIM0_CLK_B,
	FN_VI3_CLK_B,

	/* IPSR10 */
	FN_SD1_WP, FN_MMC1_D7, FN_TS_SPSYNC1, FN_USB1_IDIN,
	FN_GLO_RFON, FN_VI1_CLK_B, FN_SDA2_D, FN_SDA2_CIS_D,
	FN_SIM0_D_B, FN_SD2_CLK, FN_MMC0_CLK, FN_SIM0_CLK,
	FN_VI0_DATA0_VI0_B0_B, FN_TS_SDEN0_C, FN_GLO_SCLK_B,
	FN_VI3_DATA0_B, FN_SD2_CMD, FN_MMC0_CMD, FN_SIM0_D,
	FN_VI0_DATA1_VI0_B1_B, FN_SCIFB1_SCK_E, FN_SCK1_D,
	FN_TS_SPSYNC0_C, FN_GLO_SDATA_B, FN_VI3_DATA1_B,
	FN_SD2_DAT0, FN_MMC0_D0, FN_FMCLK_B,
	FN_VI0_DATA2_VI0_B2_B, FN_SCIFB1_RXD_E, FN_RX1_D,
	FN_TS_SDAT0_C, FN_GLO_SS_B, FN_VI3_DATA2_B,
	FN_SD2_DAT1, FN_MMC0_D1, FN_FMIN_B, FN_RDS_DATA,
	FN_VI0_DATA3_VI0_B3_B, FN_SCIFB1_TXD_E, FN_TX1_D,
	FN_TS_SCK0_C, FN_GLO_RFON_B, FN_VI3_DATA3_B,
	FN_SD2_DAT2, FN_MMC0_D2, FN_BPFCLK_B, FN_RDS_CLK,
	FN_VI0_DATA4_VI0_B4_B, FN_HRX0_D, FN_TS_SDEN1_B,
	FN_GLO_Q0_B, FN_VI3_DATA4_B, FN_SD2_DAT3,
	FN_MMC0_D3, FN_SIM0_RST, FN_VI0_DATA5_VI0_B5_B,
	FN_HTX0_D, FN_TS_SPSYNC1_B, FN_GLO_Q1_B,
	FN_VI3_DATA5_B, FN_SD2_CD, FN_MMC0_D4,
	FN_TS_SDAT0_B, FN_USB2_EXTP, FN_GLO_I0,
	FN_VI0_DATA6_VI0_B6_B, FN_HCTS0_N_D, FN_TS_SDAT1_B,
	FN_GLO_I0_B, FN_VI3_DATA6_B,

	/* IPSR11 */
	FN_SD2_WP, FN_MMC0_D5, FN_TS_SCK0_B, FN_USB2_IDIN,
	FN_GLO_I1, FN_VI0_DATA7_VI0_B7_B, FN_HRTS0_N_D,
	FN_TS_SCK1_B, FN_GLO_I1_B, FN_VI3_DATA7_B,
	FN_SD3_CLK, FN_MMC1_CLK, FN_SD3_CMD, FN_MMC1_CMD,
	FN_MTS_N, FN_SD3_DAT0, FN_MMC1_D0, FN_STM_N,
	FN_SD3_DAT1, FN_MMC1_D1, FN_MDATA, FN_SD3_DAT2,
	FN_MMC1_D2, FN_SDATA, FN_SD3_DAT3, FN_MMC1_D3,
	FN_SCKZ, FN_SD3_CD, FN_MMC1_D4, FN_TS_SDAT1,
	FN_VSP, FN_GLO_Q0, FN_SIM0_RST_B, FN_SD3_WP,
	FN_MMC1_D5, FN_TS_SCK1, FN_GLO_Q1, FN_FMIN_C,
	FN_RDS_DATA_B, FN_FMIN_E, FN_RDS_DATA_D, FN_FMIN_F,
	FN_RDS_DATA_E, FN_MLB_CLK, FN_SCL2_B, FN_SCL2_CIS_B,
	FN_MLB_SIG, FN_SCIFB1_RXD_D, FN_RX1_C, FN_SDA2_B,
	FN_SDA2_CIS_B, FN_MLB_DAT, FN_SPV_EVEN,
	FN_SCIFB1_TXD_D, FN_TX1_C, FN_BPFCLK_C,
	FN_RDS_CLK_B, FN_SSI_SCK0129, FN_CAN_CLK_B,
	FN_MOUT0,

	FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, FN_SEL_SCIF1_3,
	FN_SEL_SCIF1_4,
	FN_SEL_SCIFB_0, FN_SEL_SCIFB_1, FN_SEL_SCIFB_2,
	FN_SEL_SCIFB2_0, FN_SEL_SCIFB2_1, FN_SEL_SCIFB2_2,
	FN_SEL_SCIFB1_0, FN_SEL_SCIFB1_1, FN_SEL_SCIFB1_2, FN_SEL_SCIFB1_3,
	FN_SEL_SCIFB1_4,
	FN_SEL_SCIFB1_5, FN_SEL_SCIFB1_6,
	FN_SEL_SCIFA1_0, FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2, FN_SEL_SCIFA1_3,
	FN_SEL_SCIF0_0, FN_SEL_SCIF0_1,
	FN_SEL_SCFA_0, FN_SEL_SCFA_1,
	FN_SEL_SOF1_0, FN_SEL_SOF1_1,
	FN_SEL_SSI7_0, FN_SEL_SSI7_1, FN_SEL_SSI7_2,
	FN_SEL_SSI6_0, FN_SEL_SSI6_1,
	FN_SEL_SSI5_0, FN_SEL_SSI5_1, FN_SEL_SSI5_2,
	FN_SEL_VI3_0, FN_SEL_VI3_1,
	FN_SEL_VI2_0, FN_SEL_VI2_1,
	FN_SEL_VI1_0, FN_SEL_VI1_1,
	FN_SEL_VI0_0, FN_SEL_VI0_1,
	FN_SEL_TSIF1_0, FN_SEL_TSIF1_1, FN_SEL_TSIF1_2,
	FN_SEL_LBS_0, FN_SEL_LBS_1,
	FN_SEL_TSIF0_0, FN_SEL_TSIF0_1, FN_SEL_TSIF0_2, FN_SEL_TSIF0_3,
	FN_SEL_SOF3_0, FN_SEL_SOF3_1,
	FN_SEL_SOF0_0, FN_SEL_SOF0_1,

	FN_SEL_TMU1_0, FN_SEL_TMU1_1,
	FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1,
	FN_SEL_SCIFCLK_0, FN_SEL_SCIFCLK_1,
	FN_SEL_CAN0_0, FN_SEL_CAN0_1, FN_SEL_CAN0_2, FN_SEL_CAN0_3,
	FN_SEL_CANCLK_0, FN_SEL_CANCLK_1,
	FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1, FN_SEL_SCIFA2_2,
	FN_SEL_CAN1_0, FN_SEL_CAN1_1,
	FN_SEL_ADI_0, FN_SEL_ADI_1,
	FN_SEL_SSP_0, FN_SEL_SSP_1,
	FN_SEL_FM_0, FN_SEL_FM_1, FN_SEL_FM_2, FN_SEL_FM_3,
	FN_SEL_FM_4, FN_SEL_FM_5, FN_SEL_FM_6,
	FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1, FN_SEL_HSCIF0_2, FN_SEL_HSCIF0_3,
	FN_SEL_HSCIF0_4, FN_SEL_HSCIF0_5,
	FN_SEL_GPS_0, FN_SEL_GPS_1, FN_SEL_GPS_2,
	FN_SEL_RDS_0, FN_SEL_RDS_1, FN_SEL_RDS_2,
	FN_SEL_RDS_3, FN_SEL_RDS_4, FN_SEL_RDS_5,
	FN_SEL_SIM_0, FN_SEL_SIM_1, FN_SEL_SIM_2,
	FN_SEL_SSI8_0, FN_SEL_SSI8_1, FN_SEL_SSI8_2,

	FN_SEL_IICDVFS_0, FN_SEL_IICDVFS_1,
	FN_SEL_IIC0_0, FN_SEL_IIC0_1,
	FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2,
	FN_SEL_IIC2_0, FN_SEL_IIC2_1, FN_SEL_IIC2_2, FN_SEL_IIC2_3,
	FN_SEL_IIC2_4,
	FN_SEL_IIC1_0, FN_SEL_IIC1_1, FN_SEL_IIC1_2,
	FN_SEL_I2C2_0, FN_SEL_I2C2_1, FN_SEL_I2C2_2, FN_SEL_I2C2_3,
	FN_SEL_I2C2_4,
	FN_SEL_I2C1_0, FN_SEL_I2C1_1, FN_SEL_I2C1_2,
	PINMUX_FUNCTION_END,

	PINMUX_MARK_BEGIN,

	VI1_DATA7_VI1_B7_MARK,

	USB0_PWEN_MARK, USB0_OVC_VBUS_MARK,
	USB2_PWEN_MARK, USB2_OVC_MARK, AVS1_MARK, AVS2_MARK,
	DU_DOTCLKIN0_MARK, DU_DOTCLKIN2_MARK,

	D0_MARK, MSIOF3_SCK_B_MARK, VI3_DATA0_MARK, VI0_G4_MARK, VI0_G4_B_MARK,
	D1_MARK, MSIOF3_SYNC_B_MARK, VI3_DATA1_MARK, VI0_G5_MARK,
	VI0_G5_B_MARK, D2_MARK, MSIOF3_RXD_B_MARK, VI3_DATA2_MARK,
	VI0_G6_MARK, VI0_G6_B_MARK, D3_MARK, MSIOF3_TXD_B_MARK,
	VI3_DATA3_MARK, VI0_G7_MARK, VI0_G7_B_MARK, D4_MARK,
	SCIFB1_RXD_F_MARK, SCIFB0_RXD_C_MARK, VI3_DATA4_MARK,
	VI0_R0_MARK, VI0_R0_B_MARK, RX0_B_MARK, D5_MARK,
	SCIFB1_TXD_F_MARK, SCIFB0_TXD_C_MARK, VI3_DATA5_MARK,
	VI0_R1_MARK, VI0_R1_B_MARK, TX0_B_MARK, D6_MARK,
	SCL2_C_MARK, VI3_DATA6_MARK, VI0_R2_MARK, VI0_R2_B_MARK,
	SCL2_CIS_C_MARK, D7_MARK, AD_DI_B_MARK, SDA2_C_MARK,
	VI3_DATA7_MARK, VI0_R3_MARK, VI0_R3_B_MARK, SDA2_CIS_C_MARK,
	D8_MARK, SCIFA1_SCK_C_MARK, AVB_TXD0_MARK, MII_TXD0_MARK,
	VI0_G0_MARK, VI0_G0_B_MARK, VI2_DATA0_VI2_B0_MARK,

	D9_MARK, SCIFA1_RXD_C_MARK, AVB_TXD1_MARK, MII_TXD1_MARK,
	VI0_G1_MARK, VI0_G1_B_MARK, VI2_DATA1_VI2_B1_MARK, D10_MARK,
	SCIFA1_TXD_C_MARK, AVB_TXD2_MARK, MII_TXD2_MARK,
	VI0_G2_MARK, VI0_G2_B_MARK, VI2_DATA2_VI2_B2_MARK, D11_MARK,
	SCIFA1_CTS_N_C_MARK, AVB_TXD3_MARK, MII_TXD3_MARK,
	VI0_G3_MARK, VI0_G3_B_MARK, VI2_DATA3_VI2_B3_MARK,
	D12_MARK, SCIFA1_RTS_N_C_MARK, AVB_TXD4_MARK,
	VI0_HSYNC_N_MARK, VI0_HSYNC_N_B_MARK, VI2_DATA4_VI2_B4_MARK,
	D13_MARK, AVB_TXD5_MARK, VI0_VSYNC_N_MARK,
	VI0_VSYNC_N_B_MARK, VI2_DATA5_VI2_B5_MARK, D14_MARK,
	SCIFB1_RXD_C_MARK, AVB_TXD6_MARK, RX1_B_MARK,
	VI0_CLKENB_MARK, VI0_CLKENB_B_MARK, VI2_DATA6_VI2_B6_MARK,
	D15_MARK, SCIFB1_TXD_C_MARK, AVB_TXD7_MARK, TX1_B_MARK,
	VI0_FIELD_MARK, VI0_FIELD_B_MARK, VI2_DATA7_VI2_B7_MARK,
	A0_MARK, PWM3_MARK, A1_MARK, PWM4_MARK,

	A2_MARK, PWM5_MARK, MSIOF1_SS1_B_MARK, A3_MARK,
	PWM6_MARK, MSIOF1_SS2_B_MARK, A4_MARK, MSIOF1_TXD_B_MARK,
	TPU0TO0_MARK, A5_MARK, SCIFA1_TXD_B_MARK, TPU0TO1_MARK,
	A6_MARK, SCIFA1_RTS_N_B_MARK, TPU0TO2_MARK, A7_MARK,
	SCIFA1_SCK_B_MARK, AUDIO_CLKOUT_B_MARK, TPU0TO3_MARK,
	A8_MARK, SCIFA1_RXD_B_MARK, SSI_SCK5_B_MARK, VI0_R4_MARK,
	VI0_R4_B_MARK, SCIFB2_RXD_C_MARK, VI2_DATA0_VI2_B0_B_MARK,
	A9_MARK, SCIFA1_CTS_N_B_MARK, SSI_WS5_B_MARK, VI0_R5_MARK,
	VI0_R5_B_MARK, SCIFB2_TXD_C_MARK, VI2_DATA1_VI2_B1_B_MARK,
	A10_MARK, SSI_SDATA5_B_MARK, MSIOF2_SYNC_MARK, VI0_R6_MARK,
	VI0_R6_B_MARK, VI2_DATA2_VI2_B2_B_MARK,

	A11_MARK, SCIFB2_CTS_N_B_MARK, MSIOF2_SCK_MARK, VI1_R0_MARK,
	VI1_R0_B_MARK, VI2_G0_MARK, VI2_DATA3_VI2_B3_B_MARK,
	A12_MARK, SCIFB2_RXD_B_MARK, MSIOF2_TXD_MARK, VI1_R1_MARK,
	VI1_R1_B_MARK, VI2_G1_MARK, VI2_DATA4_VI2_B4_B_MARK,
	A13_MARK, SCIFB2_RTS_N_B_MARK, EX_WAIT2_MARK,
	MSIOF2_RXD_MARK, VI1_R2_MARK, VI1_R2_B_MARK, VI2_G2_MARK,
	VI2_DATA5_VI2_B5_B_MARK, A14_MARK, SCIFB2_TXD_B_MARK,
	ATACS11_N_MARK, MSIOF2_SS1_MARK, A15_MARK, SCIFB2_SCK_B_MARK,
	ATARD1_N_MARK, MSIOF2_SS2_MARK, A16_MARK, ATAWR1_N_MARK,
	A17_MARK, AD_DO_B_MARK, ATADIR1_N_MARK, A18_MARK,
	AD_CLK_B_MARK, ATAG1_N_MARK, A19_MARK, AD_NCS_N_B_MARK,
	ATACS01_N_MARK, EX_WAIT0_B_MARK, A20_MARK, SPCLK_MARK,
	VI1_R3_MARK, VI1_R3_B_MARK, VI2_G4_MARK,

	A21_MARK, MOSI_IO0_MARK, VI1_R4_MARK, VI1_R4_B_MARK, VI2_G5_MARK,
	A22_MARK, MISO_IO1_MARK, VI1_R5_MARK, VI1_R5_B_MARK,
	VI2_G6_MARK, A23_MARK, IO2_MARK, VI1_G7_MARK,
	VI1_G7_B_MARK, VI2_G7_MARK, A24_MARK, IO3_MARK,
	VI1_R7_MARK, VI1_R7_B_MARK, VI2_CLKENB_MARK,
	VI2_CLKENB_B_MARK, A25_MARK, SSL_MARK, VI1_G6_MARK,
	VI1_G6_B_MARK, VI2_FIELD_MARK, VI2_FIELD_B_MARK, CS0_N_MARK,
	VI1_R6_MARK, VI1_R6_B_MARK, VI2_G3_MARK, MSIOF0_SS2_B_MARK,
	CS1_N_A26_MARK, SPEEDIN_MARK, VI0_R7_MARK, VI0_R7_B_MARK,
	VI2_CLK_MARK, VI2_CLK_B_MARK, EX_CS0_N_MARK, HRX1_B_MARK,
	VI1_G5_MARK, VI1_G5_B_MARK, VI2_R0_MARK, HTX0_B_MARK,
	MSIOF0_SS1_B_MARK, EX_CS1_N_MARK, GPS_CLK_MARK,
	HCTS1_N_B_MARK, VI1_FIELD_MARK, VI1_FIELD_B_MARK,
	VI2_R1_MARK, EX_CS2_N_MARK, GPS_SIGN_MARK, HRTS1_N_B_MARK,
	VI3_CLKENB_MARK, VI1_G0_MARK, VI1_G0_B_MARK, VI2_R2_MARK,

	EX_CS3_N_MARK, GPS_MAG_MARK, VI3_FIELD_MARK,
	VI1_G1_MARK, VI1_G1_B_MARK, VI2_R3_MARK,
	EX_CS4_N_MARK, MSIOF1_SCK_B_MARK, VI3_HSYNC_N_MARK,
	VI2_HSYNC_N_MARK, SCL1_MARK, VI2_HSYNC_N_B_MARK,
	INTC_EN0_N_MARK, SCL1_CIS_MARK, EX_CS5_N_MARK, CAN0_RX_MARK,
	MSIOF1_RXD_B_MARK, VI3_VSYNC_N_MARK, VI1_G2_MARK,
	VI1_G2_B_MARK, VI2_R4_MARK, SDA1_MARK, INTC_EN1_N_MARK,
	SDA1_CIS_MARK, BS_N_MARK, IETX_MARK, HTX1_B_MARK,
	CAN1_TX_MARK, DRACK0_MARK, IETX_C_MARK, RD_N_MARK,
	CAN0_TX_MARK, SCIFA0_SCK_B_MARK, RD_WR_N_MARK, VI1_G3_MARK,
	VI1_G3_B_MARK, VI2_R5_MARK, SCIFA0_RXD_B_MARK,
	INTC_IRQ4_N_MARK, WE0_N_MARK, IECLK_MARK, CAN_CLK_MARK,
	VI2_VSYNC_N_MARK, SCIFA0_TXD_B_MARK, VI2_VSYNC_N_B_MARK,
	WE1_N_MARK, IERX_MARK, CAN1_RX_MARK, VI1_G4_MARK,
	VI1_G4_B_MARK, VI2_R6_MARK, SCIFA0_CTS_N_B_MARK,
	IERX_C_MARK, EX_WAIT0_MARK, IRQ3_MARK, INTC_IRQ3_N_MARK,
	VI3_CLK_MARK, SCIFA0_RTS_N_B_MARK, HRX0_B_MARK,
	MSIOF0_SCK_B_MARK, DREQ0_N_MARK, VI1_HSYNC_N_MARK,
	VI1_HSYNC_N_B_MARK, VI2_R7_MARK, SSI_SCK78_C_MARK,
	SSI_WS78_B_MARK,

	DACK0_MARK, IRQ0_MARK, INTC_IRQ0_N_MARK, SSI_SCK6_B_MARK,
	VI1_VSYNC_N_MARK, VI1_VSYNC_N_B_MARK, SSI_WS78_C_MARK,
	DREQ1_N_MARK, VI1_CLKENB_MARK, VI1_CLKENB_B_MARK,
	SSI_SDATA7_C_MARK, SSI_SCK78_B_MARK, DACK1_MARK, IRQ1_MARK,
	INTC_IRQ1_N_MARK, SSI_WS6_B_MARK, SSI_SDATA8_C_MARK,
	DREQ2_N_MARK, HSCK1_B_MARK, HCTS0_N_B_MARK,
	MSIOF0_TXD_B_MARK, DACK2_MARK, IRQ2_MARK, INTC_IRQ2_N_MARK,
	SSI_SDATA6_B_MARK, HRTS0_N_B_MARK, MSIOF0_RXD_B_MARK,
	ETH_CRS_DV_MARK, RMII_CRS_DV_MARK, STP_ISCLK_0_B_MARK,
	TS_SDEN0_D_MARK, GLO_Q0_C_MARK, SCL2_E_MARK,
	SCL2_CIS_E_MARK, ETH_RX_ER_MARK, RMII_RX_ER_MARK,
	STP_ISD_0_B_MARK, TS_SPSYNC0_D_MARK, GLO_Q1_C_MARK,
	SDA2_E_MARK, SDA2_CIS_E_MARK, ETH_RXD0_MARK, RMII_RXD0_MARK,
	STP_ISEN_0_B_MARK, TS_SDAT0_D_MARK, GLO_I0_C_MARK,
	SCIFB1_SCK_G_MARK, SCK1_E_MARK, ETH_RXD1_MARK,
	RMII_RXD1_MARK, HRX0_E_MARK, STP_ISSYNC_0_B_MARK,
	TS_SCK0_D_MARK, GLO_I1_C_MARK, SCIFB1_RXD_G_MARK,
	RX1_E_MARK, ETH_LINK_MARK, RMII_LINK_MARK, HTX0_E_MARK,
	STP_IVCXO27_0_B_MARK, SCIFB1_TXD_G_MARK, TX1_E_MARK,
	ETH_REF_CLK_MARK, RMII_REF_CLK_MARK, HCTS0_N_E_MARK,
	STP_IVCXO27_1_B_MARK, HRX0_F_MARK,

	ETH_MDIO_MARK, RMII_MDIO_MARK, HRTS0_N_E_MARK,
	SIM0_D_C_MARK, HCTS0_N_F_MARK, ETH_TXD1_MARK,
	RMII_TXD1_MARK, HTX0_F_MARK, BPFCLK_G_MARK, RDS_CLK_F_MARK,
	ETH_TX_EN_MARK, RMII_TX_EN_MARK, SIM0_CLK_C_MARK,
	HRTS0_N_F_MARK, ETH_MAGIC_MARK, RMII_MAGIC_MARK,
	SIM0_RST_C_MARK, ETH_TXD0_MARK, RMII_TXD0_MARK,
	STP_ISCLK_1_B_MARK, TS_SDEN1_C_MARK, GLO_SCLK_C_MARK,
	ETH_MDC_MARK, RMII_MDC_MARK, STP_ISD_1_B_MARK,
	TS_SPSYNC1_C_MARK, GLO_SDATA_C_MARK, PWM0_MARK,
	SCIFA2_SCK_C_MARK, STP_ISEN_1_B_MARK, TS_SDAT1_C_MARK,
	GLO_SS_C_MARK, PWM1_MARK, SCIFA2_TXD_C_MARK,
	STP_ISSYNC_1_B_MARK, TS_SCK1_C_MARK, GLO_RFON_C_MARK,
	PCMOE_N_MARK, PWM2_MARK, PWMFSW0_MARK, SCIFA2_RXD_C_MARK,
	PCMWE_N_MARK, IECLK_C_MARK, DU1_DOTCLKIN_MARK,
	AUDIO_CLKC_MARK, AUDIO_CLKOUT_C_MARK, VI0_CLK_MARK,
	ATACS00_N_MARK, AVB_RXD1_MARK, MII_RXD1_MARK,
	VI0_DATA0_VI0_B0_MARK, ATACS10_N_MARK, AVB_RXD2_MARK,
	MII_RXD2_MARK,

	VI0_DATA1_VI0_B1_MARK, ATARD0_N_MARK, AVB_RXD3_MARK,
	MII_RXD3_MARK, VI0_DATA2_VI0_B2_MARK, ATAWR0_N_MARK,
	AVB_RXD4_MARK, VI0_DATA3_VI0_B3_MARK, ATADIR0_N_MARK,
	AVB_RXD5_MARK, VI0_DATA4_VI0_B4_MARK, ATAG0_N_MARK,
	AVB_RXD6_MARK, VI0_DATA5_VI0_B5_MARK, EX_WAIT1_MARK,
	AVB_RXD7_MARK, VI0_DATA6_VI0_B6_MARK, AVB_RX_ER_MARK,
	MII_RX_ER_MARK, VI0_DATA7_VI0_B7_MARK, AVB_RX_CLK_MARK,
	MII_RX_CLK_MARK, VI1_CLK_MARK, AVB_RX_DV_MARK,
	MII_RX_DV_MARK, VI1_DATA0_VI1_B0_MARK, SCIFA1_SCK_D_MARK,
	AVB_CRS_MARK, MII_CRS_MARK, VI1_DATA1_VI1_B1_MARK,
	SCIFA1_RXD_D_MARK, AVB_MDC_MARK, MII_MDC_MARK,
	VI1_DATA2_VI1_B2_MARK, SCIFA1_TXD_D_MARK, AVB_MDIO_MARK,
	MII_MDIO_MARK, VI1_DATA3_VI1_B3_MARK, SCIFA1_CTS_N_D_MARK,
	AVB_GTX_CLK_MARK, VI1_DATA4_VI1_B4_MARK, SCIFA1_RTS_N_D_MARK,
	AVB_MAGIC_MARK, MII_MAGIC_MARK, VI1_DATA5_VI1_B5_MARK,
	AVB_PHY_INT_MARK, VI1_DATA6_VI1_B6_MARK, AVB_GTXREFCLK_MARK,
	SD0_CLK_MARK, VI1_DATA0_VI1_B0_B_MARK, SD0_CMD_MARK,
	SCIFB1_SCK_B_MARK, VI1_DATA1_VI1_B1_B_MARK,

	SD0_DAT0_MARK, SCIFB1_RXD_B_MARK, VI1_DATA2_VI1_B2_B_MARK,
	SD0_DAT1_MARK, SCIFB1_TXD_B_MARK, VI1_DATA3_VI1_B3_B_MARK,
	SD0_DAT2_MARK, SCIFB1_CTS_N_B_MARK, VI1_DATA4_VI1_B4_B_MARK,
	SD0_DAT3_MARK, SCIFB1_RTS_N_B_MARK, VI1_DATA5_VI1_B5_B_MARK,
	SD0_CD_MARK, MMC0_D6_MARK, TS_SDEN0_B_MARK, USB0_EXTP_MARK,
	GLO_SCLK_MARK, VI1_DATA6_VI1_B6_B_MARK, SCL1_B_MARK,
	SCL1_CIS_B_MARK, VI2_DATA6_VI2_B6_B_MARK, SD0_WP_MARK,
	MMC0_D7_MARK, TS_SPSYNC0_B_MARK, USB0_IDIN_MARK,
	GLO_SDATA_MARK, VI1_DATA7_VI1_B7_B_MARK, SDA1_B_MARK,
	SDA1_CIS_B_MARK, VI2_DATA7_VI2_B7_B_MARK, SD1_CLK_MARK,
	AVB_TX_EN_MARK, MII_TX_EN_MARK, SD1_CMD_MARK,
	AVB_TX_ER_MARK, MII_TX_ER_MARK, SCIFB0_SCK_B_MARK,
	SD1_DAT0_MARK, AVB_TX_CLK_MARK, MII_TX_CLK_MARK,
	SCIFB0_RXD_B_MARK, SD1_DAT1_MARK, AVB_LINK_MARK,
	MII_LINK_MARK, SCIFB0_TXD_B_MARK, SD1_DAT2_MARK,
	AVB_COL_MARK, MII_COL_MARK, SCIFB0_CTS_N_B_MARK,
	SD1_DAT3_MARK, AVB_RXD0_MARK, MII_RXD0_MARK,
	SCIFB0_RTS_N_B_MARK, SD1_CD_MARK, MMC1_D6_MARK,
	TS_SDEN1_MARK, USB1_EXTP_MARK, GLO_SS_MARK, VI0_CLK_B_MARK,
	SCL2_D_MARK, SCL2_CIS_D_MARK, SIM0_CLK_B_MARK,
	VI3_CLK_B_MARK,

	SD1_WP_MARK, MMC1_D7_MARK, TS_SPSYNC1_MARK, USB1_IDIN_MARK,
	GLO_RFON_MARK, VI1_CLK_B_MARK, SDA2_D_MARK, SDA2_CIS_D_MARK,
	SIM0_D_B_MARK, SD2_CLK_MARK, MMC0_CLK_MARK, SIM0_CLK_MARK,
	VI0_DATA0_VI0_B0_B_MARK, TS_SDEN0_C_MARK, GLO_SCLK_B_MARK,
	VI3_DATA0_B_MARK, SD2_CMD_MARK, MMC0_CMD_MARK, SIM0_D_MARK,
	VI0_DATA1_VI0_B1_B_MARK, SCIFB1_SCK_E_MARK, SCK1_D_MARK,
	TS_SPSYNC0_C_MARK, GLO_SDATA_B_MARK, VI3_DATA1_B_MARK,
	SD2_DAT0_MARK, MMC0_D0_MARK, FMCLK_B_MARK,
	VI0_DATA2_VI0_B2_B_MARK, SCIFB1_RXD_E_MARK, RX1_D_MARK,
	TS_SDAT0_C_MARK, GLO_SS_B_MARK, VI3_DATA2_B_MARK,
	SD2_DAT1_MARK, MMC0_D1_MARK, FMIN_B_MARK, RDS_DATA_MARK,
	VI0_DATA3_VI0_B3_B_MARK, SCIFB1_TXD_E_MARK, TX1_D_MARK,
	TS_SCK0_C_MARK, GLO_RFON_B_MARK, VI3_DATA3_B_MARK,
	SD2_DAT2_MARK, MMC0_D2_MARK, BPFCLK_B_MARK, RDS_CLK_MARK,
	VI0_DATA4_VI0_B4_B_MARK, HRX0_D_MARK, TS_SDEN1_B_MARK,
	GLO_Q0_B_MARK, VI3_DATA4_B_MARK, SD2_DAT3_MARK,
	MMC0_D3_MARK, SIM0_RST_MARK, VI0_DATA5_VI0_B5_B_MARK,
	HTX0_D_MARK, TS_SPSYNC1_B_MARK, GLO_Q1_B_MARK,
	VI3_DATA5_B_MARK, SD2_CD_MARK, MMC0_D4_MARK,
	TS_SDAT0_B_MARK, USB2_EXTP_MARK, GLO_I0_MARK,
	VI0_DATA6_VI0_B6_B_MARK, HCTS0_N_D_MARK, TS_SDAT1_B_MARK,
	GLO_I0_B_MARK, VI3_DATA6_B_MARK,

	SD2_WP_MARK, MMC0_D5_MARK, TS_SCK0_B_MARK, USB2_IDIN_MARK,
	GLO_I1_MARK, VI0_DATA7_VI0_B7_B_MARK, HRTS0_N_D_MARK,
	TS_SCK1_B_MARK, GLO_I1_B_MARK, VI3_DATA7_B_MARK,
	SD3_CLK_MARK, MMC1_CLK_MARK, SD3_CMD_MARK, MMC1_CMD_MARK,
	MTS_N_MARK, SD3_DAT0_MARK, MMC1_D0_MARK, STM_N_MARK,
	SD3_DAT1_MARK, MMC1_D1_MARK, MDATA_MARK, SD3_DAT2_MARK,
	MMC1_D2_MARK, SDATA_MARK, SD3_DAT3_MARK, MMC1_D3_MARK,
	SCKZ_MARK, SD3_CD_MARK, MMC1_D4_MARK, TS_SDAT1_MARK,
	VSP_MARK, GLO_Q0_MARK, SIM0_RST_B_MARK, SD3_WP_MARK,
	MMC1_D5_MARK, TS_SCK1_MARK, GLO_Q1_MARK, FMIN_C_MARK,
	RDS_DATA_B_MARK, FMIN_E_MARK, RDS_DATA_D_MARK, FMIN_F_MARK,
	RDS_DATA_E_MARK, MLB_CLK_MARK, SCL2_B_MARK, SCL2_CIS_B_MARK,
	MLB_SIG_MARK, SCIFB1_RXD_D_MARK, RX1_C_MARK, SDA2_B_MARK,
	SDA2_CIS_B_MARK, MLB_DAT_MARK, SPV_EVEN_MARK,
	SCIFB1_TXD_D_MARK, TX1_C_MARK, BPFCLK_C_MARK,
	RDS_CLK_B_MARK, SSI_SCK0129_MARK, CAN_CLK_B_MARK,
	MOUT0_MARK,

	SSI_WS0129_MARK, CAN0_TX_B_MARK, MOUT1_MARK,
	SSI_SDATA0_MARK, CAN0_RX_B_MARK, MOUT2_MARK,
	SSI_SDATA1_MARK, CAN1_TX_B_MARK, MOUT5_MARK,
	SSI_SDATA2_MARK, CAN1_RX_B_MARK, SSI_SCK1_MARK, MOUT6_MARK,
	SSI_SCK34_MARK, STP_OPWM_0_MARK, SCIFB0_SCK_MARK,
	MSIOF1_SCK_MARK, CAN_DEBUG_HW_TRIGGER_MARK, SSI_WS34_MARK,
	STP_IVCXO27_0_MARK, SCIFB0_RXD_MARK, MSIOF1_SYNC_MARK,
	CAN_STEP0_MARK, SSI_SDATA3_MARK, STP_ISCLK_0_MARK,
	SCIFB0_TXD_MARK, MSIOF1_SS1_MARK, CAN_TXCLK_MARK,
	SSI_SCK4_MARK, STP_ISD_0_MARK, SCIFB0_CTS_N_MARK,
	MSIOF1_SS2_MARK, SSI_SCK5_C_MARK, CAN_DEBUGOUT0_MARK,
	SSI_WS4_MARK, STP_ISEN_0_MARK, SCIFB0_RTS_N_MARK,
	MSIOF1_TXD_MARK, SSI_WS5_C_MARK, CAN_DEBUGOUT1_MARK,
	SSI_SDATA4_MARK, STP_ISSYNC_0_MARK, MSIOF1_RXD_MARK,
	CAN_DEBUGOUT2_MARK, SSI_SCK5_MARK, SCIFB1_SCK_MARK,
	IERX_B_MARK, DU2_EXHSYNC_DU2_HSYNC_MARK, QSTH_QHS_MARK,
	CAN_DEBUGOUT3_MARK, SSI_WS5_MARK, SCIFB1_RXD_MARK,
	IECLK_B_MARK, DU2_EXVSYNC_DU2_VSYNC_MARK, QSTB_QHE_MARK,
	CAN_DEBUGOUT4_MARK,

	SSI_SDATA5_MARK, SCIFB1_TXD_MARK, IETX_B_MARK, DU2_DR2_MARK,
	LCDOUT2_MARK, CAN_DEBUGOUT5_MARK, SSI_SCK6_MARK,
	SCIFB1_CTS_N_MARK, BPFCLK_D_MARK, RDS_CLK_C_MARK,
	DU2_DR3_MARK, LCDOUT3_MARK, CAN_DEBUGOUT6_MARK,
	BPFCLK_F_MARK, RDS_CLK_E_MARK, SSI_WS6_MARK,
	SCIFB1_RTS_N_MARK, CAN0_TX_D_MARK, DU2_DR4_MARK,
	LCDOUT4_MARK, CAN_DEBUGOUT7_MARK, SSI_SDATA6_MARK,
	FMIN_D_MARK, RDS_DATA_C_MARK, DU2_DR5_MARK, LCDOUT5_MARK,
	CAN_DEBUGOUT8_MARK, SSI_SCK78_MARK, STP_IVCXO27_1_MARK,
	SCK1_MARK, SCIFA1_SCK_MARK, DU2_DR6_MARK, LCDOUT6_MARK,
	CAN_DEBUGOUT9_MARK, SSI_WS78_MARK, STP_ISCLK_1_MARK,
	SCIFB2_SCK_MARK, SCIFA2_CTS_N_MARK, DU2_DR7_MARK,
	LCDOUT7_MARK, CAN_DEBUGOUT10_MARK, SSI_SDATA7_MARK,
	STP_ISD_1_MARK, SCIFB2_RXD_MARK, SCIFA2_RTS_N_MARK,
	TCLK2_MARK, QSTVA_QVS_MARK, CAN_DEBUGOUT11_MARK,
	BPFCLK_E_MARK, RDS_CLK_D_MARK, SSI_SDATA7_B_MARK,
	FMIN_G_MARK, RDS_DATA_F_MARK, SSI_SDATA8_MARK,
	STP_ISEN_1_MARK, SCIFB2_TXD_MARK, CAN0_TX_C_MARK,
	CAN_DEBUGOUT12_MARK, SSI_SDATA8_B_MARK, SSI_SDATA9_MARK,
	STP_ISSYNC_1_MARK, SCIFB2_CTS_N_MARK, SSI_WS1_MARK,
	SSI_SDATA5_C_MARK, CAN_DEBUGOUT13_MARK, AUDIO_CLKA_MARK,
	SCIFB2_RTS_N_MARK, CAN_DEBUGOUT14_MARK,

	AUDIO_CLKB_MARK, SCIF_CLK_MARK, CAN0_RX_D_MARK,
	DVC_MUTE_MARK, CAN0_RX_C_MARK, CAN_DEBUGOUT15_MARK,
	REMOCON_MARK, SCIFA0_SCK_MARK, HSCK1_MARK, SCK0_MARK,
	MSIOF3_SS2_MARK, DU2_DG2_MARK, LCDOUT10_MARK, SDA1_C_MARK,
	SDA1_CIS_C_MARK, SCIFA0_RXD_MARK, HRX1_MARK, RX0_MARK,
	DU2_DR0_MARK, LCDOUT0_MARK, SCIFA0_TXD_MARK, HTX1_MARK,
	TX0_MARK, DU2_DR1_MARK, LCDOUT1_MARK, SCIFA0_CTS_N_MARK,
	HCTS1_N_MARK, CTS0_N_MARK, MSIOF3_SYNC_MARK, DU2_DG3_MARK,
	LCDOUT11_MARK, PWM0_B_MARK, SCL1_C_MARK, SCL1_CIS_C_MARK,
	SCIFA0_RTS_N_MARK, HRTS1_N_MARK, RTS0_N_TANS_MARK,
	MSIOF3_SS1_MARK, DU2_DG0_MARK, LCDOUT8_MARK, PWM1_B_MARK,
	SCIFA1_RXD_MARK, AD_DI_MARK, RX1_MARK,
	DU2_EXODDF_DU2_ODDF_DISP_CDE_MARK, QCPV_QDE_MARK,
	SCIFA1_TXD_MARK, AD_DO_MARK, TX1_MARK, DU2_DG1_MARK,
	LCDOUT9_MARK, SCIFA1_CTS_N_MARK, AD_CLK_MARK,
	CTS1_N_MARK, MSIOF3_RXD_MARK, DU0_DOTCLKOUT_MARK, QCLK_MARK,
	SCIFA1_RTS_N_MARK, AD_NCS_N_MARK, RTS1_N_TANS_MARK,
	MSIOF3_TXD_MARK, DU1_DOTCLKOUT_MARK, QSTVB_QVE_MARK,
	HRTS0_N_C_MARK,

	SCIFA2_SCK_MARK, FMCLK_MARK, MSIOF3_SCK_MARK, DU2_DG7_MARK,
	LCDOUT15_MARK, SCIF_CLK_B_MARK, SCIFA2_RXD_MARK, FMIN_MARK,
	DU2_DB0_MARK, LCDOUT16_MARK, SCL2_MARK, SCL2_CIS_MARK,
	SCIFA2_TXD_MARK, BPFCLK_MARK, DU2_DB1_MARK, LCDOUT17_MARK,
	SDA2_MARK, SDA2_CIS_MARK, HSCK0_MARK, TS_SDEN0_MARK,
	DU2_DG4_MARK, LCDOUT12_MARK, HCTS0_N_C_MARK, HRX0_MARK,
	DU2_DB2_MARK, LCDOUT18_MARK, HTX0_MARK, DU2_DB3_MARK,
	LCDOUT19_MARK, HCTS0_N_MARK, SSI_SCK9_MARK, DU2_DB4_MARK,
	LCDOUT20_MARK, HRTS0_N_MARK, SSI_WS9_MARK, DU2_DB5_MARK,
	LCDOUT21_MARK, MSIOF0_SCK_MARK, TS_SDAT0_MARK, ADICLK_MARK,
	DU2_DB6_MARK, LCDOUT22_MARK, MSIOF0_SYNC_MARK, TS_SCK0_MARK,
	SSI_SCK2_MARK, ADIDATA_MARK, DU2_DB7_MARK, LCDOUT23_MARK,
	SCIFA2_RXD_B_MARK, MSIOF0_SS1_MARK, ADICHS0_MARK,
	DU2_DG5_MARK, LCDOUT13_MARK, MSIOF0_TXD_MARK, ADICHS1_MARK,
	DU2_DG6_MARK, LCDOUT14_MARK,

	MSIOF0_SS2_MARK, AUDIO_CLKOUT_MARK, ADICHS2_MARK,
	DU2_DISP_MARK, QPOLA_MARK, HTX0_C_MARK, SCIFA2_TXD_B_MARK,
	MSIOF0_RXD_MARK, TS_SPSYNC0_MARK, SSI_WS2_MARK,
	ADICS_SAMP_MARK, DU2_CDE_MARK, QPOLB_MARK, HRX0_C_MARK,
	USB1_PWEN_MARK, AUDIO_CLKOUT_D_MARK, USB1_OVC_MARK,
	TCLK1_B_MARK,
	PINMUX_MARK_END,
};

static pinmux_enum_t pinmux_data[] = {
	PINMUX_DATA_GP_ALL(), /* PINMUX_DATA(GP_M_N_DATA, GP_M_N_FN...), */

	PINMUX_DATA(VI1_DATA7_VI1_B7_MARK, FN_VI1_DATA7_VI1_B7),
	PINMUX_DATA(USB0_PWEN_MARK, FN_USB0_PWEN),
	PINMUX_DATA(USB0_OVC_VBUS_MARK, FN_USB0_OVC_VBUS),
	PINMUX_DATA(USB2_PWEN_MARK, FN_USB2_PWEN),
	PINMUX_DATA(USB2_OVC_MARK, FN_USB2_OVC),
	PINMUX_DATA(AVS1_MARK, FN_AVS1),
	PINMUX_DATA(AVS2_MARK, FN_AVS2),
	PINMUX_DATA(DU_DOTCLKIN0_MARK, FN_DU_DOTCLKIN0),
	PINMUX_DATA(DU_DOTCLKIN2_MARK, FN_DU_DOTCLKIN2),

	PINMUX_IPSR_DATA(IP6_2_0, DACK0),
	PINMUX_IPSR_DATA(IP6_2_0, IRQ0),
	PINMUX_IPSR_DATA(IP6_2_0, INTC_IRQ0_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, SSI_SCK6_B, SEL_SSI6_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, VI1_VSYNC_N, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, VI1_VSYNC_N_B, SEL_VI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, SSI_WS78_C, SEL_SSI7_2),
	PINMUX_IPSR_DATA(IP6_5_3, DREQ1_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, VI1_CLKENB, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, VI1_CLKENB_B, SEL_VI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, SSI_SDATA7_C, SEL_SSI7_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, SSI_SCK78_B, SEL_SSI7_1),
	PINMUX_IPSR_DATA(IP6_8_6, DACK1),
	PINMUX_IPSR_DATA(IP6_8_6, IRQ1),
	PINMUX_IPSR_DATA(IP6_8_6, INTC_IRQ1_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_8_6, SSI_WS6_B, SEL_SSI6_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_8_6, SSI_SDATA8_C, SEL_SSI8_2),
	PINMUX_IPSR_DATA(IP6_10_9, DREQ2_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_10_9, HSCK1_B, SEL_HSCIF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_10_9, HCTS0_N_B, SEL_HSCIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_10_9, MSIOF0_TXD_B, SEL_SOF0_1),
	PINMUX_IPSR_DATA(IP6_13_11, DACK2),
	PINMUX_IPSR_DATA(IP6_13_11, IRQ2),
	PINMUX_IPSR_DATA(IP6_13_11, INTC_IRQ2_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_13_11, SSI_SDATA6_B, SEL_SSI6_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_13_11, HRTS0_N_B, SEL_HSCIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_13_11, MSIOF0_RXD_B, SEL_SOF0_1),
	PINMUX_IPSR_DATA(IP6_16_14, ETH_CRS_DV),
	PINMUX_IPSR_DATA(IP6_16_14, RMII_CRS_DV),
	PINMUX_IPSR_MODSEL_DATA(IP6_16_14, STP_ISCLK_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_16_14, TS_SDEN0_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP6_16_14, GLO_Q0_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_16_14, SCL2_E, SEL_IIC2_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_16_14, SCL2_CIS_E, SEL_I2C2_4),
	PINMUX_IPSR_DATA(IP6_19_17, ETH_RX_ER),
	PINMUX_IPSR_DATA(IP6_19_17, RMII_RX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, STP_ISD_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, TS_SPSYNC0_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, GLO_Q1_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, SDA2_E, SEL_IIC2_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, SDA2_CIS_E, SEL_I2C2_4),
	PINMUX_IPSR_DATA(IP6_22_20, ETH_RXD0),
	PINMUX_IPSR_DATA(IP6_22_20, RMII_RXD0),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, STP_ISEN_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, TS_SDAT0_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, GLO_I0_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, SCIFB1_SCK_G, SEL_SCIFB1_6),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, SCK1_E, SEL_SCIF1_4),
	PINMUX_IPSR_DATA(IP6_25_23, ETH_RXD1),
	PINMUX_IPSR_DATA(IP6_25_23, RMII_RXD1),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, HRX0_E, SEL_HSCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, STP_ISSYNC_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, TS_SCK0_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, GLO_I1_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, SCIFB1_RXD_G, SEL_SCIFB1_6),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, RX1_E, SEL_SCIF1_4),
	PINMUX_IPSR_DATA(IP6_28_26, ETH_LINK),
	PINMUX_IPSR_DATA(IP6_28_26, RMII_LINK),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, HTX0_E, SEL_HSCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, STP_IVCXO27_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, SCIFB1_TXD_G, SEL_SCIFB1_6),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, TX1_E, SEL_SCIF1_4),
	PINMUX_IPSR_DATA(IP6_31_29, ETH_REF_CLK),
	PINMUX_IPSR_DATA(IP6_31_29, RMII_REF_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, HCTS0_N_E, SEL_HSCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, STP_IVCXO27_1_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, HRX0_F, SEL_HSCIF0_5),

	PINMUX_IPSR_DATA(IP7_2_0, ETH_MDIO),
	PINMUX_IPSR_DATA(IP7_2_0, RMII_MDIO),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, HRTS0_N_E, SEL_HSCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, SIM0_D_C, SEL_SIM_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, HCTS0_N_F, SEL_HSCIF0_5),
	PINMUX_IPSR_DATA(IP7_5_3, ETH_TXD1),
	PINMUX_IPSR_DATA(IP7_5_3, RMII_TXD1),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, HTX0_F, SEL_HSCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, BPFCLK_G, SEL_SIM_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, RDS_CLK_F, SEL_HSCIF0_5),
	PINMUX_IPSR_DATA(IP7_7_6, ETH_TX_EN),
	PINMUX_IPSR_DATA(IP7_7_6, RMII_TX_EN),
	PINMUX_IPSR_MODSEL_DATA(IP7_7_6, SIM0_CLK_C, SEL_SIM_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_7_6, HRTS0_N_F, SEL_HSCIF0_5),
	PINMUX_IPSR_DATA(IP7_9_8, ETH_MAGIC),
	PINMUX_IPSR_DATA(IP7_9_8, RMII_MAGIC),
	PINMUX_IPSR_MODSEL_DATA(IP7_9_8, SIM0_RST_C, SEL_SIM_2),
	PINMUX_IPSR_DATA(IP7_12_10, ETH_TXD0),
	PINMUX_IPSR_DATA(IP7_12_10, RMII_TXD0),
	PINMUX_IPSR_MODSEL_DATA(IP7_12_10, STP_ISCLK_1_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_12_10, TS_SDEN1_C, SEL_TSIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_12_10, GLO_SCLK_C, SEL_GPS_2),
	PINMUX_IPSR_DATA(IP7_15_13, ETH_MDC),
	PINMUX_IPSR_DATA(IP7_15_13, RMII_MDC),
	PINMUX_IPSR_MODSEL_DATA(IP7_15_13, STP_ISD_1_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_15_13, TS_SPSYNC1_C, SEL_TSIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_15_13, GLO_SDATA_C, SEL_GPS_2),
	PINMUX_IPSR_DATA(IP7_18_16, PWM0),
	PINMUX_IPSR_MODSEL_DATA(IP7_18_16, SCIFA2_SCK_C, SEL_SCIFA2_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_18_16, STP_ISEN_1_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_18_16, TS_SDAT1_C, SEL_TSIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_18_16, GLO_SS_C, SEL_GPS_2),
	PINMUX_IPSR_DATA(IP7_21_19, PWM1),
	PINMUX_IPSR_MODSEL_DATA(IP7_21_19, SCIFA2_TXD_C, SEL_SCIFA2_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_21_19, STP_ISSYNC_1_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_21_19, TS_SCK1_C, SEL_TSIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_21_19, GLO_RFON_C, SEL_GPS_2),
	PINMUX_IPSR_DATA(IP7_21_19, PCMOE_N),
	PINMUX_IPSR_DATA(IP7_24_22, PWM2),
	PINMUX_IPSR_DATA(IP7_24_22, PWMFSW0),
	PINMUX_IPSR_MODSEL_DATA(IP7_24_22, SCIFA2_RXD_C, SEL_SCIFA2_2),
	PINMUX_IPSR_DATA(IP7_24_22, PCMWE_N),
	PINMUX_IPSR_MODSEL_DATA(IP7_24_22, IECLK_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP7_26_25, DU1_DOTCLKIN),
	PINMUX_IPSR_DATA(IP7_26_25, AUDIO_CLKC),
	PINMUX_IPSR_DATA(IP7_26_25, AUDIO_CLKOUT_C),
	PINMUX_IPSR_MODSEL_DATA(IP7_28_27, VI0_CLK, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP7_28_27, ATACS00_N),
	PINMUX_IPSR_DATA(IP7_28_27, AVB_RXD1),
	PINMUX_IPSR_DATA(IP7_28_27, MII_RXD1),
	PINMUX_IPSR_MODSEL_DATA(IP7_30_29, VI0_DATA0_VI0_B0, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP7_30_29, ATACS10_N),
	PINMUX_IPSR_DATA(IP7_30_29, AVB_RXD2),
	PINMUX_IPSR_DATA(IP7_30_29, MII_RXD2),

	PINMUX_IPSR_MODSEL_DATA(IP8_1_0, VI0_DATA1_VI0_B1, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_1_0, ATARD0_N),
	PINMUX_IPSR_DATA(IP8_1_0, AVB_RXD3),
	PINMUX_IPSR_DATA(IP8_1_0, MII_RXD3),
	PINMUX_IPSR_MODSEL_DATA(IP8_3_2, VI0_DATA2_VI0_B2, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_3_2, ATAWR0_N),
	PINMUX_IPSR_DATA(IP8_3_2, AVB_RXD4),
	PINMUX_IPSR_MODSEL_DATA(IP8_5_4, VI0_DATA3_VI0_B3, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_5_4, ATADIR0_N),
	PINMUX_IPSR_DATA(IP8_5_4, AVB_RXD5),
	PINMUX_IPSR_MODSEL_DATA(IP8_7_6, VI0_DATA4_VI0_B4, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_7_6, ATAG0_N),
	PINMUX_IPSR_DATA(IP8_7_6, AVB_RXD6),
	PINMUX_IPSR_MODSEL_DATA(IP8_9_8, VI0_DATA5_VI0_B5, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_9_8, EX_WAIT1),
	PINMUX_IPSR_DATA(IP8_9_8, AVB_RXD7),
	PINMUX_IPSR_MODSEL_DATA(IP8_11_10, VI0_DATA6_VI0_B6, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_11_10, AVB_RX_ER),
	PINMUX_IPSR_DATA(IP8_11_10, MII_RX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP8_13_12, VI0_DATA7_VI0_B7, SEL_VI0_0),
	PINMUX_IPSR_DATA(IP8_13_12, AVB_RX_CLK),
	PINMUX_IPSR_DATA(IP8_13_12, MII_RX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP8_15_14, VI1_CLK, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP8_15_14, AVB_RX_DV),
	PINMUX_IPSR_DATA(IP8_15_14, MII_RX_DV),
	PINMUX_IPSR_MODSEL_DATA(IP8_17_16, VI1_DATA0_VI1_B0, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_17_16, SCIFA1_SCK_D, SEL_SCIFA1_3),
	PINMUX_IPSR_DATA(IP8_17_16, AVB_CRS),
	PINMUX_IPSR_DATA(IP8_17_16, MII_CRS),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_18, VI1_DATA1_VI1_B1, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_18, SCIFA1_RXD_D, SEL_SCIFA1_3),
	PINMUX_IPSR_DATA(IP8_19_18, AVB_MDC),
	PINMUX_IPSR_DATA(IP8_19_18, MII_MDC),
	PINMUX_IPSR_MODSEL_DATA(IP8_21_20, VI1_DATA2_VI1_B2, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_21_20, SCIFA1_TXD_D, SEL_SCIFA1_3),
	PINMUX_IPSR_DATA(IP8_21_20, AVB_MDIO),
	PINMUX_IPSR_DATA(IP8_21_20, MII_MDIO),
	PINMUX_IPSR_MODSEL_DATA(IP8_23_22, VI1_DATA3_VI1_B3, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_23_22, SCIFA1_CTS_N_D, SEL_SCIFA1_3),
	PINMUX_IPSR_DATA(IP8_23_22, AVB_GTX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_24, VI1_DATA4_VI1_B4, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_24, SCIFA1_RTS_N_D, SEL_SCIFA1_3),
	PINMUX_IPSR_DATA(IP8_25_24, AVB_MAGIC),
	PINMUX_IPSR_DATA(IP8_25_24, MII_MAGIC),
	PINMUX_IPSR_MODSEL_DATA(IP8_26, VI1_DATA5_VI1_B5, SEL_VI1_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_26, AVB_PHY_INT, SEL_SCIFA1_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_27, VI1_DATA6_VI1_B6, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP8_27, AVB_GTXREFCLK),
	PINMUX_IPSR_DATA(IP8_28, SD0_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP8_28, VI1_DATA0_VI1_B0_B, SEL_VI1_1),
	PINMUX_IPSR_DATA(IP8_30_29, SD0_CMD),
	PINMUX_IPSR_MODSEL_DATA(IP8_30_29, SCIFB1_SCK_B, SEL_SCIFB1_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_30_29, VI1_DATA1_VI1_B1_B, SEL_VI1_1),

	PINMUX_IPSR_DATA(IP9_1_0, SD0_DAT0),
	PINMUX_IPSR_MODSEL_DATA(IP9_1_0, SCIFB1_RXD_B, SEL_SCIFB1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_1_0, VI1_DATA2_VI1_B2_B, SEL_VI1_1),
	PINMUX_IPSR_DATA(IP9_3_2, SD0_DAT1),
	PINMUX_IPSR_MODSEL_DATA(IP9_3_2, SCIFB1_TXD_B, SEL_SCIFB1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_3_2, VI1_DATA3_VI1_B3_B, SEL_VI1_1),
	PINMUX_IPSR_DATA(IP9_5_4, SD0_DAT2),
	PINMUX_IPSR_MODSEL_DATA(IP9_5_4, SCIFB1_CTS_N_B, SEL_SCIFB1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_5_4, VI1_DATA4_VI1_B4_B, SEL_VI1_1),
	PINMUX_IPSR_DATA(IP9_7_6, SD0_DAT3),
	PINMUX_IPSR_MODSEL_DATA(IP9_7_6, SCIFB1_RTS_N_B, SEL_SCIFB1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_7_6, VI1_DATA5_VI1_B5_B, SEL_VI1_1),
	PINMUX_IPSR_DATA(IP9_11_8, SD0_CD),
	PINMUX_IPSR_DATA(IP9_11_8, MMC0_D6),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, TS_SDEN0_B, SEL_TSIF0_1),
	PINMUX_IPSR_DATA(IP9_11_8, USB0_EXTP),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, GLO_SCLK, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, VI1_DATA6_VI1_B6_B, SEL_VI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, SCL1_B, SEL_IIC1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, SCL1_CIS_B, SEL_I2C1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_11_8, VI2_DATA6_VI2_B6_B, SEL_VI2_1),
	PINMUX_IPSR_DATA(IP9_15_12, SD0_WP),
	PINMUX_IPSR_DATA(IP9_15_12, MMC0_D7),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, TS_SPSYNC0_B, SEL_TSIF0_1),
	PINMUX_IPSR_DATA(IP9_15_12, USB0_IDIN),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, GLO_SDATA, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, VI1_DATA7_VI1_B7_B, SEL_VI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, SDA1_B, SEL_IIC1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, SDA1_CIS_B, SEL_I2C1_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_15_12, VI2_DATA7_VI2_B7_B, SEL_VI2_1),
	PINMUX_IPSR_DATA(IP9_17_16, SD1_CLK),
	PINMUX_IPSR_DATA(IP9_17_16, AVB_TX_EN),
	PINMUX_IPSR_DATA(IP9_17_16, MII_TX_EN),
	PINMUX_IPSR_DATA(IP9_19_18, SD1_CMD),
	PINMUX_IPSR_DATA(IP9_19_18, AVB_TX_ER),
	PINMUX_IPSR_DATA(IP9_19_18, MII_TX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP9_19_18, SCIFB0_SCK_B, SEL_SCIFB_1),
	PINMUX_IPSR_DATA(IP9_21_20, SD1_DAT0),
	PINMUX_IPSR_DATA(IP9_21_20, AVB_TX_CLK),
	PINMUX_IPSR_DATA(IP9_21_20, MII_TX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP9_21_20, SCIFB0_RXD_B, SEL_SCIFB_1),
	PINMUX_IPSR_DATA(IP9_23_22, SD1_DAT1),
	PINMUX_IPSR_DATA(IP9_23_22, AVB_LINK),
	PINMUX_IPSR_DATA(IP9_23_22, MII_LINK),
	PINMUX_IPSR_MODSEL_DATA(IP9_23_22, SCIFB0_TXD_B, SEL_SCIFB_1),
	PINMUX_IPSR_DATA(IP9_25_24, SD1_DAT2),
	PINMUX_IPSR_DATA(IP9_25_24, AVB_COL),
	PINMUX_IPSR_DATA(IP9_25_24, MII_COL),
	PINMUX_IPSR_MODSEL_DATA(IP9_25_24, SCIFB0_CTS_N_B, SEL_SCIFB_1),
	PINMUX_IPSR_DATA(IP9_27_26, SD1_DAT3),
	PINMUX_IPSR_DATA(IP9_27_26, AVB_RXD0),
	PINMUX_IPSR_DATA(IP9_27_26, MII_RXD0),
	PINMUX_IPSR_MODSEL_DATA(IP9_27_26, SCIFB0_RTS_N_B, SEL_SCIFB_1),
	PINMUX_IPSR_DATA(IP9_31_28, SD1_CD),
	PINMUX_IPSR_DATA(IP9_31_28, MMC1_D6),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, TS_SDEN1, SEL_TSIF1_0),
	PINMUX_IPSR_DATA(IP9_31_28, USB1_EXTP),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, GLO_SS, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, VI0_CLK_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, SCL2_D, SEL_IIC2_3),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, SCL2_CIS_D, SEL_I2C2_3),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, SIM0_CLK_B, SEL_SIM_1),
	PINMUX_IPSR_MODSEL_DATA(IP9_31_28, VI3_CLK_B, SEL_VI3_1),

	PINMUX_IPSR_DATA(IP10_3_0, SD1_WP),
	PINMUX_IPSR_DATA(IP10_3_0, MMC1_D7),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, TS_SPSYNC1, SEL_TSIF1_0),
	PINMUX_IPSR_DATA(IP10_3_0, USB1_IDIN),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, GLO_RFON, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, VI1_CLK_B, SEL_VI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, SDA2_D, SEL_IIC2_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, SDA2_CIS_D, SEL_I2C2_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_3_0, SIM0_D_B, SEL_SIM_1),
	PINMUX_IPSR_DATA(IP10_6_4, SD2_CLK),
	PINMUX_IPSR_DATA(IP10_6_4, MMC0_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP10_6_4, SIM0_CLK, SEL_SIM_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_6_4, VI0_DATA0_VI0_B0_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_6_4, TS_SDEN0_C, SEL_TSIF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP10_6_4, GLO_SCLK_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_6_4, VI3_DATA0_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_10_7, SD2_CMD),
	PINMUX_IPSR_DATA(IP10_10_7, MMC0_CMD),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, SIM0_D, SEL_SIM_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, VI0_DATA1_VI0_B1_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, SCIFB1_SCK_E, SEL_SCIFB1_4),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, SCK1_D, SEL_SCIF1_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, TS_SPSYNC0_C, SEL_TSIF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, GLO_SDATA_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_10_7, VI3_DATA1_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_14_11, SD2_DAT0),
	PINMUX_IPSR_DATA(IP10_14_11, MMC0_D0),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, FMCLK_B, SEL_FM_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, VI0_DATA2_VI0_B2_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, SCIFB1_RXD_E, SEL_SCIFB1_4),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, RX1_D, SEL_SCIF1_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, TS_SDAT0_C, SEL_TSIF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, GLO_SS_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_14_11, VI3_DATA2_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_18_15, SD2_DAT1),
	PINMUX_IPSR_DATA(IP10_18_15, MMC0_D1),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, FMIN_B, SEL_FM_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, RDS_DATA, SEL_RDS_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, VI0_DATA3_VI0_B3_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, SCIFB1_TXD_E, SEL_SCIFB1_4),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, TX1_D, SEL_SCIF1_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, TS_SCK0_C, SEL_TSIF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, GLO_RFON_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_18_15, VI3_DATA3_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_22_19, SD2_DAT2),
	PINMUX_IPSR_DATA(IP10_22_19, MMC0_D2),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, BPFCLK_B, SEL_FM_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, RDS_CLK, SEL_RDS_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, VI0_DATA4_VI0_B4_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, HRX0_D, SEL_HSCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, TS_SDEN1_B, SEL_TSIF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, GLO_Q0_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_22_19, VI3_DATA4_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_25_23, SD2_DAT3),
	PINMUX_IPSR_DATA(IP10_25_23, MMC0_D3),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, SIM0_RST, SEL_SIM_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, VI0_DATA5_VI0_B5_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, HTX0_D, SEL_HSCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, TS_SPSYNC1_B, SEL_TSIF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, GLO_Q1_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_25_23, VI3_DATA5_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP10_29_26, SD2_CD),
	PINMUX_IPSR_DATA(IP10_29_26, MMC0_D4),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, TS_SDAT0_B, SEL_TSIF0_1),
	PINMUX_IPSR_DATA(IP10_29_26, USB2_EXTP),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, GLO_I0, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, VI0_DATA6_VI0_B6_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, HCTS0_N_D, SEL_HSCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, TS_SDAT1_B, SEL_TSIF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, GLO_I0_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP10_29_26, VI3_DATA6_B, SEL_VI3_1),

	PINMUX_IPSR_DATA(IP11_3_0, SD2_WP),
	PINMUX_IPSR_DATA(IP11_3_0, MMC0_D5),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, TS_SCK0_B, SEL_TSIF0_1),
	PINMUX_IPSR_DATA(IP11_3_0, USB2_IDIN),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, GLO_I1, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, VI0_DATA7_VI0_B7_B, SEL_VI0_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, HRTS0_N_D, SEL_HSCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, TS_SCK1_B, SEL_TSIF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, GLO_I1_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_3_0, VI3_DATA7_B, SEL_VI3_1),
	PINMUX_IPSR_DATA(IP11_4, SD3_CLK),
	PINMUX_IPSR_DATA(IP11_4, MMC1_CLK),
	PINMUX_IPSR_DATA(IP11_6_5, SD3_CMD),
	PINMUX_IPSR_DATA(IP11_6_5, MMC1_CMD),
	PINMUX_IPSR_DATA(IP11_6_5, MTS_N),
	PINMUX_IPSR_DATA(IP11_8_7, SD3_DAT0),
	PINMUX_IPSR_DATA(IP11_8_7, MMC1_D0),
	PINMUX_IPSR_DATA(IP11_8_7, STM_N),
	PINMUX_IPSR_DATA(IP11_10_9, SD3_DAT1),
	PINMUX_IPSR_DATA(IP11_10_9, MMC1_D1),
	PINMUX_IPSR_DATA(IP11_10_9, MDATA),
	PINMUX_IPSR_DATA(IP11_12_11, SD3_DAT2),
	PINMUX_IPSR_DATA(IP11_12_11, MMC1_D2),
	PINMUX_IPSR_DATA(IP11_12_11, SDATA),
	PINMUX_IPSR_DATA(IP11_14_13, SD3_DAT3),
	PINMUX_IPSR_DATA(IP11_14_13, MMC1_D3),
	PINMUX_IPSR_DATA(IP11_14_13, SCKZ),
	PINMUX_IPSR_DATA(IP11_17_15, SD3_CD),
	PINMUX_IPSR_DATA(IP11_17_15, MMC1_D4),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_15, TS_SDAT1, SEL_TSIF1_0),
	PINMUX_IPSR_DATA(IP11_17_15, VSP),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_15, GLO_Q0, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_15, SIM0_RST_B, SEL_SIM_1),
	PINMUX_IPSR_DATA(IP11_21_18, SD3_WP),
	PINMUX_IPSR_DATA(IP11_21_18, MMC1_D5),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, TS_SCK1, SEL_TSIF1_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, GLO_Q1, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, FMIN_C, SEL_FM_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, RDS_DATA_B, SEL_RDS_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, FMIN_E, SEL_FM_4),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, RDS_DATA_D, SEL_RDS_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, FMIN_F, SEL_FM_5),
	PINMUX_IPSR_MODSEL_DATA(IP11_21_18, RDS_DATA_E, SEL_RDS_4),
	PINMUX_IPSR_DATA(IP11_23_22, MLB_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_22, SCL2_B, SEL_IIC2_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_22, SCL2_CIS_B, SEL_I2C2_1),
	PINMUX_IPSR_DATA(IP11_26_24, MLB_SIG),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, SCIFB1_RXD_D, SEL_SCIFB1_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, RX1_C, SEL_SCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, SDA2_B, SEL_IIC2_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, SDA2_CIS_B, SEL_I2C2_1),
	PINMUX_IPSR_DATA(IP11_29_27, MLB_DAT),
	PINMUX_IPSR_DATA(IP11_29_27, SPV_EVEN),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, SCIFB1_TXD_D, SEL_SCIFB1_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, TX1_C, SEL_SCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, BPFCLK_C, SEL_FM_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, RDS_CLK_B, SEL_RDS_1),
	PINMUX_IPSR_DATA(IP11_31_30, SSI_SCK0129),
	PINMUX_IPSR_MODSEL_DATA(IP11_31_30, CAN_CLK_B, SEL_CANCLK_1),
	PINMUX_IPSR_DATA(IP11_31_30, MOUT0),

};

static struct pinmux_gpio pinmux_gpios[] = {
	PINMUX_GPIO_GP_ALL(),

	GPIO_FN(VI1_DATA7_VI1_B7), GPIO_FN(USB0_PWEN), GPIO_FN(USB0_OVC_VBUS),
	GPIO_FN(USB2_PWEN), GPIO_FN(USB2_OVC), GPIO_FN(AVS1), GPIO_FN(AVS2),
	GPIO_FN(DU_DOTCLKIN0), GPIO_FN(DU_DOTCLKIN2),

	/* IPSR0 - IPSR5 */
	/*IPSR6*/
	GPIO_FN(DACK0), GPIO_FN(IRQ0), GPIO_FN(INTC_IRQ0_N),
	GPIO_FN(SSI_SCK6_B), GPIO_FN(VI1_VSYNC_N), GPIO_FN(VI1_VSYNC_N_B),
	GPIO_FN(SSI_WS78_C), GPIO_FN(DREQ1_N), GPIO_FN(VI1_CLKENB),
	GPIO_FN(VI1_CLKENB_B), GPIO_FN(SSI_SDATA7_C), GPIO_FN(SSI_SCK78_B),
	GPIO_FN(DACK1), GPIO_FN(IRQ1), GPIO_FN(INTC_IRQ1_N), GPIO_FN(SSI_WS6_B),
	GPIO_FN(SSI_SDATA8_C), GPIO_FN(DREQ2_N), GPIO_FN(HSCK1_B),
	GPIO_FN(HCTS0_N_B), GPIO_FN(MSIOF0_TXD_B), GPIO_FN(DACK2),
	GPIO_FN(IRQ2), GPIO_FN(INTC_IRQ2_N), GPIO_FN(SSI_SDATA6_B),
	GPIO_FN(HRTS0_N_B), GPIO_FN(MSIOF0_RXD_B), GPIO_FN(ETH_CRS_DV),
	GPIO_FN(RMII_CRS_DV), GPIO_FN(STP_ISCLK_0_B), GPIO_FN(TS_SDEN0_D),
	GPIO_FN(GLO_Q0_C), GPIO_FN(SCL2_E), GPIO_FN(SCL2_CIS_E),
	GPIO_FN(ETH_RX_ER), GPIO_FN(RMII_RX_ER), GPIO_FN(STP_ISD_0_B),
	GPIO_FN(TS_SPSYNC0_D), GPIO_FN(GLO_Q1_C), GPIO_FN(SDA2_E),
	GPIO_FN(SDA2_CIS_E), GPIO_FN(ETH_RXD0), GPIO_FN(RMII_RXD0),
	GPIO_FN(STP_ISEN_0_B), GPIO_FN(TS_SDAT0_D), GPIO_FN(GLO_I0_C),
	GPIO_FN(SCIFB1_SCK_G), GPIO_FN(SCK1_E), GPIO_FN(ETH_RXD1),
	GPIO_FN(RMII_RXD1), GPIO_FN(HRX0_E), GPIO_FN(STP_ISSYNC_0_B),
	GPIO_FN(TS_SCK0_D), GPIO_FN(GLO_I1_C), GPIO_FN(SCIFB1_RXD_G),
	GPIO_FN(RX1_E), GPIO_FN(ETH_LINK), GPIO_FN(RMII_LINK), GPIO_FN(HTX0_E),
	GPIO_FN(STP_IVCXO27_0_B), GPIO_FN(SCIFB1_TXD_G), GPIO_FN(TX1_E),
	GPIO_FN(ETH_REF_CLK), GPIO_FN(RMII_REF_CLK), GPIO_FN(HCTS0_N_E),
	GPIO_FN(STP_IVCXO27_1_B), GPIO_FN(HRX0_F),

	/*IPSR7*/
	GPIO_FN(ETH_MDIO), GPIO_FN(RMII_MDIO), GPIO_FN(HRTS0_N_E),
	GPIO_FN(SIM0_D_C), GPIO_FN(HCTS0_N_F), GPIO_FN(ETH_TXD1),
	GPIO_FN(RMII_TXD1), GPIO_FN(HTX0_F), GPIO_FN(BPFCLK_G),
	GPIO_FN(RDS_CLK_F), GPIO_FN(ETH_TX_EN), GPIO_FN(RMII_TX_EN),
	GPIO_FN(SIM0_CLK_C), GPIO_FN(HRTS0_N_F), GPIO_FN(ETH_MAGIC),
	GPIO_FN(RMII_MAGIC), GPIO_FN(SIM0_RST_C), GPIO_FN(ETH_TXD0),
	GPIO_FN(RMII_TXD0), GPIO_FN(STP_ISCLK_1_B), GPIO_FN(TS_SDEN1_C),
	GPIO_FN(GLO_SCLK_C), GPIO_FN(ETH_MDC), GPIO_FN(RMII_MDC),
	GPIO_FN(STP_ISD_1_B), GPIO_FN(TS_SPSYNC1_C), GPIO_FN(GLO_SDATA_C),
	GPIO_FN(PWM0), GPIO_FN(SCIFA2_SCK_C), GPIO_FN(STP_ISEN_1_B),
	GPIO_FN(TS_SDAT1_C), GPIO_FN(GLO_SS_C), GPIO_FN(PWM1),
	GPIO_FN(SCIFA2_TXD_C), GPIO_FN(STP_ISSYNC_1_B), GPIO_FN(TS_SCK1_C),
	GPIO_FN(GLO_RFON_C), GPIO_FN(PCMOE_N), GPIO_FN(PWM2), GPIO_FN(PWMFSW0),
	GPIO_FN(SCIFA2_RXD_C), GPIO_FN(PCMWE_N), GPIO_FN(IECLK_C),
	GPIO_FN(DU1_DOTCLKIN), GPIO_FN(AUDIO_CLKC), GPIO_FN(AUDIO_CLKOUT_C),
	GPIO_FN(VI0_CLK), GPIO_FN(ATACS00_N), GPIO_FN(AVB_RXD1),
	GPIO_FN(MII_RXD1), GPIO_FN(VI0_DATA0_VI0_B0), GPIO_FN(ATACS10_N),
	GPIO_FN(AVB_RXD2), GPIO_FN(MII_RXD2),

	/*IPSR8*/
	GPIO_FN(VI0_DATA1_VI0_B1), GPIO_FN(ATARD0_N), GPIO_FN(AVB_RXD3),
	GPIO_FN(MII_RXD3), GPIO_FN(VI0_DATA2_VI0_B2), GPIO_FN(ATAWR0_N),
	GPIO_FN(AVB_RXD4), GPIO_FN(VI0_DATA3_VI0_B3), GPIO_FN(ATADIR0_N),
	GPIO_FN(AVB_RXD5), GPIO_FN(VI0_DATA4_VI0_B4), GPIO_FN(ATAG0_N),
	GPIO_FN(AVB_RXD6), GPIO_FN(VI0_DATA5_VI0_B5), GPIO_FN(EX_WAIT1),
	GPIO_FN(AVB_RXD7), GPIO_FN(VI0_DATA6_VI0_B6), GPIO_FN(AVB_RX_ER),
	GPIO_FN(MII_RX_ER), GPIO_FN(VI0_DATA7_VI0_B7), GPIO_FN(AVB_RX_CLK),
	GPIO_FN(MII_RX_CLK), GPIO_FN(VI1_CLK), GPIO_FN(AVB_RX_DV),
	GPIO_FN(MII_RX_DV), GPIO_FN(VI1_DATA0_VI1_B0), GPIO_FN(SCIFA1_SCK_D),
	GPIO_FN(AVB_CRS), GPIO_FN(MII_CRS), GPIO_FN(VI1_DATA1_VI1_B1),
	GPIO_FN(SCIFA1_RXD_D), GPIO_FN(AVB_MDC), GPIO_FN(MII_MDC),
	GPIO_FN(VI1_DATA2_VI1_B2), GPIO_FN(SCIFA1_TXD_D), GPIO_FN(AVB_MDIO),
	GPIO_FN(MII_MDIO), GPIO_FN(VI1_DATA3_VI1_B3), GPIO_FN(SCIFA1_CTS_N_D),
	GPIO_FN(AVB_GTX_CLK), GPIO_FN(VI1_DATA4_VI1_B4),
	GPIO_FN(SCIFA1_RTS_N_D), GPIO_FN(AVB_MAGIC), GPIO_FN(MII_MAGIC),
	GPIO_FN(VI1_DATA5_VI1_B5), GPIO_FN(AVB_PHY_INT),
	GPIO_FN(VI1_DATA6_VI1_B6), GPIO_FN(AVB_GTXREFCLK),
	GPIO_FN(SD0_CLK), GPIO_FN(VI1_DATA0_VI1_B0_B), GPIO_FN(SD0_CMD),
	GPIO_FN(SCIFB1_SCK_B), GPIO_FN(VI1_DATA1_VI1_B1_B),

	/*IPSR9*/
	GPIO_FN(SD0_DAT0), GPIO_FN(SCIFB1_RXD_B), GPIO_FN(VI1_DATA2_VI1_B2_B),
	GPIO_FN(SD0_DAT1), GPIO_FN(SCIFB1_TXD_B), GPIO_FN(VI1_DATA3_VI1_B3_B),
	GPIO_FN(SD0_DAT2), GPIO_FN(SCIFB1_CTS_N_B), GPIO_FN(VI1_DATA4_VI1_B4_B),
	GPIO_FN(SD0_DAT3), GPIO_FN(SCIFB1_RTS_N_B), GPIO_FN(VI1_DATA5_VI1_B5_B),
	GPIO_FN(SD0_CD), GPIO_FN(MMC0_D6), GPIO_FN(TS_SDEN0_B),
	GPIO_FN(USB0_EXTP), GPIO_FN(GLO_SCLK), GPIO_FN(VI1_DATA6_VI1_B6_B),
	GPIO_FN(SCL1_B), GPIO_FN(SCL1_CIS_B), GPIO_FN(VI2_DATA6_VI2_B6_B),
	GPIO_FN(SD0_WP), GPIO_FN(MMC0_D7), GPIO_FN(TS_SPSYNC0_B),
	GPIO_FN(USB0_IDIN), GPIO_FN(GLO_SDATA), GPIO_FN(VI1_DATA7_VI1_B7_B),
	GPIO_FN(SDA1_B), GPIO_FN(SDA1_CIS_B), GPIO_FN(VI2_DATA7_VI2_B7_B),
	GPIO_FN(SD1_CLK), GPIO_FN(AVB_TX_EN), GPIO_FN(MII_TX_EN),
	GPIO_FN(SD1_CMD), GPIO_FN(AVB_TX_ER), GPIO_FN(MII_TX_ER),
	GPIO_FN(SCIFB0_SCK_B), GPIO_FN(SD1_DAT0), GPIO_FN(AVB_TX_CLK),
	GPIO_FN(MII_TX_CLK), GPIO_FN(SCIFB0_RXD_B), GPIO_FN(SD1_DAT1),
	GPIO_FN(AVB_LINK), GPIO_FN(MII_LINK), GPIO_FN(SCIFB0_TXD_B),
	GPIO_FN(SD1_DAT2), GPIO_FN(AVB_COL), GPIO_FN(MII_COL),
	GPIO_FN(SCIFB0_CTS_N_B), GPIO_FN(SD1_DAT3), GPIO_FN(AVB_RXD0),
	GPIO_FN(MII_RXD0), GPIO_FN(SCIFB0_RTS_N_B), GPIO_FN(SD1_CD),
	GPIO_FN(MMC1_D6), GPIO_FN(TS_SDEN1), GPIO_FN(USB1_EXTP),
	GPIO_FN(GLO_SS), GPIO_FN(VI0_CLK_B), GPIO_FN(SCL2_D),
	GPIO_FN(SCL2_CIS_D), GPIO_FN(SIM0_CLK_B), GPIO_FN(VI3_CLK_B),

	/*IPSR10*/
	GPIO_FN(SD1_WP), GPIO_FN(MMC1_D7), GPIO_FN(TS_SPSYNC1),
	GPIO_FN(USB1_IDIN), GPIO_FN(GLO_RFON), GPIO_FN(VI1_CLK_B),
	GPIO_FN(SDA2_D), GPIO_FN(SDA2_CIS_D), GPIO_FN(SIM0_D_B),
	GPIO_FN(SD2_CLK), GPIO_FN(MMC0_CLK), GPIO_FN(SIM0_CLK),
	GPIO_FN(VI0_DATA0_VI0_B0_B), GPIO_FN(TS_SDEN0_C), GPIO_FN(GLO_SCLK_B),
	GPIO_FN(VI3_DATA0_B), GPIO_FN(SD2_CMD), GPIO_FN(MMC0_CMD),
	GPIO_FN(SIM0_D), GPIO_FN(VI0_DATA1_VI0_B1_B), GPIO_FN(SCIFB1_SCK_E),
	GPIO_FN(SCK1_D), GPIO_FN(TS_SPSYNC0_C), GPIO_FN(GLO_SDATA_B),
	GPIO_FN(VI3_DATA1_B), GPIO_FN(SD2_DAT0), GPIO_FN(MMC0_D0),
	GPIO_FN(FMCLK_B), GPIO_FN(VI0_DATA2_VI0_B2_B), GPIO_FN(SCIFB1_RXD_E),
	GPIO_FN(RX1_D), GPIO_FN(TS_SDAT0_C), GPIO_FN(GLO_SS_B),
	GPIO_FN(VI3_DATA2_B), GPIO_FN(SD2_DAT1), GPIO_FN(MMC0_D1),
	GPIO_FN(FMIN_B), GPIO_FN(RDS_DATA), GPIO_FN(VI0_DATA3_VI0_B3_B),
	GPIO_FN(SCIFB1_TXD_E), GPIO_FN(TX1_D), GPIO_FN(TS_SCK0_C),
	GPIO_FN(GLO_RFON_B), GPIO_FN(VI3_DATA3_B), GPIO_FN(SD2_DAT2),
	GPIO_FN(MMC0_D2), GPIO_FN(BPFCLK_B), GPIO_FN(RDS_CLK),
	GPIO_FN(VI0_DATA4_VI0_B4_B), GPIO_FN(HRX0_D), GPIO_FN(TS_SDEN1_B),
	GPIO_FN(GLO_Q0_B), GPIO_FN(VI3_DATA4_B), GPIO_FN(SD2_DAT3),
	GPIO_FN(MMC0_D3), GPIO_FN(SIM0_RST), GPIO_FN(VI0_DATA5_VI0_B5_B),
	GPIO_FN(HTX0_D), GPIO_FN(TS_SPSYNC1_B), GPIO_FN(GLO_Q1_B),
	GPIO_FN(VI3_DATA5_B), GPIO_FN(SD2_CD), GPIO_FN(MMC0_D4),
	GPIO_FN(TS_SDAT0_B), GPIO_FN(USB2_EXTP), GPIO_FN(GLO_I0),
	GPIO_FN(VI0_DATA6_VI0_B6_B), GPIO_FN(HCTS0_N_D), GPIO_FN(TS_SDAT1_B),
	GPIO_FN(GLO_I0_B), GPIO_FN(VI3_DATA6_B),

	/*IPSR11*/
	GPIO_FN(SD2_WP), GPIO_FN(MMC0_D5), GPIO_FN(TS_SCK0_B),
	GPIO_FN(USB2_IDIN), GPIO_FN(GLO_I1), GPIO_FN(VI0_DATA7_VI0_B7_B),
	GPIO_FN(HRTS0_N_D), GPIO_FN(TS_SCK1_B), GPIO_FN(GLO_I1_B),
	GPIO_FN(VI3_DATA7_B), GPIO_FN(SD3_CLK), GPIO_FN(MMC1_CLK),
	GPIO_FN(SD3_CMD), GPIO_FN(MMC1_CMD), GPIO_FN(MTS_N), GPIO_FN(SD3_DAT0),
	GPIO_FN(MMC1_D0), GPIO_FN(STM_N), GPIO_FN(SD3_DAT1), GPIO_FN(MMC1_D1),
	GPIO_FN(MDATA), GPIO_FN(SD3_DAT2), GPIO_FN(MMC1_D2), GPIO_FN(SDATA),
	GPIO_FN(SD3_DAT3), GPIO_FN(MMC1_D3), GPIO_FN(SCKZ), GPIO_FN(SD3_CD),
	GPIO_FN(MMC1_D4), GPIO_FN(TS_SDAT1), GPIO_FN(VSP), GPIO_FN(GLO_Q0),
	GPIO_FN(SIM0_RST_B), GPIO_FN(SD3_WP), GPIO_FN(MMC1_D5),
	GPIO_FN(TS_SCK1), GPIO_FN(GLO_Q1), GPIO_FN(FMIN_C), GPIO_FN(RDS_DATA_B),
	GPIO_FN(FMIN_E), GPIO_FN(RDS_DATA_D), GPIO_FN(FMIN_F),
	GPIO_FN(RDS_DATA_E), GPIO_FN(MLB_CLK), GPIO_FN(SCL2_B),
	GPIO_FN(SCL2_CIS_B), GPIO_FN(MLB_SIG), GPIO_FN(SCIFB1_RXD_D),
	GPIO_FN(RX1_C), GPIO_FN(SDA2_B), GPIO_FN(SDA2_CIS_B), GPIO_FN(MLB_DAT),
	GPIO_FN(SPV_EVEN), GPIO_FN(SCIFB1_TXD_D), GPIO_FN(TX1_C),
	GPIO_FN(BPFCLK_C), GPIO_FN(RDS_CLK_B), GPIO_FN(SSI_SCK0129),
	GPIO_FN(CAN_CLK_B), GPIO_FN(MOUT0),

};

static struct pinmux_cfg_reg pinmux_config_regs[] = {
	{ PINMUX_CFG_REG("GPSR0", 0xE6060004, 32, 1) {
		GP_0_31_FN, FN_IP3_17_15,
		GP_0_30_FN, FN_IP3_14_12,
		GP_0_29_FN, FN_IP3_11_8,
		GP_0_28_FN, FN_IP3_7_4,
		GP_0_27_FN, FN_IP3_3_0,
		GP_0_26_FN, FN_IP2_28_26,
		GP_0_25_FN, FN_IP2_25_22,
		GP_0_24_FN, FN_IP2_21_18,
		GP_0_23_FN, FN_IP2_17_15,
		GP_0_22_FN, FN_IP2_14_12,
		GP_0_21_FN, FN_IP2_11_9,
		GP_0_20_FN, FN_IP2_8_6,
		GP_0_19_FN, FN_IP2_5_3,
		GP_0_18_FN, FN_IP2_2_0,
		GP_0_17_FN, FN_IP1_29_28,
		GP_0_16_FN, FN_IP1_27_26,
		GP_0_15_FN, FN_IP1_25_22,
		GP_0_14_FN, FN_IP1_21_18,
		GP_0_13_FN, FN_IP1_17_15,
		GP_0_12_FN, FN_IP1_14_12,
		GP_0_11_FN, FN_IP1_11_8,
		GP_0_10_FN, FN_IP1_7_4,
		GP_0_9_FN, FN_IP1_3_0,
		GP_0_8_FN, FN_IP0_30_27,
		GP_0_7_FN, FN_IP0_26_23,
		GP_0_6_FN, FN_IP0_22_20,
		GP_0_5_FN, FN_IP0_19_16,
		GP_0_4_FN, FN_IP0_15_12,
		GP_0_3_FN, FN_IP0_11_9,
		GP_0_2_FN, FN_IP0_8_6,
		GP_0_1_FN, FN_IP0_5_3,
		GP_0_0_FN, FN_IP0_2_0 }
	},
	{ PINMUX_CFG_REG("GPSR1", 0xE6060008, 32, 1) {
		0, 0,
		0, 0,
		GP_1_29_FN, FN_IP6_13_11,
		GP_1_28_FN, FN_IP6_10_9,
		GP_1_27_FN, FN_IP6_8_6,
		GP_1_26_FN, FN_IP6_5_3,
		GP_1_25_FN, FN_IP6_2_0,
		GP_1_24_FN, FN_IP5_29_27,
		GP_1_23_FN, FN_IP5_26_24,
		GP_1_22_FN, FN_IP5_23_21,
		GP_1_21_FN, FN_IP5_20_18,
		GP_1_20_FN, FN_IP5_17_15,
		GP_1_19_FN, FN_IP5_14_13,
		GP_1_18_FN, FN_IP5_12_10,
		GP_1_17_FN, FN_IP5_9_6,
		GP_1_16_FN, FN_IP5_5_3,
		GP_1_15_FN, FN_IP5_2_0,
		GP_1_14_FN, FN_IP4_29_27,
		GP_1_13_FN, FN_IP4_26_24,
		GP_1_12_FN, FN_IP4_23_21,
		GP_1_11_FN, FN_IP4_20_18,
		GP_1_10_FN, FN_IP4_17_15,
		GP_1_9_FN, FN_IP4_14_12,
		GP_1_8_FN, FN_IP4_11_9,
		GP_1_7_FN, FN_IP4_8_6,
		GP_1_6_FN, FN_IP4_5_3,
		GP_1_5_FN, FN_IP4_2_0,
		GP_1_4_FN, FN_IP3_31_29,
		GP_1_3_FN, FN_IP3_28_26,
		GP_1_2_FN, FN_IP3_25_23,
		GP_1_1_FN, FN_IP3_22_20,
		GP_1_0_FN, FN_IP3_19_18, }
	},
	{ PINMUX_CFG_REG("GPSR2", 0xE606000C, 32, 1) {
		0, 0,
		0, 0,
		GP_2_29_FN, FN_IP7_15_13,
		GP_2_28_FN, FN_IP7_12_10,
		GP_2_27_FN, FN_IP7_9_8,
		GP_2_26_FN, FN_IP7_7_6,
		GP_2_25_FN, FN_IP7_5_3,
		GP_2_24_FN, FN_IP7_2_0,
		GP_2_23_FN, FN_IP6_31_29,
		GP_2_22_FN, FN_IP6_28_26,
		GP_2_21_FN, FN_IP6_25_23,
		GP_2_20_FN, FN_IP6_22_20,
		GP_2_19_FN, FN_IP6_19_17,
		GP_2_18_FN, FN_IP6_16_14,
		GP_2_17_FN, FN_VI1_DATA7_VI1_B7,
		GP_2_16_FN, FN_IP8_27,
		GP_2_15_FN, FN_IP8_26,
		GP_2_14_FN, FN_IP8_25_24,
		GP_2_13_FN, FN_IP8_23_22,
		GP_2_12_FN, FN_IP8_21_20,
		GP_2_11_FN, FN_IP8_19_18,
		GP_2_10_FN, FN_IP8_17_16,
		GP_2_9_FN, FN_IP8_15_14,
		GP_2_8_FN, FN_IP8_13_12,
		GP_2_7_FN, FN_IP8_11_10,
		GP_2_6_FN, FN_IP8_9_8,
		GP_2_5_FN, FN_IP8_7_6,
		GP_2_4_FN, FN_IP8_5_4,
		GP_2_3_FN, FN_IP8_3_2,
		GP_2_2_FN, FN_IP8_1_0,
		GP_2_1_FN, FN_IP7_30_29,
		GP_2_0_FN, FN_IP7_28_27 }
	},
	{ PINMUX_CFG_REG("GPSR3", 0xE6060010, 32, 1) {
		GP_3_31_FN, FN_IP11_21_18,
		GP_3_30_FN, FN_IP11_17_15,
		GP_3_29_FN, FN_IP11_14_13,
		GP_3_28_FN, FN_IP11_12_11,
		GP_3_27_FN, FN_IP11_10_9,
		GP_3_26_FN, FN_IP11_8_7,
		GP_3_25_FN, FN_IP11_6_5,
		GP_3_24_FN, FN_IP11_4,
		GP_3_23_FN, FN_IP11_3_0,
		GP_3_22_FN, FN_IP10_29_26,
		GP_3_21_FN, FN_IP10_25_23,
		GP_3_20_FN, FN_IP10_22_19,
		GP_3_19_FN, FN_IP10_18_15,
		GP_3_18_FN, FN_IP10_14_11,
		GP_3_17_FN, FN_IP10_10_7,
		GP_3_16_FN, FN_IP10_6_4,
		GP_3_15_FN, FN_IP10_3_0,
		GP_3_14_FN, FN_IP9_31_28,
		GP_3_13_FN, FN_IP9_27_26,
		GP_3_12_FN, FN_IP9_25_24,
		GP_3_11_FN, FN_IP9_23_22,
		GP_3_10_FN, FN_IP9_21_20,
		GP_3_9_FN, FN_IP9_19_18,
		GP_3_8_FN, FN_IP9_17_16,
		GP_3_7_FN, FN_IP9_15_12,
		GP_3_6_FN, FN_IP9_11_8,
		GP_3_5_FN, FN_IP9_7_6,
		GP_3_4_FN, FN_IP9_5_4,
		GP_3_3_FN, FN_IP9_3_2,
		GP_3_2_FN, FN_IP9_1_0,
		GP_3_1_FN, FN_IP8_30_29,
		GP_3_0_FN, FN_IP8_28 }
	},
	{ PINMUX_CFG_REG("GPSR4", 0xE6060014, 32, 1) {
		GP_4_31_FN, FN_IP14_18_16,
		GP_4_30_FN, FN_IP14_15_12,
		GP_4_29_FN, FN_IP14_11_9,
		GP_4_28_FN, FN_IP14_8_6,
		GP_4_27_FN, FN_IP14_5_3,
		GP_4_26_FN, FN_IP14_2_0,
		GP_4_25_FN, FN_IP13_30_29,
		GP_4_24_FN, FN_IP13_28_26,
		GP_4_23_FN, FN_IP13_25_23,
		GP_4_22_FN, FN_IP13_22_19,
		GP_4_21_FN, FN_IP13_18_16,
		GP_4_20_FN, FN_IP13_15_13,
		GP_4_19_FN, FN_IP13_12_10,
		GP_4_18_FN, FN_IP13_9_7,
		GP_4_17_FN, FN_IP13_6_3,
		GP_4_16_FN, FN_IP13_2_0,
		GP_4_15_FN, FN_IP12_30_28,
		GP_4_14_FN, FN_IP12_27_25,
		GP_4_13_FN, FN_IP12_24_23,
		GP_4_12_FN, FN_IP12_22_20,
		GP_4_11_FN, FN_IP12_19_17,
		GP_4_10_FN, FN_IP12_16_14,
		GP_4_9_FN, FN_IP12_13_11,
		GP_4_8_FN, FN_IP12_10_8,
		GP_4_7_FN, FN_IP12_7_6,
		GP_4_6_FN, FN_IP12_5_4,
		GP_4_5_FN, FN_IP12_3_2,
		GP_4_4_FN, FN_IP12_1_0,
		GP_4_3_FN, FN_IP11_31_30,
		GP_4_2_FN, FN_IP11_29_27,
		GP_4_1_FN, FN_IP11_26_24,
		GP_4_0_FN, FN_IP11_23_22 }
	},
	{ PINMUX_CFG_REG("GPSR5", 0xE6060018, 32, 1) {
		GP_5_31_FN, FN_IP7_24_22,
		GP_5_30_FN, FN_IP7_21_19,
		GP_5_29_FN, FN_IP7_18_16,
		GP_5_28_FN, FN_DU_DOTCLKIN2,
		GP_5_27_FN, FN_IP7_26_25,
		GP_5_26_FN, FN_DU_DOTCLKIN0,
		GP_5_25_FN, FN_AVS2,
		GP_5_24_FN, FN_AVS1,
		GP_5_23_FN, FN_USB2_OVC,
		GP_5_22_FN, FN_USB2_PWEN,
		GP_5_21_FN, FN_IP16_7,
		GP_5_20_FN, FN_IP16_6,
		GP_5_19_FN, FN_USB0_OVC_VBUS,
		GP_5_18_FN, FN_USB0_PWEN,
		GP_5_17_FN, FN_IP16_5_3,
		GP_5_16_FN, FN_IP16_2_0,
		GP_5_15_FN, FN_IP15_29_28,
		GP_5_14_FN, FN_IP15_27_26,
		GP_5_13_FN, FN_IP15_25_23,
		GP_5_12_FN, FN_IP15_22_20,
		GP_5_11_FN, FN_IP15_19_18,
		GP_5_10_FN, FN_IP15_17_16,
		GP_5_9_FN, FN_IP15_15_14,
		GP_5_8_FN, FN_IP15_13_12,
		GP_5_7_FN, FN_IP15_11_9,
		GP_5_6_FN, FN_IP15_8_6,
		GP_5_5_FN, FN_IP15_5_3,
		GP_5_4_FN, FN_IP15_2_0,
		GP_5_3_FN, FN_IP14_30_28,
		GP_5_2_FN, FN_IP14_27_25,
		GP_5_1_FN, FN_IP14_24_22,
		GP_5_0_FN, FN_IP14_21_19 }
	},
	/* IPSR0 - IPSR5 */
	{ PINMUX_CFG_REG_VAR("IPSR6", 0xE6060038, 32,
			     3, 3, 3, 3, 3, 3, 3, 2, 3, 3, 3) {
		/* IP6_31_29 [3] */
		FN_ETH_REF_CLK, FN_RMII_REF_CLK, FN_HCTS0_N_E,
		FN_STP_IVCXO27_1_B, FN_HRX0_F, 0, 0, 0,
		/* IP6_28_26 [3] */
		FN_ETH_LINK, FN_RMII_LINK, FN_HTX0_E,
		FN_STP_IVCXO27_0_B, FN_SCIFB1_TXD_G, FN_TX1_E, 0, 0,
		/* IP6_25_23 [3] */
		FN_ETH_RXD1, FN_RMII_RXD1, FN_HRX0_E, FN_STP_ISSYNC_0_B,
		FN_TS_SCK0_D, FN_GLO_I1_C, FN_SCIFB1_RXD_G, FN_RX1_E,
		/* IP6_22_20 [3] */
		FN_ETH_RXD0, FN_RMII_RXD0, FN_STP_ISEN_0_B, FN_TS_SDAT0_D,
		FN_GLO_I0_C, FN_SCIFB1_SCK_G, FN_SCK1_E, 0,
		/* IP6_19_17 [3] */
		FN_ETH_RX_ER, FN_RMII_RX_ER, FN_STP_ISD_0_B,
		FN_TS_SPSYNC0_D, FN_GLO_Q1_C, FN_SDA2_E, FN_SDA2_CIS_E, 0,
		/* IP6_16_14 [3] */
		FN_ETH_CRS_DV, FN_RMII_CRS_DV, FN_STP_ISCLK_0_B,
		FN_TS_SDEN0_D, FN_GLO_Q0_C, FN_SCL2_E,
		FN_SCL2_CIS_E, 0,
		/* IP6_13_11 [3] */
		FN_DACK2, FN_IRQ2, FN_INTC_IRQ2_N,
		FN_SSI_SDATA6_B, FN_HRTS0_N_B, FN_MSIOF0_RXD_B, 0, 0,
		/* IP6_10_9 [2] */
		FN_DREQ2_N, FN_HSCK1_B, FN_HCTS0_N_B, FN_MSIOF0_TXD_B,
		/* IP6_8_6 [3] */
		FN_DACK1, FN_IRQ1, FN_INTC_IRQ1_N, FN_SSI_WS6_B,
		FN_SSI_SDATA8_C, 0, 0, 0,
		/* IP6_5_3 [3] */
		FN_DREQ1_N, FN_VI1_CLKENB, FN_VI1_CLKENB_B,
		FN_SSI_SDATA7_C, FN_SSI_SCK78_B, 0, 0, 0,
		/* IP6_2_0 [3] */
		FN_DACK0, FN_IRQ0, FN_INTC_IRQ0_N, FN_SSI_SCK6_B,
		FN_VI1_VSYNC_N, FN_VI1_VSYNC_N_B, FN_SSI_WS78_C, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR7", 0xE606003C, 32,
			     1, 2, 2, 2, 3, 3, 3, 3, 3, 2, 2, 3, 3) {
		/* IP7_31 [1] */
		0, 0,
		/* IP7_30_29 [2] */
		FN_VI0_DATA0_VI0_B0, FN_ATACS10_N, FN_AVB_RXD2,
		FN_MII_RXD2,
		/* IP7_28_27 [2] */
		FN_VI0_CLK, FN_ATACS00_N, FN_AVB_RXD1, FN_MII_RXD1,
		/* IP7_26_25 [2] */
		FN_DU1_DOTCLKIN, FN_AUDIO_CLKC, FN_AUDIO_CLKOUT_C, 0,
		/* IP7_24_22 [3] */
		FN_PWM2, FN_PWMFSW0, FN_SCIFA2_RXD_C, FN_PCMWE_N, FN_IECLK_C,
		0, 0, 0,
		/* IP7_21_19 [3] */
		FN_PWM1, FN_SCIFA2_TXD_C, FN_STP_ISSYNC_1_B, FN_TS_SCK1_C,
		FN_GLO_RFON_C, FN_PCMOE_N, 0, 0,
		/* IP7_18_16 [3] */
		FN_PWM0, FN_SCIFA2_SCK_C, FN_STP_ISEN_1_B, FN_TS_SDAT1_C,
		FN_GLO_SS_C, 0, 0, 0,
		/* IP7_15_13 [3] */
		FN_ETH_MDC, FN_RMII_MDC, FN_STP_ISD_1_B,
		FN_TS_SPSYNC1_C, FN_GLO_SDATA_C, 0, 0, 0,
		/* IP7_12_10 [3] */
		FN_ETH_TXD0, FN_RMII_TXD0, FN_STP_ISCLK_1_B, FN_TS_SDEN1_C,
		FN_GLO_SCLK_C, 0, 0, 0,
		/* IP7_9_8 [2] */
		FN_ETH_MAGIC, FN_RMII_MAGIC, FN_SIM0_RST_C, 0,
		/* IP7_7_6 [2] */
		FN_ETH_TX_EN, FN_RMII_TX_EN, FN_SIM0_CLK_C, FN_HRTS0_N_F,
		/* IP7_5_3 [3] */
		FN_ETH_TXD1, FN_RMII_TXD1, FN_HTX0_F, FN_BPFCLK_G, FN_RDS_CLK_F,
		0, 0, 0,
		/* IP7_2_0 [3] */
		FN_ETH_MDIO, FN_RMII_MDIO, FN_HRTS0_N_E,
		FN_SIM0_D_C, FN_HCTS0_N_F, 0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR8", 0xE6060040, 32,
			     1, 2, 1, 1, 1, 2, 2, 2, 2, 2, 2,
			     2, 2, 2, 2, 2, 2, 2) {
		/* IP8_31 [1] */
		0, 0,
		/* IP8_30_29 [2] */
		FN_SD0_CMD, FN_SCIFB1_SCK_B, FN_VI1_DATA1_VI1_B1_B, 0,
		/* IP8_28 [1] */
		FN_SD0_CLK, FN_VI1_DATA0_VI1_B0_B,
		/* IP8_27 [1] */
		FN_VI1_DATA6_VI1_B6, FN_AVB_GTXREFCLK,
		/* IP8_26 [1] */
		FN_VI1_DATA5_VI1_B5, FN_AVB_PHY_INT,
		/* IP8_25_24 [2] */
		FN_VI1_DATA4_VI1_B4, FN_SCIFA1_RTS_N_D,
		FN_AVB_MAGIC, FN_MII_MAGIC,
		/* IP8_23_22 [2] */
		FN_VI1_DATA3_VI1_B3, FN_SCIFA1_CTS_N_D, FN_AVB_GTX_CLK, 0,
		/* IP8_21_20 [2] */
		FN_VI1_DATA2_VI1_B2, FN_SCIFA1_TXD_D, FN_AVB_MDIO,
		FN_MII_MDIO,
		/* IP8_19_18 [2] */
		FN_VI1_DATA1_VI1_B1, FN_SCIFA1_RXD_D, FN_AVB_MDC, FN_MII_MDC,
		/* IP8_17_16 [2] */
		FN_VI1_DATA0_VI1_B0, FN_SCIFA1_SCK_D, FN_AVB_CRS, FN_MII_CRS,
		/* IP8_15_14 [2] */
		FN_VI1_CLK, FN_AVB_RX_DV, FN_MII_RX_DV, 0,
		/* IP8_13_12 [2] */
		FN_VI0_DATA7_VI0_B7, FN_AVB_RX_CLK, FN_MII_RX_CLK, 0,
		/* IP8_11_10 [2] */
		FN_VI0_DATA6_VI0_B6, FN_AVB_RX_ER, FN_MII_RX_ER, 0,
		/* IP8_9_8 [2] */
		FN_VI0_DATA5_VI0_B5, FN_EX_WAIT1, FN_AVB_RXD7, 0,
		/* IP8_7_6 [2] */
		FN_VI0_DATA4_VI0_B4, FN_ATAG0_N, FN_AVB_RXD6, 0,
		/* IP8_5_4 [2] */
		FN_VI0_DATA3_VI0_B3, FN_ATADIR0_N, FN_AVB_RXD5, 0,
		/* IP8_3_2 [2] */
		FN_VI0_DATA2_VI0_B2, FN_ATAWR0_N, FN_AVB_RXD4, 0,
		/* IP8_1_0 [2] */
		FN_VI0_DATA1_VI0_B1, FN_ATARD0_N, FN_AVB_RXD3, FN_MII_RXD3, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR9", 0xE6060044, 32,
			     4, 2, 2, 2, 2, 2, 2, 4, 4, 2, 2, 2, 2) {
		/* IP9_31_28 [4] */
		FN_SD1_CD, FN_MMC1_D6, FN_TS_SDEN1, FN_USB1_EXTP,
		FN_GLO_SS, FN_VI0_CLK_B, FN_SCL2_D, FN_SCL2_CIS_D,
		FN_SIM0_CLK_B, FN_VI3_CLK_B, 0, 0, 0, 0, 0, 0,
		/* IP9_27_26 [2] */
		FN_SD1_DAT3, FN_AVB_RXD0, FN_MII_RXD0, FN_SCIFB0_RTS_N_B,
		/* IP9_25_24 [2] */
		FN_SD1_DAT2, FN_AVB_COL, FN_MII_COL, FN_SCIFB0_CTS_N_B,
		/* IP9_23_22 [2] */
		FN_SD1_DAT1, FN_AVB_LINK, FN_MII_LINK, FN_SCIFB0_TXD_B,
		/* IP9_21_20 [2] */
		FN_SD1_DAT0, FN_AVB_TX_CLK, FN_MII_TX_CLK, FN_SCIFB0_RXD_B,
		/* IP9_19_18 [2] */
		FN_SD1_CMD, FN_AVB_TX_ER, FN_MII_TX_ER, FN_SCIFB0_SCK_B,
		/* IP9_17_16 [2] */
		FN_SD1_CLK, FN_AVB_TX_EN, FN_MII_TX_EN, 0,
		/* IP9_15_12 [4] */
		FN_SD0_WP, FN_MMC0_D7, FN_TS_SPSYNC0_B, FN_USB0_IDIN,
		FN_GLO_SDATA, FN_VI1_DATA7_VI1_B7_B, FN_SDA1_B,
		FN_SDA1_CIS_B, FN_VI2_DATA7_VI2_B7_B, 0, 0, 0, 0, 0, 0, 0,
		/* IP9_11_8 [4] */
		FN_SD0_CD, FN_MMC0_D6, FN_TS_SDEN0_B, FN_USB0_EXTP,
		FN_GLO_SCLK, FN_VI1_DATA6_VI1_B6_B, FN_SCL1_B,
		FN_SCL1_CIS_B, FN_VI2_DATA6_VI2_B6_B, 0, 0, 0, 0, 0, 0, 0,
		/* IP9_7_6 [2] */
		FN_SD0_DAT3, FN_SCIFB1_RTS_N_B, FN_VI1_DATA5_VI1_B5_B, 0,
		/* IP9_5_4 [2] */
		FN_SD0_DAT2, FN_SCIFB1_CTS_N_B, FN_VI1_DATA4_VI1_B4_B, 0,
		/* IP9_3_2 [2] */
		FN_SD0_DAT1, FN_SCIFB1_TXD_B, FN_VI1_DATA3_VI1_B3_B, 0,
		/* IP9_1_0 [2] */
		FN_SD0_DAT0, FN_SCIFB1_RXD_B, FN_VI1_DATA2_VI1_B2_B, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR10", 0xE6060048, 32,
			     2, 4, 3, 4, 4, 4, 4, 3, 4) {
		/* IP10_31_30 [2] */
		0, 0, 0, 0,
		/* IP10_29_26 [4] */
		FN_SD2_CD, FN_MMC0_D4, FN_TS_SDAT0_B, FN_USB2_EXTP, FN_GLO_I0,
		FN_VI0_DATA6_VI0_B6_B, FN_HCTS0_N_D, FN_TS_SDAT1_B,
		FN_GLO_I0_B, FN_VI3_DATA6_B, 0, 0, 0, 0, 0, 0,
		/* IP10_25_23 [3] */
		FN_SD2_DAT3, FN_MMC0_D3, FN_SIM0_RST, FN_VI0_DATA5_VI0_B5_B,
		FN_HTX0_D, FN_TS_SPSYNC1_B, FN_GLO_Q1_B, FN_VI3_DATA5_B,
		/* IP10_22_19 [4] */
		FN_SD2_DAT2, FN_MMC0_D2, FN_BPFCLK_B, FN_RDS_CLK,
		FN_VI0_DATA4_VI0_B4_B, FN_HRX0_D, FN_TS_SDEN1_B,
		FN_GLO_Q0_B, FN_VI3_DATA4_B, 0, 0, 0, 0, 0, 0, 0,
		/* IP10_18_15 [4] */
		FN_SD2_DAT1, FN_MMC0_D1, FN_FMIN_B, FN_RDS_DATA,
		FN_VI0_DATA3_VI0_B3_B, FN_SCIFB1_TXD_E, FN_TX1_D,
		FN_TS_SCK0_C, FN_GLO_RFON_B, FN_VI3_DATA3_B,
		0, 0, 0, 0, 0, 0,
		/* IP10_14_11 [4] */
		FN_SD2_DAT0, FN_MMC0_D0, FN_FMCLK_B,
		FN_VI0_DATA2_VI0_B2_B, FN_SCIFB1_RXD_E, FN_RX1_D,
		FN_TS_SDAT0_C, FN_GLO_SS_B, FN_VI3_DATA2_B,
		0, 0, 0, 0, 0, 0, 0,
		/* IP10_10_7 [4] */
		FN_SD2_CMD, FN_MMC0_CMD, FN_SIM0_D,
		FN_VI0_DATA1_VI0_B1_B, FN_SCIFB1_SCK_E, FN_SCK1_D,
		FN_TS_SPSYNC0_C, FN_GLO_SDATA_B, FN_VI3_DATA1_B,
		0, 0, 0, 0, 0, 0, 0,
		/* IP10_6_4 [3] */
		FN_SD2_CLK, FN_MMC0_CLK, FN_SIM0_CLK,
		FN_VI0_DATA0_VI0_B0_B, FN_TS_SDEN0_C, FN_GLO_SCLK_B,
		FN_VI3_DATA0_B, 0,
		/* IP10_3_0 [4] */
		FN_SD1_WP, FN_MMC1_D7, FN_TS_SPSYNC1, FN_USB1_IDIN,
		FN_GLO_RFON, FN_VI1_CLK_B, FN_SDA2_D, FN_SDA2_CIS_D,
		FN_SIM0_D_B, 0, 0, 0, 0, 0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR11", 0xE606004C, 32,
			     2, 3, 3, 2, 4, 3, 2, 2, 2, 2, 2, 1, 4) {
		/* IP11_31_30 [2] */
		FN_SSI_SCK0129, FN_CAN_CLK_B, FN_MOUT0, 0,
		/* IP11_29_27 [3] */
		FN_MLB_DAT, FN_SPV_EVEN, FN_SCIFB1_TXD_D, FN_TX1_C, FN_BPFCLK_C,
		FN_RDS_CLK_B, 0, 0,
		/* IP11_26_24 [3] */
		FN_MLB_SIG, FN_SCIFB1_RXD_D, FN_RX1_C, FN_SDA2_B, FN_SDA2_CIS_B,
		0, 0, 0,
		/* IP11_23_22 [2] */
		FN_MLB_CLK, FN_SCL2_B, FN_SCL2_CIS_B, 0,
		/* IP11_21_18 [4] */
		FN_SD3_WP, FN_MMC1_D5, FN_TS_SCK1, FN_GLO_Q1, FN_FMIN_C,
		FN_RDS_DATA_B, FN_FMIN_E, FN_RDS_DATA_D, FN_FMIN_F,
		FN_RDS_DATA_E, 0, 0, 0, 0, 0, 0,
		/* IP11_17_15 [3] */
		FN_SD3_CD, FN_MMC1_D4, FN_TS_SDAT1,
		FN_VSP, FN_GLO_Q0, FN_SIM0_RST_B, 0, 0,
		/* IP11_14_13 [2] */
		FN_SD3_DAT3, FN_MMC1_D3, FN_SCKZ, 0,
		/* IP11_12_11 [2] */
		FN_SD3_DAT2, FN_MMC1_D2, FN_SDATA, 0,
		/* IP11_10_9 [2] */
		FN_SD3_DAT1, FN_MMC1_D1, FN_MDATA, 0,
		/* IP11_8_7 [2] */
		FN_SD3_DAT0, FN_MMC1_D0, FN_STM_N, 0,
		/* IP11_6_5 [2] */
		FN_SD3_CMD, FN_MMC1_CMD, FN_MTS_N, 0,
		/* IP11_4 [1] */
		FN_SD3_CLK, FN_MMC1_CLK,
		/* IP11_3_0 [4] */
		FN_SD2_WP, FN_MMC0_D5, FN_TS_SCK0_B, FN_USB2_IDIN,
		FN_GLO_I1, FN_VI0_DATA7_VI0_B7_B, FN_HRTS0_N_D,
		FN_TS_SCK1_B, FN_GLO_I1_B, FN_VI3_DATA7_B, 0, 0, 0, 0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL", 0xE6060090, 32,
			     3, 2, 2, 3, 2, 1, 1, 1, 2, 1,
			     2, 1, 1, 1, 1, 2, 1, 1, 2, 1, 1) {
		/* SEL_SCIF1 [3] */
		FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, FN_SEL_SCIF1_3,
		FN_SEL_SCIF1_4, 0, 0, 0,
		/* SEL_SCIFB [2] */
		FN_SEL_SCIFB_0, FN_SEL_SCIFB_1, FN_SEL_SCIFB_2, 0,
		/* SEL_SCIFB2 [2] */
		FN_SEL_SCIFB2_0, FN_SEL_SCIFB2_1, FN_SEL_SCIFB2_2, 0,
		/* SEL_SCIFB1 [3] */
		FN_SEL_SCIFB1_0, FN_SEL_SCIFB1_1, FN_SEL_SCIFB1_2,
		FN_SEL_SCIFB1_3, FN_SEL_SCIFB1_4, FN_SEL_SCIFB1_5,
		FN_SEL_SCIFB1_6, 0,
		/* SEL_SCIFA1 [2] */
		FN_SEL_SCIFA1_0, FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2,
		FN_SEL_SCIFA1_3,
		/* SEL_SCIF0 [1] */
		FN_SEL_SCIF0_0, FN_SEL_SCIF0_1,
		/* SEL_SCIFA [1] */
		FN_SEL_SCFA_0, FN_SEL_SCFA_1,
		/* SEL_SOF1 [1] */
		FN_SEL_SOF1_0, FN_SEL_SOF1_1,
		/* SEL_SSI7 [2] */
		FN_SEL_SSI7_0, FN_SEL_SSI7_1, FN_SEL_SSI7_2, 0,
		/* SEL_SSI6 [1] */
		FN_SEL_SSI6_0, FN_SEL_SSI6_1,
		/* SEL_SSI5 [2] */
		FN_SEL_SSI5_0, FN_SEL_SSI5_1, FN_SEL_SSI5_2, 0,
		/* SEL_VI3 [1] */
		FN_SEL_VI3_0, FN_SEL_VI3_1,
		/* SEL_VI2 [1] */
		FN_SEL_VI2_0, FN_SEL_VI2_1,
		/* SEL_VI1 [1] */
		FN_SEL_VI1_0, FN_SEL_VI1_1,
		/* SEL_VI0 [1] */
		FN_SEL_VI0_0, FN_SEL_VI0_1,
		/* SEL_TSIF1 [2] */
		FN_SEL_TSIF1_0, FN_SEL_TSIF1_1, FN_SEL_TSIF1_2, 0,
		/* RESERVED [1] */
		0, 0,
		/* SEL_LBS [1] */
		FN_SEL_LBS_0, FN_SEL_LBS_1,
		/* SEL_TSIF0 [2] */
		FN_SEL_TSIF0_0, FN_SEL_TSIF0_1, FN_SEL_TSIF0_2, FN_SEL_TSIF0_3,
		/* SEL_SOF3 [1] */
		FN_SEL_SOF3_0, FN_SEL_SOF3_1,
		/* SEL_SOF0 [1] */
		FN_SEL_SOF0_0, FN_SEL_SOF0_1, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL2", 0xE6060094, 32,
			     2, 1, 1, 1, 1, 2, 1, 2, 1,
			     2, 1, 1, 1, 3, 3, 2, 3, 2, 2) {
		/* RESEVED [2] */
		0, 0, 0, 0, 0, 0, 0, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_TMU1 [1] */
		FN_SEL_TMU1_0, FN_SEL_TMU1_1,
		/* SEL_HSCIF1 [1] */
		FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1,
		/* SEL_SCIFCLK [1] */
		FN_SEL_SCIFCLK_0, FN_SEL_SCIFCLK_1,
		/* SEL_CAN0 [2] */
		FN_SEL_CAN0_0, FN_SEL_CAN0_1, FN_SEL_CAN0_2, FN_SEL_CAN0_3,
		/* SEL_CANCLK [1] */
		FN_SEL_CANCLK_0, FN_SEL_CANCLK_1,
		/* SEL_SCIFA2 [2] */
		FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1, FN_SEL_SCIFA2_2, 0,
		/* SEL_CAN1 [1] */
		FN_SEL_CAN1_0, FN_SEL_CAN1_1,
		/* RESEVED [2] */
		0, 0, 0, 0, 0, 0, 0, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_ADI [1] */
		FN_SEL_ADI_0, FN_SEL_ADI_1,
		/* SEL_SSP [1] */
		FN_SEL_SSP_0, FN_SEL_SSP_1,
		/* SEL_FM [3] */
		FN_SEL_FM_0, FN_SEL_FM_1, FN_SEL_FM_2, FN_SEL_FM_3,
		FN_SEL_FM_4, FN_SEL_FM_5, FN_SEL_FM_6, 0,
		/* SEL_HSCIF0 [3] */
		FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1, FN_SEL_HSCIF0_2,
		FN_SEL_HSCIF0_3, FN_SEL_HSCIF0_4, FN_SEL_HSCIF0_5, 0, 0,
		/* SEL_GPS [2] */
		FN_SEL_GPS_0, FN_SEL_GPS_1, FN_SEL_GPS_2, 0,
		/* SEL_RDS [3] */
		FN_SEL_RDS_0, FN_SEL_RDS_1, FN_SEL_RDS_2,
		FN_SEL_RDS_3, FN_SEL_RDS_4, FN_SEL_RDS_5, 0, 0,
		/* SEL_SIM [2] */
		FN_SEL_SIM_0, FN_SEL_SIM_1, FN_SEL_SIM_2, 0,
		/* SEL_SSI8 [2] */
		FN_SEL_SSI8_0, FN_SEL_SSI8_1, FN_SEL_SSI8_2, 0, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL3", 0xE6060098, 32,
			     1, 1, 2, 4, 4, 2, 2,
			     4, 2, 3, 2, 3, 2) {
		/* SEL_IICDVFS [1] */
		FN_SEL_IICDVFS_0, FN_SEL_IICDVFS_1,
		/* SEL_IIC0 [1] */
		FN_SEL_IIC0_0, FN_SEL_IIC0_1,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* RESEVED [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_IEB [2] */
		FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2, 0,
		/* RESEVED [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_IIC2 [3] */
		FN_SEL_IIC2_0, FN_SEL_IIC2_1, FN_SEL_IIC2_2, FN_SEL_IIC2_3,
		FN_SEL_IIC2_4, 0, 0, 0,
		/* SEL_IIC1 [2] */
		FN_SEL_IIC1_0, FN_SEL_IIC1_1, FN_SEL_IIC1_2, 0,
		/* SEL_I2C2 [3] */
		FN_SEL_I2C2_0, FN_SEL_I2C2_1, FN_SEL_I2C2_2, FN_SEL_I2C2_3,
		FN_SEL_I2C2_4, 0, 0, 0,
		/* SEL_I2C1 [2] */
		FN_SEL_I2C1_0, FN_SEL_I2C1_1, FN_SEL_I2C1_2, 0, }
	},
	{ PINMUX_CFG_REG("INOUTSEL0", 0xE6050004, 32, 1) { GP_INOUTSEL(0) } },
	{ PINMUX_CFG_REG("INOUTSEL1", 0xE6051004, 32, 1) {
		0, 0,
		0, 0,
		GP_1_29_IN, GP_1_29_OUT,
		GP_1_28_IN, GP_1_28_OUT,
		GP_1_27_IN, GP_1_27_OUT,
		GP_1_26_IN, GP_1_26_OUT,
		GP_1_25_IN, GP_1_25_OUT,
		GP_1_24_IN, GP_1_24_OUT,
		GP_1_23_IN, GP_1_23_OUT,
		GP_1_22_IN, GP_1_22_OUT,
		GP_1_21_IN, GP_1_21_OUT,
		GP_1_20_IN, GP_1_20_OUT,
		GP_1_19_IN, GP_1_19_OUT,
		GP_1_18_IN, GP_1_18_OUT,
		GP_1_17_IN, GP_1_17_OUT,
		GP_1_16_IN, GP_1_16_OUT,
		GP_1_15_IN, GP_1_15_OUT,
		GP_1_14_IN, GP_1_14_OUT,
		GP_1_13_IN, GP_1_13_OUT,
		GP_1_12_IN, GP_1_12_OUT,
		GP_1_11_IN, GP_1_11_OUT,
		GP_1_10_IN, GP_1_10_OUT,
		GP_1_9_IN, GP_1_9_OUT,
		GP_1_8_IN, GP_1_8_OUT,
		GP_1_7_IN, GP_1_7_OUT,
		GP_1_6_IN, GP_1_6_OUT,
		GP_1_5_IN, GP_1_5_OUT,
		GP_1_4_IN, GP_1_4_OUT,
		GP_1_3_IN, GP_1_3_OUT,
		GP_1_2_IN, GP_1_2_OUT,
		GP_1_1_IN, GP_1_1_OUT,
		GP_1_0_IN, GP_1_0_OUT, }
	},
	{ PINMUX_CFG_REG("INOUTSEL2", 0xE6052004, 32, 1) {
		0, 0,
		0, 0,
		GP_2_29_IN, GP_2_29_OUT,
		GP_2_28_IN, GP_2_28_OUT,
		GP_2_27_IN, GP_2_27_OUT,
		GP_2_26_IN, GP_2_26_OUT,
		GP_2_25_IN, GP_2_25_OUT,
		GP_2_24_IN, GP_2_24_OUT,
		GP_2_23_IN, GP_2_23_OUT,
		GP_2_22_IN, GP_2_22_OUT,
		GP_2_21_IN, GP_2_21_OUT,
		GP_2_20_IN, GP_2_20_OUT,
		GP_2_19_IN, GP_2_19_OUT,
		GP_2_18_IN, GP_2_18_OUT,
		GP_2_17_IN, GP_2_17_OUT,
		GP_2_16_IN, GP_2_16_OUT,
		GP_2_15_IN, GP_2_15_OUT,
		GP_2_14_IN, GP_2_14_OUT,
		GP_2_13_IN, GP_2_13_OUT,
		GP_2_12_IN, GP_2_12_OUT,
		GP_2_11_IN, GP_2_11_OUT,
		GP_2_10_IN, GP_2_10_OUT,
		GP_2_9_IN, GP_2_9_OUT,
		GP_2_8_IN, GP_2_8_OUT,
		GP_2_7_IN, GP_2_7_OUT,
		GP_2_6_IN, GP_2_6_OUT,
		GP_2_5_IN, GP_2_5_OUT,
		GP_2_4_IN, GP_2_4_OUT,
		GP_2_3_IN, GP_2_3_OUT,
		GP_2_2_IN, GP_2_2_OUT,
		GP_2_1_IN, GP_2_1_OUT,
		GP_2_0_IN, GP_2_0_OUT, }
	},
	{ PINMUX_CFG_REG("INOUTSEL3", 0xE6053004, 32, 1) { GP_INOUTSEL(3) } },
	{ PINMUX_CFG_REG("INOUTSEL4", 0xE6054004, 32, 1) { GP_INOUTSEL(4) } },
	{ PINMUX_CFG_REG("INOUTSEL5", 0xE6055004, 32, 1) { GP_INOUTSEL(5) } },
	{ },
};

static struct pinmux_data_reg pinmux_data_regs[] = {
	{ PINMUX_DATA_REG("INDT0", 0xE6050008, 32) { GP_INDT(0) } },
	{ PINMUX_DATA_REG("INDT1", 0xE6051008, 32) {
		0, 0, GP_1_29_DATA, GP_1_28_DATA,
		GP_1_27_DATA, GP_1_26_DATA, GP_1_25_DATA, GP_1_24_DATA,
		GP_1_23_DATA, GP_1_22_DATA, GP_1_21_DATA, GP_1_20_DATA,
		GP_1_19_DATA, GP_1_18_DATA, GP_1_17_DATA, GP_1_16_DATA,
		GP_1_15_DATA, GP_1_14_DATA, GP_1_13_DATA, GP_1_12_DATA,
		GP_1_11_DATA, GP_1_10_DATA, GP_1_9_DATA, GP_1_8_DATA,
		GP_1_7_DATA, GP_1_6_DATA, GP_1_5_DATA, GP_1_4_DATA,
		GP_1_3_DATA, GP_1_2_DATA, GP_1_1_DATA, GP_1_0_DATA }
	},
	{ PINMUX_DATA_REG("INDT2", 0xE6052008, 32) {
		0, 0, GP_2_29_DATA, GP_2_28_DATA,
		GP_2_27_DATA, GP_2_26_DATA, GP_2_25_DATA, GP_2_24_DATA,
		GP_2_23_DATA, GP_2_22_DATA, GP_2_21_DATA, GP_2_20_DATA,
		GP_2_19_DATA, GP_2_18_DATA, GP_2_17_DATA, GP_2_16_DATA,
		GP_2_15_DATA, GP_2_14_DATA, GP_2_13_DATA, GP_2_12_DATA,
		GP_2_11_DATA, GP_2_10_DATA, GP_2_9_DATA, GP_2_8_DATA,
		GP_2_7_DATA, GP_2_6_DATA, GP_2_5_DATA, GP_2_4_DATA,
		GP_2_3_DATA, GP_2_2_DATA, GP_2_1_DATA, GP_2_0_DATA }
	},
	{ PINMUX_DATA_REG("INDT3", 0xE6053008, 32) { GP_INDT(3) } },
	{ PINMUX_DATA_REG("INDT4", 0xE6054008, 32) { GP_INDT(4) } },
	{ PINMUX_DATA_REG("INDT5", 0xE6055008, 32) { GP_INDT(5) } },
	{ },
};

static struct pinmux_info r8a7790_pinmux_info = {
	.name = "r8a7790_pfc",

	.unlock_reg = 0xe6060000, /* PMMR */

	.reserved_id = PINMUX_RESERVED,
	.data = { PINMUX_DATA_BEGIN, PINMUX_DATA_END },
	.input = { PINMUX_INPUT_BEGIN, PINMUX_INPUT_END },
	.output = { PINMUX_OUTPUT_BEGIN, PINMUX_OUTPUT_END },
	.mark = { PINMUX_MARK_BEGIN, PINMUX_MARK_END },
	.function = { PINMUX_FUNCTION_BEGIN, PINMUX_FUNCTION_END },

	.first_gpio = GPIO_GP_0_0,
	.last_gpio = GPIO_FN_MOUT0,

	.gpios = pinmux_gpios,
	.cfg_regs = pinmux_config_regs,
	.data_regs = pinmux_data_regs,

	.gpio_data = pinmux_data,
	.gpio_data_size = ARRAY_SIZE(pinmux_data),
};

void r8a7790_pinmux_init(void)
{
	register_pinmux(&r8a7790_pinmux_info);
}
