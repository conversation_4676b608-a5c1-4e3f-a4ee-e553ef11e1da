/*
 * arch/arm/cpu/armv7/rmobile/pfc-r8a7793.c
 *
 * Copyright (C) 2013 Renesas Electronics Corporation
 *
 * SPDX-License-Identifier: GPL-2.0
 */

#include <common.h>
#include <sh_pfc.h>
#include <asm/gpio.h>

#define CPU_32_PORT(fn, pfx, sfx)				\
	PORT_10(fn, pfx, sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_10(fn, pfx##2, sfx), PORT_1(fn, pfx##30, sfx),	\
	PORT_1(fn, pfx##31, sfx)

#define CPU_32_PORT1(fn, pfx, sfx)				\
	PORT_10(fn, pfx, sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_1(fn, pfx##20, sfx), PORT_1(fn, pfx##21, sfx),	\
	PORT_1(fn, pfx##22, sfx), PORT_1(fn, pfx##23, sfx),	\
	PORT_1(fn, pfx##24, sfx), PORT_1(fn, pfx##25, sfx)

/*
 * GP_0_0_DATA -> GP_7_25_DATA
 * (except for GP1[26],GP1[27],GP1[28],GP1[29]),GP1[30]),GP1[31]
 *  GP7[26],GP7[27],GP7[28],GP7[29]),GP7[30]),GP7[31])
 */
#define CPU_ALL_PORT(fn, pfx, sfx)				\
	CPU_32_PORT(fn, pfx##_0_, sfx),				\
	CPU_32_PORT1(fn, pfx##_1_, sfx),			\
	CPU_32_PORT(fn, pfx##_2_, sfx),			\
	CPU_32_PORT(fn, pfx##_3_, sfx),				\
	CPU_32_PORT(fn, pfx##_4_, sfx),				\
	CPU_32_PORT(fn, pfx##_5_, sfx),			\
	CPU_32_PORT(fn, pfx##_6_, sfx),			\
	CPU_32_PORT1(fn, pfx##_7_, sfx)

#define _GP_GPIO(pfx, sfx) PINMUX_GPIO(GPIO_GP##pfx, GP##pfx##_DATA)
#define _GP_DATA(pfx, sfx) PINMUX_DATA(GP##pfx##_DATA, GP##pfx##_FN,	\
				       GP##pfx##_IN, GP##pfx##_OUT)

#define _GP_INOUTSEL(pfx, sfx) GP##pfx##_IN, GP##pfx##_OUT
#define _GP_INDT(pfx, sfx) GP##pfx##_DATA

#define GP_ALL(str)	CPU_ALL_PORT(_PORT_ALL, GP, str)
#define PINMUX_GPIO_GP_ALL()	CPU_ALL_PORT(_GP_GPIO, , unused)
#define PINMUX_DATA_GP_ALL()	CPU_ALL_PORT(_GP_DATA, , unused)


#define PORT_10_REV(fn, pfx, sfx)				\
	PORT_1(fn, pfx##9, sfx), PORT_1(fn, pfx##8, sfx),	\
	PORT_1(fn, pfx##7, sfx), PORT_1(fn, pfx##6, sfx),	\
	PORT_1(fn, pfx##5, sfx), PORT_1(fn, pfx##4, sfx),	\
	PORT_1(fn, pfx##3, sfx), PORT_1(fn, pfx##2, sfx),	\
	PORT_1(fn, pfx##1, sfx), PORT_1(fn, pfx##0, sfx)

#define CPU_32_PORT_REV(fn, pfx, sfx)					\
	PORT_1(fn, pfx##31, sfx), PORT_1(fn, pfx##30, sfx),		\
	PORT_10_REV(fn, pfx##2, sfx), PORT_10_REV(fn, pfx##1, sfx),	\
	PORT_10_REV(fn, pfx, sfx)

#define GP_INOUTSEL(bank) CPU_32_PORT_REV(_GP_INOUTSEL, _##bank##_, unused)
#define GP_INDT(bank) CPU_32_PORT_REV(_GP_INDT, _##bank##_, unused)

#define PINMUX_IPSR_DATA(ipsr, fn) PINMUX_DATA(fn##_MARK, FN_##ipsr, FN_##fn)
#define PINMUX_IPSR_MODSEL_DATA(ipsr, fn, ms) PINMUX_DATA(fn##_MARK, FN_##ms, \
							  FN_##ipsr, FN_##fn)

enum {
	PINMUX_RESERVED = 0,

	PINMUX_DATA_BEGIN,
	GP_ALL(DATA),
	PINMUX_DATA_END,

	PINMUX_INPUT_BEGIN,
	GP_ALL(IN),
	PINMUX_INPUT_END,

	PINMUX_OUTPUT_BEGIN,
	GP_ALL(OUT),
	PINMUX_OUTPUT_END,

	PINMUX_FUNCTION_BEGIN,
	GP_ALL(FN),

	/* GPSR0 */
	FN_IP0_0, FN_IP0_1, FN_IP0_2, FN_IP0_3, FN_IP0_4, FN_IP0_5,
	FN_IP0_6, FN_IP0_7, FN_IP0_8, FN_IP0_9, FN_IP0_10, FN_IP0_11,
	FN_IP0_12, FN_IP0_13, FN_IP0_14, FN_IP0_15, FN_IP0_18_16, FN_IP0_20_19,
	FN_IP0_22_21, FN_IP0_24_23, FN_IP0_26_25, FN_IP0_28_27, FN_IP0_30_29,
	FN_IP1_1_0, FN_IP1_3_2, FN_IP1_5_4, FN_IP1_7_6, FN_IP1_10_8,
	FN_IP1_13_11, FN_IP1_16_14, FN_IP1_19_17, FN_IP1_22_20,

	/* GPSR1 */
	FN_IP1_25_23, FN_IP1_28_26, FN_IP1_31_29, FN_IP2_2_0, FN_IP2_4_3,
	FN_IP2_6_5, FN_IP2_9_7, FN_IP2_12_10, FN_IP2_15_13, FN_IP2_18_16,
	FN_IP2_20_19, FN_IP2_22_21, FN_EX_CS0_N, FN_IP2_24_23, FN_IP2_26_25,
	FN_IP2_29_27, FN_IP3_2_0, FN_IP3_5_3, FN_IP3_8_6, FN_RD_N,
	FN_IP3_11_9, FN_IP3_13_12, FN_IP3_15_14 , FN_IP3_17_16 , FN_IP3_19_18,
	FN_IP3_21_20,

	/* GPSR2 */
	FN_IP3_27_25, FN_IP3_30_28, FN_IP4_1_0, FN_IP4_4_2, FN_IP4_7_5,
	FN_IP4_9_8, FN_IP4_12_10, FN_IP4_15_13, FN_IP4_18_16, FN_IP4_19,
	FN_IP4_20, FN_IP4_21, FN_IP4_23_22, FN_IP4_25_24, FN_IP4_27_26,
	FN_IP4_30_28, FN_IP5_2_0, FN_IP5_5_3, FN_IP5_8_6, FN_IP5_11_9,
	FN_IP5_14_12, FN_IP5_16_15, FN_IP5_19_17, FN_IP5_21_20, FN_IP5_23_22,
	FN_IP5_25_24, FN_IP5_28_26, FN_IP5_31_29, FN_AUDIO_CLKA, FN_IP6_2_0,
	FN_IP6_5_3, FN_IP6_7_6,

	/* GPSR3 */
	FN_IP7_5_3, FN_IP7_8_6, FN_IP7_10_9, FN_IP7_12_11, FN_IP7_14_13,
	FN_IP7_16_15, FN_IP7_18_17, FN_IP7_20_19, FN_IP7_23_21, FN_IP7_26_24,
	FN_IP7_29_27, FN_IP8_2_0, FN_IP8_5_3, FN_IP8_8_6, FN_IP8_11_9,
	FN_IP8_14_12, FN_IP8_17_15, FN_IP8_20_18, FN_IP8_23_21, FN_IP8_25_24,
	FN_IP8_27_26, FN_IP8_30_28, FN_IP9_2_0, FN_IP9_5_3, FN_IP9_6, FN_IP9_7,
	FN_IP9_10_8, FN_IP9_11, FN_IP9_12, FN_IP9_15_13, FN_IP9_16,
	FN_IP9_18_17,

	/* GPSR4 */
	FN_VI0_CLK, FN_IP9_20_19, FN_IP9_22_21, FN_IP9_24_23, FN_IP9_26_25,
	FN_VI0_DATA0_VI0_B0, FN_VI0_DATA0_VI0_B1, FN_VI0_DATA0_VI0_B2,
	FN_IP9_28_27, FN_VI0_DATA0_VI0_B4, FN_VI0_DATA0_VI0_B5,
	FN_VI0_DATA0_VI0_B6, FN_VI0_DATA0_VI0_B7, FN_IP9_31_29, FN_IP10_2_0,
	FN_IP10_5_3, FN_IP10_8_6, FN_IP10_11_9, FN_IP10_14_12, FN_IP10_16_15,
	FN_IP10_18_17, FN_IP10_21_19, FN_IP10_24_22, FN_IP10_26_25,
	FN_IP10_28_27, FN_IP10_31_29, FN_IP11_2_0, FN_IP11_5_3, FN_IP11_8_6,
	FN_IP15_1_0, FN_IP15_3_2, FN_IP15_5_4,

	/* GPSR5 */
	FN_IP11_11_9, FN_IP11_14_12, FN_IP11_16_15, FN_IP11_18_17, FN_IP11_19,
	FN_IP11_20, FN_IP11_21, FN_IP11_22, FN_IP11_23, FN_IP11_24,
	FN_IP11_25, FN_IP11_26, FN_IP11_27, FN_IP11_29_28, FN_IP11_31_30,
	FN_IP12_1_0, FN_IP12_3_2, FN_IP12_6_4, FN_IP12_9_7, FN_IP12_12_10,
	FN_IP12_15_13, FN_IP12_17_16, FN_IP12_19_18, FN_IP12_21_20,
	FN_IP12_23_22, FN_IP12_26_24, FN_IP12_29_27, FN_IP13_2_0, FN_IP13_4_3,
	FN_IP13_6_5, FN_IP13_9_7, FN_IP3_24_22,

	/* GPSR6 */
	FN_IP13_10, FN_IP13_11, FN_IP13_12, FN_IP13_13, FN_IP13_14,
	FN_IP13_15, FN_IP13_18_16, FN_IP13_21_19, FN_IP13_22, FN_IP13_24_23,
	FN_IP13_25, FN_IP13_26, FN_IP13_27, FN_IP13_30_28, FN_IP14_1_0,
	FN_IP14_2, FN_IP14_3, FN_IP14_4, FN_IP14_5, FN_IP14_6, FN_IP14_7,
	FN_IP14_10_8, FN_IP14_13_11, FN_IP14_16_14, FN_IP14_19_17,
	FN_IP14_22_20, FN_IP14_25_23, FN_IP14_28_26, FN_IP14_31_29,

	/* GPSR7 */
	FN_IP15_17_15, FN_IP15_20_18, FN_IP15_23_21, FN_IP15_26_24,
	FN_IP15_29_27, FN_IP16_2_0, FN_IP16_5_3, FN_IP16_7_6, FN_IP16_9_8,
	FN_IP16_11_10, FN_IP6_9_8, FN_IP6_11_10, FN_IP6_13_12, FN_IP6_15_14,
	FN_IP6_18_16, FN_IP6_20_19, FN_IP6_23_21, FN_IP6_26_24, FN_IP6_29_27,
	FN_IP7_2_0, FN_IP15_8_6, FN_IP15_11_9, FN_IP15_14_12,
	FN_USB0_PWEN, FN_USB0_OVC, FN_USB1_PWEN,

	/* IPSR 0 -5 */

	/* IPSR6 */
	FN_AUDIO_CLKB, FN_STP_OPWM_0_B, FN_MSIOF1_SCK_B,
	FN_SCIF_CLK, FN_BPFCLK_E,
	FN_AUDIO_CLKC, FN_SCIFB0_SCK_C, FN_MSIOF1_SYNC_B, FN_RX2,
	FN_SCIFA2_RXD, FN_FMIN_E,
	FN_AUDIO_CLKOUT, FN_MSIOF1_SS1_B, FN_TX2, FN_SCIFA2_TXD,
	FN_IRQ0, FN_SCIFB1_RXD_D, FN_INTC_IRQ0_N,
	FN_IRQ1, FN_SCIFB1_SCK_C, FN_INTC_IRQ1_N,
	FN_IRQ2, FN_SCIFB1_TXD_D, FN_INTC_IRQ2_N,
	FN_IRQ3, FN_SCL4_C, FN_MSIOF2_TXD_E, FN_INTC_IRQ3_N,
	FN_IRQ4, FN_HRX1_C, FN_SDA4_C, FN_MSIOF2_RXD_E, FN_INTC_IRQ4_N,
	FN_IRQ5, FN_HTX1_C, FN_SCL1_E, FN_MSIOF2_SCK_E,
	FN_IRQ6, FN_HSCK1_C, FN_MSIOF1_SS2_B, FN_SDA1_E, FN_MSIOF2_SYNC_E,
	FN_IRQ7, FN_HCTS1_N_C, FN_MSIOF1_TXD_B, FN_GPS_CLK_C, FN_GPS_CLK_D,
	FN_IRQ8, FN_HRTS1_N_C, FN_MSIOF1_RXD_B, FN_GPS_SIGN_C, FN_GPS_SIGN_D,

	/* IPSR7 - IPSR10 */

	/* IPSR11 */
	FN_VI0_R5, FN_VI2_DATA6, FN_GLO_SDATA_B, FN_RX0_C, FN_SDA1_D,
	FN_VI0_R6, FN_VI2_DATA7, FN_GLO_SS_B, FN_TX1_C, FN_SCL4_B,
	FN_VI0_R7, FN_GLO_RFON_B, FN_RX1_C, FN_CAN0_RX_E,
	FN_SDA4_B, FN_HRX1_D, FN_SCIFB0_RXD_D,
	FN_VI1_HSYNC_N, FN_AVB_RXD0, FN_TS_SDATA0_B, FN_TX4_B, FN_SCIFA4_TXD_B,
	FN_VI1_VSYNC_N, FN_AVB_RXD1, FN_TS_SCK0_B, FN_RX4_B, FN_SCIFA4_RXD_B,
	FN_VI1_CLKENB, FN_AVB_RXD2, FN_TS_SDEN0_B,
	FN_VI1_FIELD, FN_AVB_RXD3, FN_TS_SPSYNC0_B,
	FN_VI1_CLK, FN_AVB_RXD4, FN_VI1_DATA0, FN_AVB_RXD5,
	FN_VI1_DATA1, FN_AVB_RXD6, FN_VI1_DATA2, FN_AVB_RXD7,
	FN_VI1_DATA3, FN_AVB_RX_ER, FN_VI1_DATA4, FN_AVB_MDIO,
	FN_VI1_DATA5, FN_AVB_RX_DV, FN_VI1_DATA6, FN_AVB_MAGIC,
	FN_VI1_DATA7, FN_AVB_MDC,
	FN_ETH_MDIO, FN_AVB_RX_CLK, FN_SCL2_C,
	FN_ETH_CRS_DV, FN_AVB_LINK, FN_SDA2_C,

	/* IPSR12 */
	FN_ETH_RX_ER, FN_AVB_CRS, FN_SCL3, FN_SCL7,
	FN_ETH_RXD0, FN_AVB_PHY_INT, FN_SDA3, FN_SDA7,
	FN_ETH_RXD1, FN_AVB_GTXREFCLK, FN_CAN0_TX_C,
	FN_SCL2_D, FN_MSIOF1_RXD_E,
	FN_ETH_LINK, FN_AVB_TXD0, FN_CAN0_RX_C, FN_SDA2_D, FN_MSIOF1_SCK_E,
	FN_ETH_REFCLK, FN_AVB_TXD1, FN_SCIFA3_RXD_B,
	FN_CAN1_RX_C, FN_MSIOF1_SYNC_E,
	FN_ETH_TXD1, FN_AVB_TXD2, FN_SCIFA3_TXD_B,
	FN_CAN1_TX_C, FN_MSIOF1_TXD_E,
	FN_ETH_TX_EN, FN_AVB_TXD3, FN_TCLK1_B, FN_CAN_CLK_B,
	FN_ETH_MAGIC, FN_AVB_TXD4, FN_IETX_C,
	FN_ETH_TXD0, FN_AVB_TXD5, FN_IECLK_C,
	FN_ETH_MDC, FN_AVB_TXD6, FN_IERX_C,
	FN_STP_IVCXO27_0, FN_AVB_TXD7, FN_SCIFB2_TXD_D,
	FN_ADIDATA_B, FN_MSIOF0_SYNC_C,
	FN_STP_ISCLK_0, FN_AVB_TX_EN, FN_SCIFB2_RXD_D,
	FN_ADICS_SAMP_B, FN_MSIOF0_SCK_C,

	/* IPSR13 */
	FN_STP_ISD_0, FN_AVB_TX_ER, FN_SCIFB2_SCK_C,
	FN_ADICLK_B, FN_MSIOF0_SS1_C,
	FN_STP_ISEN_0, FN_AVB_TX_CLK, FN_ADICHS0_B, FN_MSIOF0_SS2_C,
	FN_STP_ISSYNC_0, FN_AVB_COL, FN_ADICHS1_B, FN_MSIOF0_RXD_C,
	FN_STP_OPWM_0, FN_AVB_GTX_CLK, FN_PWM0_B,
	FN_ADICHS2_B, FN_MSIOF0_TXD_C,
	FN_SD0_CLK, FN_SPCLK_B, FN_SD0_CMD, FN_MOSI_IO0_B,
	FN_SD0_DATA0, FN_MISO_IO1_B, FN_SD0_DATA1, FN_IO2_B,
	FN_SD0_DATA2, FN_IO3_B, FN_SD0_DATA3, FN_SSL_B,
	FN_SD0_CD, FN_MMC_D6_B, FN_SIM0_RST_B, FN_CAN0_RX_F,
	FN_SCIFA5_TXD_B, FN_TX3_C,
	FN_SD0_WP, FN_MMC_D7_B, FN_SIM0_D_B, FN_CAN0_TX_F,
	FN_SCIFA5_RXD_B, FN_RX3_C,
	FN_SD1_CMD, FN_REMOCON_B, FN_SD1_DATA0, FN_SPEEDIN_B,
	FN_SD1_DATA1, FN_IETX_B, FN_SD1_DATA2, FN_IECLK_B,
	FN_SD1_DATA3, FN_IERX_B,
	FN_SD1_CD, FN_PWM0, FN_TPU_TO0, FN_SCL1_C,

	/* IPSR14 */
	FN_SD1_WP, FN_PWM1_B, FN_SDA1_C,
	FN_SD2_CLK, FN_MMC_CLK, FN_SD2_CMD, FN_MMC_CMD,
	FN_SD2_DATA0, FN_MMC_D0, FN_SD2_DATA1, FN_MMC_D1,
	FN_SD2_DATA2, FN_MMC_D2, FN_SD2_DATA3, FN_MMC_D3,
	FN_SD2_CD, FN_MMC_D4, FN_SCL8_C, FN_TX5_B, FN_SCIFA5_TXD_C,
	FN_SD2_WP, FN_MMC_D5, FN_SDA8_C, FN_RX5_B, FN_SCIFA5_RXD_C,
	FN_MSIOF0_SCK, FN_RX2_C, FN_ADIDATA, FN_VI1_CLK_C, FN_VI1_G0_B,
	FN_MSIOF0_SYNC, FN_TX2_C, FN_ADICS_SAMP, FN_VI1_CLKENB_C, FN_VI1_G1_B,
	FN_MSIOF0_TXD, FN_ADICLK, FN_VI1_FIELD_C, FN_VI1_G2_B,
	FN_MSIOF0_RXD, FN_ADICHS0, FN_VI1_DATA0_C, FN_VI1_G3_B,
	FN_MSIOF0_SS1, FN_MMC_D6, FN_ADICHS1, FN_TX0_E,
	FN_VI1_HSYNC_N_C, FN_SCL7_C, FN_VI1_G4_B,
	FN_MSIOF0_SS2, FN_MMC_D7, FN_ADICHS2, FN_RX0_E,
	FN_VI1_VSYNC_N_C, FN_SDA7_C, FN_VI1_G5_B,

	/* IPSR15 */
	FN_SIM0_RST, FN_IETX, FN_CAN1_TX_D,
	FN_SIM0_CLK, FN_IECLK, FN_CAN_CLK_C,
	FN_SIM0_D, FN_IERX, FN_CAN1_RX_D,
	FN_GPS_CLK, FN_DU1_DOTCLKIN_C, FN_AUDIO_CLKB_B,
	FN_PWM5_B, FN_SCIFA3_TXD_C,
	FN_GPS_SIGN, FN_TX4_C, FN_SCIFA4_TXD_C, FN_PWM5,
	FN_VI1_G6_B, FN_SCIFA3_RXD_C,
	FN_GPS_MAG, FN_RX4_C, FN_SCIFA4_RXD_C, FN_PWM6,
	FN_VI1_G7_B, FN_SCIFA3_SCK_C,
	FN_HCTS0_N, FN_SCIFB0_CTS_N, FN_GLO_I0_C, FN_TCLK1, FN_VI1_DATA1_C,
	FN_HRTS0_N, FN_SCIFB0_RTS_N, FN_GLO_I1_C, FN_VI1_DATA2_C,
	FN_HSCK0, FN_SCIFB0_SCK, FN_GLO_Q0_C, FN_CAN_CLK,
	FN_TCLK2, FN_VI1_DATA3_C,
	FN_HRX0, FN_SCIFB0_RXD, FN_GLO_Q1_C, FN_CAN0_RX_B, FN_VI1_DATA4_C,
	FN_HTX0, FN_SCIFB0_TXD, FN_GLO_SCLK_C, FN_CAN0_TX_B, FN_VI1_DATA5_C,

	/* IPSR16 */
	FN_HRX1, FN_SCIFB1_RXD, FN_VI1_R0_B, FN_GLO_SDATA_C, FN_VI1_DATA6_C,
	FN_HTX1, FN_SCIFB1_TXD, FN_VI1_R1_B, FN_GLO_SS_C, FN_VI1_DATA7_C,
	FN_HSCK1, FN_SCIFB1_SCK, FN_MLB_CK, FN_GLO_RFON_C,
	FN_HCTS1_N, FN_SCIFB1_CTS_N, FN_MLB_SIG, FN_CAN1_TX_B,
	FN_HRTS1_N, FN_SCIFB1_RTS_N, FN_MLB_DAT, FN_CAN1_RX_B,

	/* MOD_SEL */
	FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, FN_SEL_SCIF1_3,
	FN_SEL_SCIFB_0, FN_SEL_SCIFB_1, FN_SEL_SCIFB_2, FN_SEL_SCIFB_3,
	FN_SEL_SCIFB2_0, FN_SEL_SCIFB2_1, FN_SEL_SCIFB2_2, FN_SEL_SCIFB2_3,
	FN_SEL_SCIFB1_0, FN_SEL_SCIFB1_1, FN_SEL_SCIFB1_2, FN_SEL_SCIFB1_3,
	FN_SEL_SCIFA1_0, FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2,
	FN_SEL_SSI9_0, FN_SEL_SSI9_1,
	FN_SEL_SCFA_0, FN_SEL_SCFA_1,
	FN_SEL_QSP_0, FN_SEL_QSP_1,
	FN_SEL_SSI7_0, FN_SEL_SSI7_1,
	FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1, FN_SEL_HSCIF1_2, FN_SEL_HSCIF1_3,
	FN_SEL_HSCIF1_4,
	FN_SEL_VI1_0, FN_SEL_VI1_1, FN_SEL_VI1_2,
	FN_SEL_TMU1_0, FN_SEL_TMU1_1,
	FN_SEL_LBS_0, FN_SEL_LBS_1, FN_SEL_LBS_2, FN_SEL_LBS_3,
	FN_SEL_TSIF0_0, FN_SEL_TSIF0_1, FN_SEL_TSIF0_2, FN_SEL_TSIF0_3,
	FN_SEL_SOF0_0, FN_SEL_SOF0_1, FN_SEL_SOF0_2,

	/* MOD_SEL2 */
	FN_SEL_SCIF0_0, FN_SEL_SCIF0_1, FN_SEL_SCIF0_2, FN_SEL_SCIF0_3,
	FN_SEL_SCIF0_4,
	FN_SEL_SCIF_0, FN_SEL_SCIF_1,
	FN_SEL_CAN0_0, FN_SEL_CAN0_1, FN_SEL_CAN0_2, FN_SEL_CAN0_3,
	FN_SEL_CAN0_4, FN_SEL_CAN0_5,
	FN_SEL_CAN1_0, FN_SEL_CAN1_1, FN_SEL_CAN1_2, FN_SEL_CAN1_3,
	FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1,
	FN_SEL_SCIF4_0, FN_SEL_SCIF4_1, FN_SEL_SCIF4_2,
	FN_SEL_ADG_0, FN_SEL_ADG_1,
	FN_SEL_FM_0, FN_SEL_FM_1, FN_SEL_FM_2, FN_SEL_FM_3, FN_SEL_FM_4,
	FN_SEL_SCIFA5_0, FN_SEL_SCIFA5_1, FN_SEL_SCIFA5_2,
	FN_SEL_GPS_0, FN_SEL_GPS_1, FN_SEL_GPS_2, FN_SEL_GPS_3,
	FN_SEL_SCIFA4_0, FN_SEL_SCIFA4_1, FN_SEL_SCIFA4_2,
	FN_SEL_SCIFA3_0, FN_SEL_SCIFA3_1, FN_SEL_SCIFA3_2,
	FN_SEL_SIM_0, FN_SEL_SIM_1,
	FN_SEL_SSI8_0, FN_SEL_SSI8_1,

	/* MOD_SEL3 */
	FN_SEL_HSCIF2_0, FN_SEL_HSCIF2_1, FN_SEL_HSCIF2_2, FN_SEL_HSCIF2_3,
	FN_SEL_CANCLK_0, FN_SEL_CANCLK_1, FN_SEL_CANCLK_2, FN_SEL_CANCLK_3,
	FN_SEL_IIC8_0, FN_SEL_IIC8_1, FN_SEL_IIC8_2,
	FN_SEL_IIC7_0, FN_SEL_IIC7_1, FN_SEL_IIC7_2,
	FN_SEL_IIC4_0, FN_SEL_IIC4_1, FN_SEL_IIC4_2,
	FN_SEL_IIC3_0, FN_SEL_IIC3_1, FN_SEL_IIC3_2, FN_SEL_IIC3_3,
	FN_SEL_SCIF3_0, FN_SEL_SCIF3_1, FN_SEL_SCIF3_2, FN_SEL_SCIF3_3,
	FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2,
	FN_SEL_MMC_0, FN_SEL_MMC_1,
	FN_SEL_SCIF5_0, FN_SEL_SCIF5_1,
	FN_SEL_IIC2_0, FN_SEL_IIC2_1, FN_SEL_IIC2_2, FN_SEL_IIC2_3,
	FN_SEL_IIC1_0, FN_SEL_IIC1_1, FN_SEL_IIC1_2, FN_SEL_IIC1_3,
	FN_SEL_IIC1_4,
	FN_SEL_IIC0_0, FN_SEL_IIC0_1, FN_SEL_IIC0_2,

	/* MOD_SEL4 */
	FN_SEL_SOF1_0, FN_SEL_SOF1_1, FN_SEL_SOF1_2, FN_SEL_SOF1_3,
	FN_SEL_SOF1_4,
	FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1, FN_SEL_HSCIF0_2,
	FN_SEL_DIS_0, FN_SEL_DIS_1, FN_SEL_DIS_2,
	FN_SEL_RAD_0, FN_SEL_RAD_1,
	FN_SEL_RCN_0, FN_SEL_RCN_1,
	FN_SEL_RSP_0, FN_SEL_RSP_1,
	FN_SEL_SCIF2_0, FN_SEL_SCIF2_1, FN_SEL_SCIF2_2, FN_SEL_SCIF2_3,
	FN_SEL_SCIF2_4,
	FN_SEL_SOF2_0, FN_SEL_SOF2_1, FN_SEL_SOF2_2, FN_SEL_SOF2_3,
	FN_SEL_SOF2_4,
	FN_SEL_SSI1_0, FN_SEL_SSI1_1,
	FN_SEL_SSI0_0, FN_SEL_SSI0_1,
	FN_SEL_SSP_0, FN_SEL_SSP_1, FN_SEL_SSP_2,
	PINMUX_FUNCTION_END,

	PINMUX_MARK_BEGIN,

	EX_CS0_N_MARK, RD_N_MARK,

	AUDIO_CLKA_MARK,

	VI0_CLK_MARK, VI0_DATA0_VI0_B0_MARK, VI0_DATA0_VI0_B1_MARK,
	VI0_DATA0_VI0_B2_MARK, VI0_DATA0_VI0_B4_MARK, VI0_DATA0_VI0_B5_MARK,
	VI0_DATA0_VI0_B6_MARK, VI0_DATA0_VI0_B7_MARK,

	USB0_PWEN_MARK, USB0_OVC_MARK, USB1_PWEN_MARK,

	/* IPSR0 - 5 */

	/* IPSR6 */
	AUDIO_CLKB_MARK, STP_OPWM_0_B_MARK, MSIOF1_SCK_B_MARK,
	SCIF_CLK_MARK, BPFCLK_E_MARK,
	AUDIO_CLKC_MARK, SCIFB0_SCK_C_MARK, MSIOF1_SYNC_B_MARK, RX2_MARK,
	SCIFA2_RXD_MARK, FMIN_E_MARK,
	AUDIO_CLKOUT_MARK, MSIOF1_SS1_B_MARK, TX2_MARK, SCIFA2_TXD_MARK,
	IRQ0_MARK, SCIFB1_RXD_D_MARK, INTC_IRQ0_N_MARK,
	IRQ1_MARK, SCIFB1_SCK_C_MARK, INTC_IRQ1_N_MARK,
	IRQ2_MARK, SCIFB1_TXD_D_MARK, INTC_IRQ2_N_MARK,
	IRQ3_MARK, SCL4_C_MARK, MSIOF2_TXD_E_MARK, INTC_IRQ3_N_MARK,
	IRQ4_MARK, HRX1_C_MARK, SDA4_C_MARK,
	MSIOF2_RXD_E_MARK, INTC_IRQ4_N_MARK,
	IRQ5_MARK, HTX1_C_MARK, SCL1_E_MARK, MSIOF2_SCK_E_MARK,
	IRQ6_MARK, HSCK1_C_MARK, MSIOF1_SS2_B_MARK,
	SDA1_E_MARK, MSIOF2_SYNC_E_MARK,
	IRQ7_MARK, HCTS1_N_C_MARK, MSIOF1_TXD_B_MARK,
	GPS_CLK_C_MARK, GPS_CLK_D_MARK,
	IRQ8_MARK, HRTS1_N_C_MARK, MSIOF1_RXD_B_MARK,
	GPS_SIGN_C_MARK, GPS_SIGN_D_MARK,

	/* IPSR7 - 10 */

	/* IPSR11 */
	VI0_R5_MARK, VI2_DATA6_MARK, GLO_SDATA_B_MARK, RX0_C_MARK, SDA1_D_MARK,
	VI0_R6_MARK, VI2_DATA7_MARK, GLO_SS_B_MARK, TX1_C_MARK, SCL4_B_MARK,
	VI0_R7_MARK, GLO_RFON_B_MARK, RX1_C_MARK, CAN0_RX_E_MARK,
	SDA4_B_MARK, HRX1_D_MARK, SCIFB0_RXD_D_MARK,
	VI1_HSYNC_N_MARK, AVB_RXD0_MARK, TS_SDATA0_B_MARK,
	TX4_B_MARK, SCIFA4_TXD_B_MARK,
	VI1_VSYNC_N_MARK, AVB_RXD1_MARK, TS_SCK0_B_MARK,
	RX4_B_MARK, SCIFA4_RXD_B_MARK,
	VI1_CLKENB_MARK, AVB_RXD2_MARK, TS_SDEN0_B_MARK,
	VI1_FIELD_MARK, AVB_RXD3_MARK, TS_SPSYNC0_B_MARK,
	VI1_CLK_MARK, AVB_RXD4_MARK, VI1_DATA0_MARK, AVB_RXD5_MARK,
	VI1_DATA1_MARK, AVB_RXD6_MARK, VI1_DATA2_MARK, AVB_RXD7_MARK,
	VI1_DATA3_MARK, AVB_RX_ER_MARK, VI1_DATA4_MARK, AVB_MDIO_MARK,
	VI1_DATA5_MARK, AVB_RX_DV_MARK, VI1_DATA6_MARK, AVB_MAGIC_MARK,
	VI1_DATA7_MARK, AVB_MDC_MARK,
	ETH_MDIO_MARK, AVB_RX_CLK_MARK, SCL2_C_MARK,
	ETH_CRS_DV_MARK, AVB_LINK_MARK, SDA2_C_MARK,

	/* IPSR12 */
	ETH_RX_ER_MARK, AVB_CRS_MARK, SCL3_MARK, SCL7_MARK,
	ETH_RXD0_MARK, AVB_PHY_INT_MARK, SDA3_MARK, SDA7_MARK,
	ETH_RXD1_MARK, AVB_GTXREFCLK_MARK, CAN0_TX_C_MARK,
	SCL2_D_MARK, MSIOF1_RXD_E_MARK,
	ETH_LINK_MARK, AVB_TXD0_MARK, CAN0_RX_C_MARK,
	SDA2_D_MARK, MSIOF1_SCK_E_MARK,
	ETH_REFCLK_MARK, AVB_TXD1_MARK, SCIFA3_RXD_B_MARK,
	CAN1_RX_C_MARK, MSIOF1_SYNC_E_MARK,
	ETH_TXD1_MARK, AVB_TXD2_MARK, SCIFA3_TXD_B_MARK,
	CAN1_TX_C_MARK, MSIOF1_TXD_E_MARK,
	ETH_TX_EN_MARK, AVB_TXD3_MARK, TCLK1_B_MARK, CAN_CLK_B_MARK,
	ETH_MAGIC_MARK, AVB_TXD4_MARK, IETX_C_MARK,
	ETH_TXD0_MARK, AVB_TXD5_MARK, IECLK_C_MARK,
	ETH_MDC_MARK, AVB_TXD6_MARK, IERX_C_MARK,
	STP_IVCXO27_0_MARK, AVB_TXD7_MARK, SCIFB2_TXD_D_MARK,
	ADIDATA_B_MARK, MSIOF0_SYNC_C_MARK,
	STP_ISCLK_0_MARK, AVB_TX_EN_MARK, SCIFB2_RXD_D_MARK,
	ADICS_SAMP_B_MARK, MSIOF0_SCK_C_MARK,

	/* IPSR13 */
	STP_ISD_0_MARK, AVB_TX_ER_MARK, SCIFB2_SCK_C_MARK,
	ADICLK_B_MARK, MSIOF0_SS1_C_MARK,
	STP_ISEN_0_MARK, AVB_TX_CLK_MARK, ADICHS0_B_MARK, MSIOF0_SS2_C_MARK,
	STP_ISSYNC_0_MARK, AVB_COL_MARK, ADICHS1_B_MARK, MSIOF0_RXD_C_MARK,
	STP_OPWM_0_MARK, AVB_GTX_CLK_MARK, PWM0_B_MARK,
	ADICHS2_B_MARK, MSIOF0_TXD_C_MARK,
	SD0_CLK_MARK, SPCLK_B_MARK, SD0_CMD_MARK, MOSI_IO0_B_MARK,
	SD0_DATA0_MARK, MISO_IO1_B_MARK, SD0_DATA1_MARK, IO2_B_MARK,
	SD0_DATA2_MARK, IO3_B_MARK, SD0_DATA3_MARK, SSL_B_MARK,
	SD0_CD_MARK, MMC_D6_B_MARK, SIM0_RST_B_MARK, CAN0_RX_F_MARK,
	SCIFA5_TXD_B_MARK, TX3_C_MARK,
	SD0_WP_MARK, MMC_D7_B_MARK, SIM0_D_B_MARK, CAN0_TX_F_MARK,
	SCIFA5_RXD_B_MARK, RX3_C_MARK,
	SD1_CMD_MARK, REMOCON_B_MARK, SD1_DATA0_MARK, SPEEDIN_B_MARK,
	SD1_DATA1_MARK, IETX_B_MARK, SD1_DATA2_MARK, IECLK_B_MARK,
	SD1_DATA3_MARK, IERX_B_MARK,
	SD1_CD_MARK, PWM0_MARK, TPU_TO0_MARK, SCL1_C_MARK,

	/* IPSR14 */
	SD1_WP_MARK, PWM1_B_MARK, SDA1_C_MARK,
	SD2_CLK_MARK, MMC_CLK_MARK, SD2_CMD_MARK, MMC_CMD_MARK,
	SD2_DATA0_MARK, MMC_D0_MARK, SD2_DATA1_MARK, MMC_D1_MARK,
	SD2_DATA2_MARK, MMC_D2_MARK, SD2_DATA3_MARK, MMC_D3_MARK,
	SD2_CD_MARK, MMC_D4_MARK, SCL8_C_MARK, TX5_B_MARK, SCIFA5_TXD_C_MARK,
	SD2_WP_MARK, MMC_D5_MARK, SDA8_C_MARK, RX5_B_MARK, SCIFA5_RXD_C_MARK,
	MSIOF0_SCK_MARK, RX2_C_MARK, ADIDATA_MARK,
	VI1_CLK_C_MARK, VI1_G0_B_MARK,
	MSIOF0_SYNC_MARK, TX2_C_MARK, ADICS_SAMP_MARK,
	VI1_CLKENB_C_MARK, VI1_G1_B_MARK,
	MSIOF0_TXD_MARK, ADICLK_MARK, VI1_FIELD_C_MARK, VI1_G2_B_MARK,
	MSIOF0_RXD_MARK, ADICHS0_MARK, VI1_DATA0_C_MARK, VI1_G3_B_MARK,
	MSIOF0_SS1_MARK, MMC_D6_MARK, ADICHS1_MARK, TX0_E_MARK,
	VI1_HSYNC_N_C_MARK, SCL7_C_MARK, VI1_G4_B_MARK,
	MSIOF0_SS2_MARK, MMC_D7_MARK, ADICHS2_MARK, RX0_E_MARK,
	VI1_VSYNC_N_C_MARK, SDA7_C_MARK, VI1_G5_B_MARK,

	/* IPSR15 */
	SIM0_RST_MARK, IETX_MARK, CAN1_TX_D_MARK,
	SIM0_CLK_MARK, IECLK_MARK, CAN_CLK_C_MARK,
	SIM0_D_MARK, IERX_MARK, CAN1_RX_D_MARK,
	GPS_CLK_MARK, DU1_DOTCLKIN_C_MARK, AUDIO_CLKB_B_MARK,
	PWM5_B_MARK, SCIFA3_TXD_C_MARK,
	GPS_SIGN_MARK, TX4_C_MARK, SCIFA4_TXD_C_MARK, PWM5_MARK,
	VI1_G6_B_MARK, SCIFA3_RXD_C_MARK,
	GPS_MAG_MARK, RX4_C_MARK, SCIFA4_RXD_C_MARK, PWM6_MARK,
	VI1_G7_B_MARK, SCIFA3_SCK_C_MARK,
	HCTS0_N_MARK, SCIFB0_CTS_N_MARK, GLO_I0_C_MARK,
	TCLK1_MARK, VI1_DATA1_C_MARK,
	HRTS0_N_MARK, SCIFB0_RTS_N_MARK, GLO_I1_C_MARK, VI1_DATA2_C_MARK,
	HSCK0_MARK, SCIFB0_SCK_MARK, GLO_Q0_C_MARK, CAN_CLK_MARK,
	TCLK2_MARK, VI1_DATA3_C_MARK,
	HRX0_MARK, SCIFB0_RXD_MARK, GLO_Q1_C_MARK,
	CAN0_RX_B_MARK, VI1_DATA4_C_MARK,
	HTX0_MARK, SCIFB0_TXD_MARK, GLO_SCLK_C_MARK,
	CAN0_TX_B_MARK, VI1_DATA5_C_MARK,

	/* IPSR16 */
	HRX1_MARK, SCIFB1_RXD_MARK, VI1_R0_B_MARK,
	GLO_SDATA_C_MARK, VI1_DATA6_C_MARK,
	HTX1_MARK, SCIFB1_TXD_MARK, VI1_R1_B_MARK,
	GLO_SS_C_MARK, VI1_DATA7_C_MARK,
	HSCK1_MARK, SCIFB1_SCK_MARK, MLB_CK_MARK, GLO_RFON_C_MARK,
	HCTS1_N_MARK, SCIFB1_CTS_N_MARK, MLB_SIG_MARK, CAN1_TX_B_MARK,
	HRTS1_N_MARK, SCIFB1_RTS_N_MARK, MLB_DAT_MARK, CAN1_RX_B_MARK,
	PINMUX_MARK_END,
};

static pinmux_enum_t pinmux_data[] = {
	PINMUX_DATA_GP_ALL(), /* PINMUX_DATA(GP_M_N_DATA, GP_M_N_FN...), */

	PINMUX_DATA(EX_CS0_N_MARK, FN_EX_CS0_N),
	PINMUX_DATA(RD_N_MARK, FN_RD_N),
	PINMUX_DATA(AUDIO_CLKA_MARK, FN_AUDIO_CLKA),
	PINMUX_DATA(VI0_CLK_MARK, FN_VI0_CLK),
	PINMUX_DATA(VI0_DATA0_VI0_B0_MARK, FN_VI0_DATA0_VI0_B0),
	PINMUX_DATA(VI0_DATA0_VI0_B1_MARK, FN_VI0_DATA0_VI0_B1),
	PINMUX_DATA(VI0_DATA0_VI0_B2_MARK, FN_VI0_DATA0_VI0_B2),
	PINMUX_DATA(VI0_DATA0_VI0_B4_MARK, FN_VI0_DATA0_VI0_B4),
	PINMUX_DATA(VI0_DATA0_VI0_B5_MARK, FN_VI0_DATA0_VI0_B5),
	PINMUX_DATA(VI0_DATA0_VI0_B6_MARK, FN_VI0_DATA0_VI0_B6),
	PINMUX_DATA(VI0_DATA0_VI0_B7_MARK, FN_VI0_DATA0_VI0_B7),
	PINMUX_DATA(USB0_PWEN_MARK, FN_USB0_PWEN),
	PINMUX_DATA(USB0_OVC_MARK, FN_USB0_OVC),
	PINMUX_DATA(USB1_PWEN_MARK, FN_USB1_PWEN),

	/* IPSR0 - 5 */

	/* IPSR6 */
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, AUDIO_CLKB, SEL_ADG_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, STP_OPWM_0_B, SEL_SSP_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, MSIOF1_SCK_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, SCIF_CLK, SEL_SCIF_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_2_0, BPFCLK_E, SEL_FM_4),
	PINMUX_IPSR_DATA(IP6_5_3, AUDIO_CLKC),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, SCIFB0_SCK_C, SEL_SCIFB_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, MSIOF1_SYNC_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, RX2, SEL_SCIF2_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, SCIFA2_RXD, SEL_SCIFA2_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, FMIN_E, SEL_FM_4),
	PINMUX_IPSR_DATA(IP6_7_6, AUDIO_CLKOUT),
	PINMUX_IPSR_MODSEL_DATA(IP6_7_6, MSIOF1_SS1_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_5_3, TX2, SEL_SCIF2_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_7_6, SCIFA2_TXD, SEL_SCIFA2_0),
	PINMUX_IPSR_DATA(IP6_9_8, IRQ0),
	PINMUX_IPSR_MODSEL_DATA(IP6_9_8, SCIFB1_RXD_D, SEL_SCIFB1_3),
	PINMUX_IPSR_DATA(IP6_9_8, INTC_IRQ0_N),
	PINMUX_IPSR_DATA(IP6_11_10, IRQ1),
	PINMUX_IPSR_MODSEL_DATA(IP6_11_10, SCIFB1_SCK_C, SEL_SCIFB1_2),
	PINMUX_IPSR_DATA(IP6_11_10, INTC_IRQ1_N),
	PINMUX_IPSR_DATA(IP6_13_12, IRQ2),
	PINMUX_IPSR_MODSEL_DATA(IP6_13_12, SCIFB1_TXD_D, SEL_SCIFB1_3),
	PINMUX_IPSR_DATA(IP6_13_12, INTC_IRQ2_N),
	PINMUX_IPSR_DATA(IP6_15_14, IRQ3),
	PINMUX_IPSR_MODSEL_DATA(IP6_15_14, SCL4_C, SEL_IIC4_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_15_14, MSIOF2_TXD_E, SEL_SOF2_4),
	PINMUX_IPSR_DATA(IP6_15_14, INTC_IRQ4_N),
	PINMUX_IPSR_DATA(IP6_18_16, IRQ4),
	PINMUX_IPSR_MODSEL_DATA(IP6_18_16, HRX1_C, SEL_HSCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_18_16, SDA4_C, SEL_IIC4_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_18_16, MSIOF2_RXD_E, SEL_SOF2_4),
	PINMUX_IPSR_DATA(IP6_18_16, INTC_IRQ4_N),
	PINMUX_IPSR_DATA(IP6_20_19, IRQ5),
	PINMUX_IPSR_MODSEL_DATA(IP6_20_19, HTX1_C, SEL_HSCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_20_19, SCL1_E, SEL_IIC1_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_20_19, MSIOF2_SCK_E, SEL_SOF2_4),
	PINMUX_IPSR_DATA(IP6_23_21, IRQ6),
	PINMUX_IPSR_MODSEL_DATA(IP6_23_21, HSCK1_C, SEL_HSCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_23_21, MSIOF1_SS2_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_23_21, SDA1_E, SEL_IIC1_4),
	PINMUX_IPSR_MODSEL_DATA(IP6_23_21, MSIOF2_SYNC_E, SEL_SOF2_4),
	PINMUX_IPSR_DATA(IP6_26_24, IRQ7),
	PINMUX_IPSR_MODSEL_DATA(IP6_26_24, HCTS1_N_C, SEL_HSCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_26_24, MSIOF1_TXD_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_26_24, GPS_CLK_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_26_24, GPS_CLK_D, SEL_GPS_3),
	PINMUX_IPSR_DATA(IP6_29_27, IRQ8),
	PINMUX_IPSR_MODSEL_DATA(IP6_29_27, HRTS1_N_C, SEL_HSCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_29_27, MSIOF1_RXD_B, SEL_SOF1_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_29_27, GPS_SIGN_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_29_27, GPS_SIGN_D, SEL_GPS_3),

	/* IPSR7 - 10 */

	/* IPSR11 */
	PINMUX_IPSR_DATA(IP11_2_0, VI0_R5),
	PINMUX_IPSR_DATA(IP11_2_0, VI2_DATA6),
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, GLO_SDATA_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, RX0_C, SEL_SCIF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, SDA1_D, SEL_IIC1_3),
	PINMUX_IPSR_DATA(IP11_5_3, VI0_R6),
	PINMUX_IPSR_DATA(IP11_5_3, VI2_DATA7),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, GLO_SS_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, TX1_C, SEL_SCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, SCL4_B, SEL_IIC4_1),
	PINMUX_IPSR_DATA(IP11_8_6, VI0_R7),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, GLO_RFON_B, SEL_GPS_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, RX1_C, SEL_SCIF1_2),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, CAN0_RX_E, SEL_CAN0_4),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, SDA4_B, SEL_IIC4_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, HRX1_D, SEL_HSCIF1_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_8_6, SCIFB0_RXD_D, SEL_SCIFB_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_11_9, VI1_HSYNC_N, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_11_9, AVB_RXD0),
	PINMUX_IPSR_MODSEL_DATA(IP11_11_9, TS_SDATA0_B, SEL_TSIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_11_9, TX4_B, SEL_SCIF4_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_11_9, SCIFA4_TXD_B, SEL_SCIFA4_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_14_12, VI1_VSYNC_N, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_14_12, AVB_RXD1),
	PINMUX_IPSR_MODSEL_DATA(IP11_14_12, TS_SCK0_B, SEL_TSIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_14_12, RX4_B, SEL_SCIF4_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_14_12, SCIFA4_RXD_B, SEL_SCIFA4_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_16_15, VI1_CLKENB, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_16_15, AVB_RXD2),
	PINMUX_IPSR_MODSEL_DATA(IP11_16_15, TS_SDEN0_B, SEL_TSIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_18_17, VI1_FIELD, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_18_17, AVB_RXD3),
	PINMUX_IPSR_MODSEL_DATA(IP11_18_17, TS_SPSYNC0_B, SEL_TSIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_19, VI1_CLK, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_19, AVB_RXD4),
	PINMUX_IPSR_MODSEL_DATA(IP11_20, VI1_DATA0, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_20, AVB_RXD5),
	PINMUX_IPSR_MODSEL_DATA(IP11_21, VI1_DATA1, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_21, AVB_RXD6),
	PINMUX_IPSR_MODSEL_DATA(IP11_22, VI1_DATA2, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_22, AVB_RXD7),
	PINMUX_IPSR_MODSEL_DATA(IP11_23, VI1_DATA3, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_23, AVB_RX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP11_24, VI1_DATA4, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_24, AVB_MDIO),
	PINMUX_IPSR_MODSEL_DATA(IP11_25, VI1_DATA5, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_25, AVB_RX_DV),
	PINMUX_IPSR_MODSEL_DATA(IP11_26, VI1_DATA6, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_26, AVB_MAGIC),
	PINMUX_IPSR_MODSEL_DATA(IP11_27, VI1_DATA7, SEL_VI1_0),
	PINMUX_IPSR_DATA(IP11_27, AVB_MDC),
	PINMUX_IPSR_DATA(IP11_29_28, ETH_MDIO),
	PINMUX_IPSR_DATA(IP11_29_28, AVB_RX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_28, SCL2_C, SEL_IIC2_2),
	PINMUX_IPSR_DATA(IP11_31_30, ETH_CRS_DV),
	PINMUX_IPSR_DATA(IP11_31_30, AVB_LINK),
	PINMUX_IPSR_MODSEL_DATA(IP11_31_30, SDA2_C, SEL_IIC2_2),

	/* IPSR12 */
	PINMUX_IPSR_DATA(IP12_1_0, ETH_RX_ER),
	PINMUX_IPSR_DATA(IP12_1_0, AVB_CRS),
	PINMUX_IPSR_MODSEL_DATA(IP12_1_0, SCL3, SEL_IIC3_0),
	PINMUX_IPSR_MODSEL_DATA(IP12_1_0, SCL7, SEL_IIC7_0),
	PINMUX_IPSR_DATA(IP12_3_2, ETH_RXD0),
	PINMUX_IPSR_DATA(IP12_3_2, AVB_PHY_INT),
	PINMUX_IPSR_MODSEL_DATA(IP12_3_2, SDA3, SEL_IIC3_0),
	PINMUX_IPSR_MODSEL_DATA(IP12_3_2, SDA7, SEL_IIC7_0),
	PINMUX_IPSR_DATA(IP12_6_4, ETH_RXD1),
	PINMUX_IPSR_DATA(IP12_6_4, AVB_GTXREFCLK),
	PINMUX_IPSR_MODSEL_DATA(IP12_6_4, CAN0_TX_C, SEL_CAN0_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_6_4, SCL2_D, SEL_IIC2_3),
	PINMUX_IPSR_MODSEL_DATA(IP12_6_4, MSIOF1_RXD_E, SEL_SOF1_4),
	PINMUX_IPSR_DATA(IP12_9_7, ETH_LINK),
	PINMUX_IPSR_DATA(IP12_9_7, AVB_TXD0),
	PINMUX_IPSR_MODSEL_DATA(IP12_9_7, CAN0_RX_C, SEL_CAN0_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_9_7, SDA2_D, SEL_IIC2_3),
	PINMUX_IPSR_MODSEL_DATA(IP12_9_7, MSIOF1_SCK_E, SEL_SOF1_4),
	PINMUX_IPSR_DATA(IP12_12_10, ETH_REFCLK),
	PINMUX_IPSR_DATA(IP12_12_10, AVB_TXD1),
	PINMUX_IPSR_MODSEL_DATA(IP12_12_10, SCIFA3_RXD_B, SEL_SCIFA3_1),
	PINMUX_IPSR_MODSEL_DATA(IP12_12_10, CAN1_RX_C, SEL_CAN1_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_12_10, MSIOF1_SYNC_E, SEL_SOF1_4),
	PINMUX_IPSR_DATA(IP12_15_13, ETH_TXD1),
	PINMUX_IPSR_DATA(IP12_15_13, AVB_TXD2),
	PINMUX_IPSR_MODSEL_DATA(IP12_15_13, SCIFA3_TXD_B, SEL_SCIFA3_1),
	PINMUX_IPSR_MODSEL_DATA(IP12_15_13, CAN1_TX_C, SEL_CAN1_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_15_13, MSIOF1_TXD_E, SEL_SOF1_4),
	PINMUX_IPSR_DATA(IP12_17_16, ETH_TX_EN),
	PINMUX_IPSR_DATA(IP12_17_16, AVB_TXD3),
	PINMUX_IPSR_MODSEL_DATA(IP12_17_16, TCLK1_B, SEL_TMU1_0),
	PINMUX_IPSR_MODSEL_DATA(IP12_17_16, CAN_CLK_B, SEL_CANCLK_1),
	PINMUX_IPSR_DATA(IP12_19_18, ETH_MAGIC),
	PINMUX_IPSR_DATA(IP12_19_18, AVB_TXD4),
	PINMUX_IPSR_MODSEL_DATA(IP12_19_18, IETX_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP12_21_20, ETH_TXD0),
	PINMUX_IPSR_DATA(IP12_21_20, AVB_TXD5),
	PINMUX_IPSR_MODSEL_DATA(IP12_21_20, IECLK_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP12_23_22, ETH_MDC),
	PINMUX_IPSR_DATA(IP12_23_22, AVB_TXD6),
	PINMUX_IPSR_MODSEL_DATA(IP12_23_22, IERX_C, SEL_IEB_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_26_24, STP_IVCXO27_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP12_26_24, AVB_TXD7),
	PINMUX_IPSR_MODSEL_DATA(IP12_26_24, SCIFB2_TXD_D, SEL_SCIFB2_3),
	PINMUX_IPSR_MODSEL_DATA(IP12_26_24, ADIDATA_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP12_26_24, MSIOF0_SYNC_C, SEL_SOF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP12_29_27, STP_ISCLK_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP12_29_27, AVB_TX_EN),
	PINMUX_IPSR_MODSEL_DATA(IP12_29_27, SCIFB2_RXD_D, SEL_SCIFB2_3),
	PINMUX_IPSR_MODSEL_DATA(IP12_29_27, ADICS_SAMP_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP12_29_27, MSIOF0_SCK_C, SEL_SOF0_2),

	/* IPSR13 */
	PINMUX_IPSR_MODSEL_DATA(IP13_2_0, STP_ISD_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP13_2_0, AVB_TX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP13_2_0, SCIFB2_SCK_C, SEL_SCIFB2_2),
	PINMUX_IPSR_MODSEL_DATA(IP13_2_0, ADICLK_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_2_0, MSIOF0_SS1_C, SEL_SOF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP13_4_3, STP_ISEN_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP13_4_3, AVB_TX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP13_4_3, ADICHS0_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_4_3, MSIOF0_SS2_C, SEL_SOF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP13_6_5, STP_ISSYNC_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP13_6_5, AVB_COL),
	PINMUX_IPSR_MODSEL_DATA(IP13_6_5, ADICHS1_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_6_5, MSIOF0_RXD_C, SEL_SOF0_2),
	PINMUX_IPSR_MODSEL_DATA(IP13_9_7, STP_OPWM_0, SEL_SSP_0),
	PINMUX_IPSR_DATA(IP13_9_7, AVB_GTX_CLK),
	PINMUX_IPSR_DATA(IP13_9_7, PWM0_B),
	PINMUX_IPSR_MODSEL_DATA(IP13_9_7, ADICHS2_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_9_7, MSIOF0_TXD_C, SEL_SOF0_2),
	PINMUX_IPSR_DATA(IP13_10, SD0_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP13_10, SPCLK_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_11, SD0_CMD),
	PINMUX_IPSR_MODSEL_DATA(IP13_11, MOSI_IO0_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_12, SD0_DATA0),
	PINMUX_IPSR_MODSEL_DATA(IP13_12, MISO_IO1_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_13, SD0_DATA1),
	PINMUX_IPSR_MODSEL_DATA(IP13_13, IO2_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_14, SD0_DATA2),
	PINMUX_IPSR_MODSEL_DATA(IP13_14, IO3_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_15, SD0_DATA3),
	PINMUX_IPSR_MODSEL_DATA(IP13_15, SSL_B, SEL_QSP_1),
	PINMUX_IPSR_DATA(IP13_18_16, SD0_CD),
	PINMUX_IPSR_MODSEL_DATA(IP13_18_16, MMC_D6_B, SEL_MMC_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_18_16, SIM0_RST_B, SEL_SIM_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_18_16, CAN0_RX_F, SEL_CAN0_5),
	PINMUX_IPSR_MODSEL_DATA(IP13_18_16, SCIFA5_TXD_B, SEL_SCIFA5_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_18_16, TX3_C, SEL_SCIF3_2),
	PINMUX_IPSR_DATA(IP13_21_19, SD0_WP),
	PINMUX_IPSR_MODSEL_DATA(IP13_21_19, MMC_D7_B, SEL_MMC_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_21_19, SIM0_D_B, SEL_SIM_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_21_19, CAN0_TX_F, SEL_CAN0_5),
	PINMUX_IPSR_MODSEL_DATA(IP13_21_19, SCIFA5_RXD_B, SEL_SCIFA5_1),
	PINMUX_IPSR_MODSEL_DATA(IP13_21_19, RX3_C, SEL_SCIF3_2),
	PINMUX_IPSR_DATA(IP13_22, SD1_CMD),
	PINMUX_IPSR_MODSEL_DATA(IP13_22, REMOCON_B, SEL_RCN_1),
	PINMUX_IPSR_DATA(IP13_24_23, SD1_DATA0),
	PINMUX_IPSR_MODSEL_DATA(IP13_24_23, SPEEDIN_B, SEL_RSP_1),
	PINMUX_IPSR_DATA(IP13_25, SD1_DATA1),
	PINMUX_IPSR_MODSEL_DATA(IP13_25, IETX_B, SEL_IEB_1),
	PINMUX_IPSR_DATA(IP13_26, SD1_DATA2),
	PINMUX_IPSR_MODSEL_DATA(IP13_26, IECLK_B, SEL_IEB_1),
	PINMUX_IPSR_DATA(IP13_27, SD1_DATA3),
	PINMUX_IPSR_MODSEL_DATA(IP13_27, IERX_B, SEL_IEB_1),
	PINMUX_IPSR_DATA(IP13_30_28, SD1_CD),
	PINMUX_IPSR_DATA(IP13_30_28, PWM0),
	PINMUX_IPSR_DATA(IP13_30_28, TPU_TO0),
	PINMUX_IPSR_MODSEL_DATA(IP13_30_28, SCL1_C, SEL_IIC1_2),

	/* IPSR14 */
	PINMUX_IPSR_DATA(IP14_1_0, SD1_WP),
	PINMUX_IPSR_DATA(IP14_1_0, PWM1_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_1_0, SDA1_C, SEL_IIC1_2),
	PINMUX_IPSR_DATA(IP14_2, SD2_CLK),
	PINMUX_IPSR_DATA(IP14_2, MMC_CLK),
	PINMUX_IPSR_DATA(IP14_3, SD2_CMD),
	PINMUX_IPSR_DATA(IP14_3, MMC_CMD),
	PINMUX_IPSR_DATA(IP14_4, SD2_DATA0),
	PINMUX_IPSR_DATA(IP14_4, MMC_D0),
	PINMUX_IPSR_DATA(IP14_5, SD2_DATA1),
	PINMUX_IPSR_DATA(IP14_5, MMC_D1),
	PINMUX_IPSR_DATA(IP14_6, SD2_DATA2),
	PINMUX_IPSR_DATA(IP14_6, MMC_D2),
	PINMUX_IPSR_DATA(IP14_7, SD2_DATA3),
	PINMUX_IPSR_DATA(IP14_7, MMC_D3),
	PINMUX_IPSR_DATA(IP14_10_8, SD2_CD),
	PINMUX_IPSR_DATA(IP14_10_8, MMC_D4),
	PINMUX_IPSR_MODSEL_DATA(IP14_10_8, SCL8_C, SEL_IIC8_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_10_8, TX5_B, SEL_SCIF5_1),
	PINMUX_IPSR_MODSEL_DATA(IP14_10_8, SCIFA5_TXD_C, SEL_SCIFA5_2),
	PINMUX_IPSR_DATA(IP14_13_11, SD2_WP),
	PINMUX_IPSR_DATA(IP14_13_11, MMC_D5),
	PINMUX_IPSR_MODSEL_DATA(IP14_13_11, SDA8_C, SEL_IIC8_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_13_11, RX5_B, SEL_SCIF5_1),
	PINMUX_IPSR_MODSEL_DATA(IP14_13_11, SCIFA5_RXD_C, SEL_SCIFA5_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_16_14, MSIOF0_SCK, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_16_14, RX2_C, SEL_SCIF2_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_16_14, ADIDATA, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_16_14, VI1_CLK_C, SEL_VI1_2),
	PINMUX_IPSR_DATA(IP14_16_14, VI1_G0_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_19_17, MSIOF0_SYNC, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_19_17, TX2_C, SEL_SCIF2_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_19_17, ADICS_SAMP, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_19_17, VI1_CLKENB_C, SEL_VI1_2),
	PINMUX_IPSR_DATA(IP14_19_17, VI1_G1_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_22_20, MSIOF0_TXD, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_22_20, ADICLK, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_22_20, VI1_FIELD_C, SEL_VI1_2),
	PINMUX_IPSR_DATA(IP14_22_20, VI1_G2_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_25_23, MSIOF0_RXD, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_25_23, ADICHS0, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_25_23, VI1_DATA0_C, SEL_VI1_2),
	PINMUX_IPSR_DATA(IP14_25_23, VI1_G3_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, MSIOF0_SS1, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, MMC_D6, SEL_MMC_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, ADICHS1, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, TX0_E, SEL_SCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, VI1_HSYNC_N_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_28_26, SCL7_C, SEL_IIC7_2),
	PINMUX_IPSR_DATA(IP14_28_26, VI1_G4_B),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, MSIOF0_SS2, SEL_SOF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, MMC_D7, SEL_MMC_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, ADICHS2, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, RX0_E, SEL_SCIF0_4),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, VI1_VSYNC_N_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP14_31_29, SDA7_C, SEL_IIC7_2),
	PINMUX_IPSR_DATA(IP14_31_29, VI1_G5_B),

	/* IPSR15 */
	PINMUX_IPSR_MODSEL_DATA(IP15_1_0, SIM0_RST, SEL_SIM_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_1_0, IETX, SEL_IEB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_1_0, CAN1_TX_D, SEL_CAN1_3),
	PINMUX_IPSR_DATA(IP15_3_2, SIM0_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP15_3_2, IECLK, SEL_IEB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_3_2, CAN_CLK_C, SEL_CANCLK_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_5_4, SIM0_D, SEL_SIM_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_5_4, IERX, SEL_IEB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_5_4, CAN1_RX_D, SEL_CAN1_3),
	PINMUX_IPSR_MODSEL_DATA(IP15_8_6, GPS_CLK, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_8_6, DU1_DOTCLKIN_C, SEL_DIS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_8_6, AUDIO_CLKB_B, SEL_ADG_1),
	PINMUX_IPSR_DATA(IP15_8_6, PWM5_B),
	PINMUX_IPSR_MODSEL_DATA(IP15_8_6, SCIFA3_TXD_C, SEL_SCIFA3_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_11_9, GPS_SIGN, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_11_9, TX4_C, SEL_SCIF4_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_11_9, SCIFA4_TXD_C, SEL_SCIFA4_2),
	PINMUX_IPSR_DATA(IP15_11_9, PWM5),
	PINMUX_IPSR_DATA(IP15_11_9, VI1_G6_B),
	PINMUX_IPSR_MODSEL_DATA(IP15_11_9, SCIFA3_RXD_C, SEL_SCIFA3_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_14_12, GPS_MAG, SEL_GPS_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_14_12, RX4_C, SEL_SCIF4_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_14_12, SCIFA4_RXD_C, SEL_SCIFA4_2),
	PINMUX_IPSR_DATA(IP15_14_12, PWM6),
	PINMUX_IPSR_DATA(IP15_14_12, VI1_G7_B),
	PINMUX_IPSR_MODSEL_DATA(IP15_14_12, SCIFA3_SCK_C, SEL_SCIFA3_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_17_15, HCTS0_N, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_17_15, SCIFB0_CTS_N, SEL_SCIFB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_17_15, GLO_I0_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_17_15, TCLK1, SEL_TMU1_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_17_15, VI1_DATA1_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_20_18, HRTS0_N, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_20_18, SCIFB0_RTS_N, SEL_SCIFB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_20_18, GLO_I1_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_20_18, VI1_DATA2_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_23_21, HSCK0, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_23_21, SCIFB0_SCK, SEL_SCIFB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_23_21, GLO_Q0_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_23_21, CAN_CLK, SEL_CANCLK_0),
	PINMUX_IPSR_DATA(IP15_23_21, TCLK2),
	PINMUX_IPSR_MODSEL_DATA(IP15_23_21, VI1_DATA3_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_26_24, HRX0, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_26_24, SCIFB0_RXD, SEL_SCIFB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_26_24, GLO_Q1_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_26_24, CAN0_RX_B, SEL_CAN0_1),
	PINMUX_IPSR_MODSEL_DATA(IP15_26_24, VI1_DATA4_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_29_27, HTX0, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_29_27, SCIFB0_TXD, SEL_SCIFB_0),
	PINMUX_IPSR_MODSEL_DATA(IP15_29_27, GLO_SCLK_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP15_29_27, CAN0_TX_B, SEL_CAN0_1),
	PINMUX_IPSR_MODSEL_DATA(IP15_29_27, VI1_DATA5_C, SEL_VI1_2),

	/* IPSR16 */
	PINMUX_IPSR_MODSEL_DATA(IP16_2_0, HRX1, SEL_HSCIF1_0),
	PINMUX_IPSR_MODSEL_DATA(IP16_2_0, SCIFB1_RXD, SEL_SCIFB1_0),
	PINMUX_IPSR_DATA(IP16_2_0, VI1_R0_B),
	PINMUX_IPSR_MODSEL_DATA(IP16_2_0, GLO_SDATA_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP16_2_0, VI1_DATA6_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP16_5_3, HTX1, SEL_HSCIF1_0),
	PINMUX_IPSR_MODSEL_DATA(IP16_5_3, SCIFB1_TXD, SEL_SCIFB1_0),
	PINMUX_IPSR_DATA(IP16_5_3, VI1_R1_B),
	PINMUX_IPSR_MODSEL_DATA(IP16_5_3, GLO_SS_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP16_5_3, VI1_DATA7_C, SEL_VI1_2),
	PINMUX_IPSR_MODSEL_DATA(IP16_7_6, HSCK1, SEL_HSCIF1_0),
	PINMUX_IPSR_MODSEL_DATA(IP16_7_6, SCIFB1_SCK, SEL_SCIFB1_0),
	PINMUX_IPSR_DATA(IP16_7_6, MLB_CK),
	PINMUX_IPSR_MODSEL_DATA(IP16_7_6, GLO_RFON_C, SEL_GPS_2),
	PINMUX_IPSR_MODSEL_DATA(IP16_9_8, HCTS1_N, SEL_HSCIF1_0),
	PINMUX_IPSR_DATA(IP16_9_8, SCIFB1_CTS_N),
	PINMUX_IPSR_DATA(IP16_9_8, MLB_SIG),
	PINMUX_IPSR_MODSEL_DATA(IP16_9_8, CAN1_TX_B, SEL_CAN1_1),
	PINMUX_IPSR_MODSEL_DATA(IP16_11_10, HRTS1_N, SEL_HSCIF1_0),
	PINMUX_IPSR_DATA(IP16_11_10, SCIFB1_RTS_N),
	PINMUX_IPSR_DATA(IP16_11_10, MLB_DAT),
	PINMUX_IPSR_MODSEL_DATA(IP16_11_10, CAN1_RX_B, SEL_CAN1_1),
};

static struct pinmux_gpio pinmux_gpios[] = {
	PINMUX_GPIO_GP_ALL(),

	GPIO_FN(EX_CS0_N), GPIO_FN(RD_N), GPIO_FN(AUDIO_CLKA),
	GPIO_FN(VI0_CLK), GPIO_FN(VI0_DATA0_VI0_B0),
	GPIO_FN(VI0_DATA0_VI0_B1), GPIO_FN(VI0_DATA0_VI0_B2),
	GPIO_FN(VI0_DATA0_VI0_B4), GPIO_FN(VI0_DATA0_VI0_B5),
	GPIO_FN(VI0_DATA0_VI0_B6), GPIO_FN(VI0_DATA0_VI0_B7),
	GPIO_FN(USB0_PWEN), GPIO_FN(USB0_OVC), GPIO_FN(USB1_PWEN),

	/* IPSR0 - 5 */

	/* IPSR6 */
	GPIO_FN(AUDIO_CLKB), GPIO_FN(STP_OPWM_0_B), GPIO_FN(MSIOF1_SCK_B),
	GPIO_FN(SCIF_CLK), GPIO_FN(BPFCLK_E),
	GPIO_FN(AUDIO_CLKC), GPIO_FN(SCIFB0_SCK_C),
	GPIO_FN(MSIOF1_SYNC_B), GPIO_FN(RX2),
	GPIO_FN(SCIFA2_RXD), GPIO_FN(FMIN_E),
	GPIO_FN(AUDIO_CLKOUT), GPIO_FN(MSIOF1_SS1_B),
	GPIO_FN(TX2), GPIO_FN(SCIFA2_TXD),
	GPIO_FN(IRQ0), GPIO_FN(SCIFB1_RXD_D), GPIO_FN(INTC_IRQ0_N),
	GPIO_FN(IRQ1), GPIO_FN(SCIFB1_SCK_C), GPIO_FN(INTC_IRQ1_N),
	GPIO_FN(IRQ2), GPIO_FN(SCIFB1_TXD_D), GPIO_FN(INTC_IRQ2_N),
	GPIO_FN(IRQ3), GPIO_FN(SCL4_C),
	GPIO_FN(MSIOF2_TXD_E), GPIO_FN(INTC_IRQ3_N),
	GPIO_FN(IRQ4), GPIO_FN(HRX1_C), GPIO_FN(SDA4_C),
	GPIO_FN(MSIOF2_RXD_E), GPIO_FN(INTC_IRQ4_N),
	GPIO_FN(IRQ5), GPIO_FN(HTX1_C), GPIO_FN(SCL1_E), GPIO_FN(MSIOF2_SCK_E),
	GPIO_FN(IRQ6), GPIO_FN(HSCK1_C), GPIO_FN(MSIOF1_SS2_B),
	GPIO_FN(SDA1_E), GPIO_FN(MSIOF2_SYNC_E),
	GPIO_FN(IRQ7), GPIO_FN(HCTS1_N_C), GPIO_FN(MSIOF1_TXD_B),
	GPIO_FN(GPS_CLK_C), GPIO_FN(GPS_CLK_D),
	GPIO_FN(IRQ8), GPIO_FN(HRTS1_N_C), GPIO_FN(MSIOF1_RXD_B),
	GPIO_FN(GPS_SIGN_C), GPIO_FN(GPS_SIGN_D),

	/* IPSR7 - 10 */

	/* IPSR11 */
	GPIO_FN(VI0_R5), GPIO_FN(VI2_DATA6), GPIO_FN(GLO_SDATA_B),
	GPIO_FN(RX0_C), GPIO_FN(SDA1_D),
	GPIO_FN(VI0_R6), GPIO_FN(VI2_DATA7),
	GPIO_FN(GLO_SS_B), GPIO_FN(TX1_C), GPIO_FN(SCL4_B),
	GPIO_FN(VI0_R7), GPIO_FN(GLO_RFON_B),
	GPIO_FN(RX1_C), GPIO_FN(CAN0_RX_E),
	GPIO_FN(SDA4_B), GPIO_FN(HRX1_D), GPIO_FN(SCIFB0_RXD_D),
	GPIO_FN(VI1_HSYNC_N), GPIO_FN(AVB_RXD0), GPIO_FN(TS_SDATA0_B),
	GPIO_FN(TX4_B), GPIO_FN(SCIFA4_TXD_B),
	GPIO_FN(VI1_VSYNC_N), GPIO_FN(AVB_RXD1), GPIO_FN(TS_SCK0_B),
	GPIO_FN(RX4_B), GPIO_FN(SCIFA4_RXD_B),
	GPIO_FN(VI1_CLKENB), GPIO_FN(AVB_RXD2), GPIO_FN(TS_SDEN0_B),
	GPIO_FN(VI1_FIELD), GPIO_FN(AVB_RXD3), GPIO_FN(TS_SPSYNC0_B),
	GPIO_FN(VI1_CLK), GPIO_FN(AVB_RXD4),
	GPIO_FN(VI1_DATA0), GPIO_FN(AVB_RXD5),
	GPIO_FN(VI1_DATA1), GPIO_FN(AVB_RXD6),
	GPIO_FN(VI1_DATA2), GPIO_FN(AVB_RXD7),
	GPIO_FN(VI1_DATA3), GPIO_FN(AVB_RX_ER),
	GPIO_FN(VI1_DATA4), GPIO_FN(AVB_MDIO),
	GPIO_FN(VI1_DATA5), GPIO_FN(AVB_RX_DV),
	GPIO_FN(VI1_DATA6), GPIO_FN(AVB_MAGIC),
	GPIO_FN(VI1_DATA7), GPIO_FN(AVB_MDC),
	GPIO_FN(ETH_MDIO), GPIO_FN(AVB_RX_CLK), GPIO_FN(SCL2_C),
	GPIO_FN(ETH_CRS_DV), GPIO_FN(AVB_LINK), GPIO_FN(SDA2_C),

	/* IPSR12 */
	GPIO_FN(ETH_RX_ER), GPIO_FN(AVB_CRS), GPIO_FN(SCL3), GPIO_FN(SCL7),
	GPIO_FN(ETH_RXD0), GPIO_FN(AVB_PHY_INT), GPIO_FN(SDA3), GPIO_FN(SDA7),
	GPIO_FN(ETH_RXD1), GPIO_FN(AVB_GTXREFCLK), GPIO_FN(CAN0_TX_C),
	GPIO_FN(SCL2_D), GPIO_FN(MSIOF1_RXD_E),
	GPIO_FN(ETH_LINK), GPIO_FN(AVB_TXD0), GPIO_FN(CAN0_RX_C),
	GPIO_FN(SDA2_D), GPIO_FN(MSIOF1_SCK_E),
	GPIO_FN(ETH_REFCLK), GPIO_FN(AVB_TXD1), GPIO_FN(SCIFA3_RXD_B),
	GPIO_FN(CAN1_RX_C), GPIO_FN(MSIOF1_SYNC_E),
	GPIO_FN(ETH_TXD1), GPIO_FN(AVB_TXD2), GPIO_FN(SCIFA3_TXD_B),
	GPIO_FN(CAN1_TX_C), GPIO_FN(MSIOF1_TXD_E),
	GPIO_FN(ETH_TX_EN), GPIO_FN(AVB_TXD3),
	GPIO_FN(TCLK1_B), GPIO_FN(CAN_CLK_B),
	GPIO_FN(ETH_MAGIC), GPIO_FN(AVB_TXD4), GPIO_FN(IETX_C),
	GPIO_FN(ETH_TXD0), GPIO_FN(AVB_TXD5), GPIO_FN(IECLK_C),
	GPIO_FN(ETH_MDC), GPIO_FN(AVB_TXD6), GPIO_FN(IERX_C),
	GPIO_FN(STP_IVCXO27_0), GPIO_FN(AVB_TXD7), GPIO_FN(SCIFB2_TXD_D),
	GPIO_FN(ADIDATA_B), GPIO_FN(MSIOF0_SYNC_C),
	GPIO_FN(STP_ISCLK_0), GPIO_FN(AVB_TX_EN), GPIO_FN(SCIFB2_RXD_D),
	GPIO_FN(ADICS_SAMP_B), GPIO_FN(MSIOF0_SCK_C),

	/* IPSR13 */
	GPIO_FN(STP_ISD_0), GPIO_FN(AVB_TX_ER), GPIO_FN(SCIFB2_SCK_C),
	GPIO_FN(ADICLK_B), GPIO_FN(MSIOF0_SS1_C),
	GPIO_FN(STP_ISEN_0), GPIO_FN(AVB_TX_CLK),
	GPIO_FN(ADICHS0_B), GPIO_FN(MSIOF0_SS2_C),
	GPIO_FN(STP_ISSYNC_0), GPIO_FN(AVB_COL),
	GPIO_FN(ADICHS1_B), GPIO_FN(MSIOF0_RXD_C),
	GPIO_FN(STP_OPWM_0), GPIO_FN(AVB_GTX_CLK), GPIO_FN(PWM0_B),
	GPIO_FN(ADICHS2_B), GPIO_FN(MSIOF0_TXD_C),
	GPIO_FN(SD0_CLK), GPIO_FN(SPCLK_B),
	GPIO_FN(SD0_CMD), GPIO_FN(MOSI_IO0_B),
	GPIO_FN(SD0_DATA0), GPIO_FN(MISO_IO1_B),
	GPIO_FN(SD0_DATA1), GPIO_FN(IO2_B),
	GPIO_FN(SD0_DATA2), GPIO_FN(IO3_B), GPIO_FN(SD0_DATA3), GPIO_FN(SSL_B),
	GPIO_FN(SD0_CD), GPIO_FN(MMC_D6_B),
	GPIO_FN(SIM0_RST_B), GPIO_FN(CAN0_RX_F),
	GPIO_FN(SCIFA5_TXD_B), GPIO_FN(TX3_C),
	GPIO_FN(SD0_WP), GPIO_FN(MMC_D7_B),
	GPIO_FN(SIM0_D_B), GPIO_FN(CAN0_TX_F),
	GPIO_FN(SCIFA5_RXD_B), GPIO_FN(RX3_C),
	GPIO_FN(SD1_CMD), GPIO_FN(REMOCON_B),
	GPIO_FN(SD1_DATA0), GPIO_FN(SPEEDIN_B),
	GPIO_FN(SD1_DATA1), GPIO_FN(IETX_B),
	GPIO_FN(SD1_DATA2), GPIO_FN(IECLK_B),
	GPIO_FN(SD1_DATA3), GPIO_FN(IERX_B),
	GPIO_FN(SD1_CD), GPIO_FN(PWM0), GPIO_FN(TPU_TO0), GPIO_FN(SCL1_C),

	/* IPSR14 */
	GPIO_FN(SD1_WP), GPIO_FN(PWM1_B), GPIO_FN(SDA1_C),
	GPIO_FN(SD2_CLK), GPIO_FN(MMC_CLK), GPIO_FN(SD2_CMD), GPIO_FN(MMC_CMD),
	GPIO_FN(SD2_DATA0), GPIO_FN(MMC_D0),
	GPIO_FN(SD2_DATA1), GPIO_FN(MMC_D1),
	GPIO_FN(SD2_DATA2), GPIO_FN(MMC_D2),
	GPIO_FN(SD2_DATA3), GPIO_FN(MMC_D3),
	GPIO_FN(SD2_CD), GPIO_FN(MMC_D4), GPIO_FN(SCL8_C),
	GPIO_FN(TX5_B), GPIO_FN(SCIFA5_TXD_C),
	GPIO_FN(SD2_WP), GPIO_FN(MMC_D5), GPIO_FN(SDA8_C),
	GPIO_FN(RX5_B), GPIO_FN(SCIFA5_RXD_C),
	GPIO_FN(MSIOF0_SCK), GPIO_FN(RX2_C), GPIO_FN(ADIDATA),
	GPIO_FN(VI1_CLK_C), GPIO_FN(VI1_G0_B),
	GPIO_FN(MSIOF0_SYNC), GPIO_FN(TX2_C), GPIO_FN(ADICS_SAMP),
	GPIO_FN(VI1_CLKENB_C), GPIO_FN(VI1_G1_B),
	GPIO_FN(MSIOF0_TXD), GPIO_FN(ADICLK),
	GPIO_FN(VI1_FIELD_C), GPIO_FN(VI1_G2_B),
	GPIO_FN(MSIOF0_RXD), GPIO_FN(ADICHS0),
	GPIO_FN(VI1_DATA0_C), GPIO_FN(VI1_G3_B),
	GPIO_FN(MSIOF0_SS1), GPIO_FN(MMC_D6), GPIO_FN(ADICHS1), GPIO_FN(TX0_E),
	GPIO_FN(VI1_HSYNC_N_C), GPIO_FN(SCL7_C), GPIO_FN(VI1_G4_B),
	GPIO_FN(MSIOF0_SS2), GPIO_FN(MMC_D7), GPIO_FN(ADICHS2), GPIO_FN(RX0_E),
	GPIO_FN(VI1_VSYNC_N_C), GPIO_FN(SDA7_C), GPIO_FN(VI1_G5_B),

	/* IPSR15 */
	GPIO_FN(SIM0_RST), GPIO_FN(IETX), GPIO_FN(CAN1_TX_D),
	GPIO_FN(SIM0_CLK), GPIO_FN(IECLK), GPIO_FN(CAN_CLK_C),
	GPIO_FN(SIM0_D), GPIO_FN(IERX), GPIO_FN(CAN1_RX_D),
	GPIO_FN(GPS_CLK), GPIO_FN(DU1_DOTCLKIN_C), GPIO_FN(AUDIO_CLKB_B),
	GPIO_FN(PWM5_B), GPIO_FN(SCIFA3_TXD_C),
	GPIO_FN(GPS_SIGN), GPIO_FN(TX4_C),
	GPIO_FN(SCIFA4_TXD_C), GPIO_FN(PWM5),
	GPIO_FN(VI1_G6_B), GPIO_FN(SCIFA3_RXD_C),
	GPIO_FN(GPS_MAG), GPIO_FN(RX4_C), GPIO_FN(SCIFA4_RXD_C), GPIO_FN(PWM6),
	GPIO_FN(VI1_G7_B), GPIO_FN(SCIFA3_SCK_C),
	GPIO_FN(HCTS0_N), GPIO_FN(SCIFB0_CTS_N), GPIO_FN(GLO_I0_C),
	GPIO_FN(TCLK1), GPIO_FN(VI1_DATA1_C),
	GPIO_FN(HRTS0_N), GPIO_FN(SCIFB0_RTS_N),
	GPIO_FN(GLO_I1_C), GPIO_FN(VI1_DATA2_C),
	GPIO_FN(HSCK0), GPIO_FN(SCIFB0_SCK),
	GPIO_FN(GLO_Q0_C), GPIO_FN(CAN_CLK),
	GPIO_FN(TCLK2), GPIO_FN(VI1_DATA3_C),
	GPIO_FN(HRX0), GPIO_FN(SCIFB0_RXD), GPIO_FN(GLO_Q1_C),
	GPIO_FN(CAN0_RX_B), GPIO_FN(VI1_DATA4_C),
	GPIO_FN(HTX0), GPIO_FN(SCIFB0_TXD), GPIO_FN(GLO_SCLK_C),
	GPIO_FN(CAN0_TX_B), GPIO_FN(VI1_DATA5_C),

	/* IPSR16 */
	GPIO_FN(HRX1), GPIO_FN(SCIFB1_RXD), GPIO_FN(VI1_R0_B),
	GPIO_FN(GLO_SDATA_C), GPIO_FN(VI1_DATA6_C),
	GPIO_FN(HTX1), GPIO_FN(SCIFB1_TXD), GPIO_FN(VI1_R1_B),
	GPIO_FN(GLO_SS_C), GPIO_FN(VI1_DATA7_C),
	GPIO_FN(HSCK1), GPIO_FN(SCIFB1_SCK),
	GPIO_FN(MLB_CK), GPIO_FN(GLO_RFON_C),
	GPIO_FN(HCTS1_N), GPIO_FN(SCIFB1_CTS_N),
	GPIO_FN(MLB_SIG), GPIO_FN(CAN1_TX_B),
	GPIO_FN(HRTS1_N), GPIO_FN(SCIFB1_RTS_N),
	GPIO_FN(MLB_DAT), GPIO_FN(CAN1_RX_B),
};

static struct pinmux_cfg_reg pinmux_config_regs[] = {
	{ PINMUX_CFG_REG("GPSR0", 0xE6060004, 32, 1) {
		GP_0_31_FN, FN_IP1_22_20,
		GP_0_30_FN, FN_IP1_19_17,
		GP_0_29_FN, FN_IP1_16_14,
		GP_0_28_FN, FN_IP1_13_11,
		GP_0_27_FN, FN_IP1_10_8,
		GP_0_26_FN, FN_IP1_7_6,
		GP_0_25_FN, FN_IP1_5_4,
		GP_0_24_FN, FN_IP1_3_2,
		GP_0_23_FN, FN_IP1_1_0,
		GP_0_22_FN, FN_IP0_30_29,
		GP_0_21_FN, FN_IP0_28_27,
		GP_0_20_FN, FN_IP0_26_25,
		GP_0_19_FN, FN_IP0_24_23,
		GP_0_18_FN, FN_IP0_22_21,
		GP_0_17_FN, FN_IP0_20_19,
		GP_0_16_FN, FN_IP0_18_16,
		GP_0_15_FN, FN_IP0_15,
		GP_0_14_FN, FN_IP0_14,
		GP_0_13_FN, FN_IP0_13,
		GP_0_12_FN, FN_IP0_12,
		GP_0_11_FN, FN_IP0_11,
		GP_0_10_FN, FN_IP0_10,
		GP_0_9_FN, FN_IP0_9,
		GP_0_8_FN, FN_IP0_8,
		GP_0_7_FN, FN_IP0_7,
		GP_0_6_FN, FN_IP0_6,
		GP_0_5_FN, FN_IP0_5,
		GP_0_4_FN, FN_IP0_4,
		GP_0_3_FN, FN_IP0_3,
		GP_0_2_FN, FN_IP0_2,
		GP_0_1_FN, FN_IP0_1,
		GP_0_0_FN, FN_IP0_0, }
	},
	{ PINMUX_CFG_REG("GPSR1", 0xE6060008, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_1_25_FN, FN_IP3_21_20,
		GP_1_24_FN, FN_IP3_19_18,
		GP_1_23_FN, FN_IP3_17_16,
		GP_1_22_FN, FN_IP3_15_14,
		GP_1_21_FN, FN_IP3_13_12,
		GP_1_20_FN, FN_IP3_11_9,
		GP_1_19_FN, FN_RD_N,
		GP_1_18_FN, FN_IP3_8_6,
		GP_1_17_FN, FN_IP3_5_3,
		GP_1_16_FN, FN_IP3_2_0,
		GP_1_15_FN, FN_IP2_29_27,
		GP_1_14_FN, FN_IP2_26_25,
		GP_1_13_FN, FN_IP2_24_23,
		GP_1_12_FN, FN_EX_CS0_N,
		GP_1_11_FN, FN_IP2_22_21,
		GP_1_10_FN, FN_IP2_20_19,
		GP_1_9_FN, FN_IP2_18_16,
		GP_1_8_FN, FN_IP2_15_13,
		GP_1_7_FN, FN_IP2_12_10,
		GP_1_6_FN, FN_IP2_9_7,
		GP_1_5_FN, FN_IP2_6_5,
		GP_1_4_FN, FN_IP2_4_3,
		GP_1_3_FN, FN_IP2_2_0,
		GP_1_2_FN, FN_IP1_31_29,
		GP_1_1_FN, FN_IP1_28_26,
		GP_1_0_FN, FN_IP1_25_23, }
	},
	{ PINMUX_CFG_REG("GPSR2", 0xE606000C, 32, 1) {
		GP_2_31_FN, FN_IP6_7_6,
		GP_2_30_FN, FN_IP6_5_3,
		GP_2_29_FN, FN_IP6_2_0,
		GP_2_28_FN, FN_AUDIO_CLKA,
		GP_2_27_FN, FN_IP5_31_29,
		GP_2_26_FN, FN_IP5_28_26,
		GP_2_25_FN, FN_IP5_25_24,
		GP_2_24_FN, FN_IP5_23_22,
		GP_2_23_FN, FN_IP5_21_20,
		GP_2_22_FN, FN_IP5_19_17,
		GP_2_21_FN, FN_IP5_16_15,
		GP_2_20_FN, FN_IP5_14_12,
		GP_2_19_FN, FN_IP5_11_9,
		GP_2_18_FN, FN_IP5_8_6,
		GP_2_17_FN, FN_IP5_5_3,
		GP_2_16_FN, FN_IP5_2_0,
		GP_2_15_FN, FN_IP4_30_28,
		GP_2_14_FN, FN_IP4_27_26,
		GP_2_13_FN, FN_IP4_25_24,
		GP_2_12_FN, FN_IP4_23_22,
		GP_2_11_FN, FN_IP4_21,
		GP_2_10_FN, FN_IP4_20,
		GP_2_9_FN, FN_IP4_19,
		GP_2_8_FN, FN_IP4_18_16,
		GP_2_7_FN, FN_IP4_15_13,
		GP_2_6_FN, FN_IP4_12_10,
		GP_2_5_FN, FN_IP4_9_8,
		GP_2_4_FN, FN_IP4_7_5,
		GP_2_3_FN, FN_IP4_4_2,
		GP_2_2_FN, FN_IP4_1_0,
		GP_2_1_FN, FN_IP3_30_28,
		GP_2_0_FN, FN_IP3_27_25 }
	},
	{ PINMUX_CFG_REG("GPSR3", 0xE6060010, 32, 1) {
		GP_3_31_FN, FN_IP9_18_17,
		GP_3_30_FN, FN_IP9_16,
		GP_3_29_FN, FN_IP9_15_13,
		GP_3_28_FN, FN_IP9_12,
		GP_3_27_FN, FN_IP9_11,
		GP_3_26_FN, FN_IP9_10_8,
		GP_3_25_FN, FN_IP9_7,
		GP_3_24_FN, FN_IP9_6,
		GP_3_23_FN, FN_IP9_5_3,
		GP_3_22_FN, FN_IP9_2_0,
		GP_3_21_FN, FN_IP8_30_28,
		GP_3_20_FN, FN_IP8_27_26,
		GP_3_19_FN, FN_IP8_25_24,
		GP_3_18_FN, FN_IP8_23_21,
		GP_3_17_FN, FN_IP8_20_18,
		GP_3_16_FN, FN_IP8_17_15,
		GP_3_15_FN, FN_IP8_14_12,
		GP_3_14_FN, FN_IP8_11_9,
		GP_3_13_FN, FN_IP8_8_6,
		GP_3_12_FN, FN_IP8_5_3,
		GP_3_11_FN, FN_IP8_2_0,
		GP_3_10_FN, FN_IP7_29_27,
		GP_3_9_FN, FN_IP7_26_24,
		GP_3_8_FN, FN_IP7_23_21,
		GP_3_7_FN, FN_IP7_20_19,
		GP_3_6_FN, FN_IP7_18_17,
		GP_3_5_FN, FN_IP7_16_15,
		GP_3_4_FN, FN_IP7_14_13,
		GP_3_3_FN, FN_IP7_12_11,
		GP_3_2_FN, FN_IP7_10_9,
		GP_3_1_FN, FN_IP7_8_6,
		GP_3_0_FN, FN_IP7_5_3 }
	},
	{ PINMUX_CFG_REG("GPSR4", 0xE6060014, 32, 1) {
		GP_4_31_FN, FN_IP15_5_4,
		GP_4_30_FN, FN_IP15_3_2,
		GP_4_29_FN, FN_IP15_1_0,
		GP_4_28_FN, FN_IP11_8_6,
		GP_4_27_FN, FN_IP11_5_3,
		GP_4_26_FN, FN_IP11_2_0,
		GP_4_25_FN, FN_IP10_31_29,
		GP_4_24_FN, FN_IP10_28_27,
		GP_4_23_FN, FN_IP10_26_25,
		GP_4_22_FN, FN_IP10_24_22,
		GP_4_21_FN, FN_IP10_21_19,
		GP_4_20_FN, FN_IP10_18_17,
		GP_4_19_FN, FN_IP10_16_15,
		GP_4_18_FN, FN_IP10_14_12,
		GP_4_17_FN, FN_IP10_11_9,
		GP_4_16_FN, FN_IP10_8_6,
		GP_4_15_FN, FN_IP10_5_3,
		GP_4_14_FN, FN_IP10_2_0,
		GP_4_13_FN, FN_IP9_31_29,
		GP_4_12_FN, FN_VI0_DATA0_VI0_B7,
		GP_4_11_FN, FN_VI0_DATA0_VI0_B6,
		GP_4_10_FN, FN_VI0_DATA0_VI0_B5,
		GP_4_9_FN, FN_VI0_DATA0_VI0_B4,
		GP_4_8_FN, FN_IP9_28_27,
		GP_4_7_FN, FN_VI0_DATA0_VI0_B2,
		GP_4_6_FN, FN_VI0_DATA0_VI0_B1,
		GP_4_5_FN, FN_VI0_DATA0_VI0_B0,
		GP_4_4_FN, FN_IP9_26_25,
		GP_4_3_FN, FN_IP9_24_23,
		GP_4_2_FN, FN_IP9_22_21,
		GP_4_1_FN, FN_IP9_20_19,
		GP_4_0_FN, FN_VI0_CLK }
	},
	{ PINMUX_CFG_REG("GPSR5", 0xE6060018, 32, 1) {
		GP_5_31_FN, FN_IP3_24_22,
		GP_5_30_FN, FN_IP13_9_7,
		GP_5_29_FN, FN_IP13_6_5,
		GP_5_28_FN, FN_IP13_4_3,
		GP_5_27_FN, FN_IP13_2_0,
		GP_5_26_FN, FN_IP12_29_27,
		GP_5_25_FN, FN_IP12_26_24,
		GP_5_24_FN, FN_IP12_23_22,
		GP_5_23_FN, FN_IP12_21_20,
		GP_5_22_FN, FN_IP12_19_18,
		GP_5_21_FN, FN_IP12_17_16,
		GP_5_20_FN, FN_IP12_15_13,
		GP_5_19_FN, FN_IP12_12_10,
		GP_5_18_FN, FN_IP12_9_7,
		GP_5_17_FN, FN_IP12_6_4,
		GP_5_16_FN, FN_IP12_3_2,
		GP_5_15_FN, FN_IP12_1_0,
		GP_5_14_FN, FN_IP11_31_30,
		GP_5_13_FN, FN_IP11_29_28,
		GP_5_12_FN, FN_IP11_27,
		GP_5_11_FN, FN_IP11_26,
		GP_5_10_FN, FN_IP11_25,
		GP_5_9_FN, FN_IP11_24,
		GP_5_8_FN, FN_IP11_23,
		GP_5_7_FN, FN_IP11_22,
		GP_5_6_FN, FN_IP11_21,
		GP_5_5_FN, FN_IP11_20,
		GP_5_4_FN, FN_IP11_19,
		GP_5_3_FN, FN_IP11_18_17,
		GP_5_2_FN, FN_IP11_16_15,
		GP_5_1_FN, FN_IP11_14_12,
		GP_5_0_FN, FN_IP11_11_9 }
	},
	{ PINMUX_CFG_REG("GPSR6", 0xE606001C, 32, 1) {
		0, 0,
		0, 0,
		GP_6_29_FN, FN_IP14_31_29,
		GP_6_28_FN, FN_IP14_28_26,
		GP_6_27_FN, FN_IP14_25_23,
		GP_6_26_FN, FN_IP14_22_20,
		GP_6_25_FN, FN_IP14_19_17,
		GP_6_24_FN, FN_IP14_16_14,
		GP_6_23_FN, FN_IP14_13_11,
		GP_6_22_FN, FN_IP14_10_8,
		GP_6_21_FN, FN_IP14_7,
		GP_6_20_FN, FN_IP14_6,
		GP_6_19_FN, FN_IP14_5,
		GP_6_18_FN, FN_IP14_4,
		GP_6_17_FN, FN_IP14_3,
		GP_6_16_FN, FN_IP14_2,
		GP_6_15_FN, FN_IP14_1_0,
		GP_6_14_FN, FN_IP13_30_28,
		GP_6_13_FN, FN_IP13_27,
		GP_6_12_FN, FN_IP13_26,
		GP_6_11_FN, FN_IP13_25,
		GP_6_10_FN, FN_IP13_24_23,
		GP_6_9_FN, FN_IP13_22,
		0, 0,
		GP_6_7_FN, FN_IP13_21_19,
		GP_6_6_FN, FN_IP13_18_16,
		GP_6_5_FN, FN_IP13_15,
		GP_6_4_FN, FN_IP13_14,
		GP_6_3_FN, FN_IP13_13,
		GP_6_2_FN, FN_IP13_12,
		GP_6_1_FN, FN_IP13_11,
		GP_6_0_FN, FN_IP13_10 }
	},
	{ PINMUX_CFG_REG("GPSR7", 0xE6060074, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_7_25_FN, FN_USB1_PWEN,
		GP_7_24_FN, FN_USB0_OVC,
		GP_7_23_FN, FN_USB0_PWEN,
		GP_7_22_FN, FN_IP15_14_12,
		GP_7_21_FN, FN_IP15_11_9,
		GP_7_20_FN, FN_IP15_8_6,
		GP_7_19_FN, FN_IP7_2_0,
		GP_7_18_FN, FN_IP6_29_27,
		GP_7_17_FN, FN_IP6_26_24,
		GP_7_16_FN, FN_IP6_23_21,
		GP_7_15_FN, FN_IP6_20_19,
		GP_7_14_FN, FN_IP6_18_16,
		GP_7_13_FN, FN_IP6_15_14,
		GP_7_12_FN, FN_IP6_13_12,
		GP_7_11_FN, FN_IP6_11_10,
		GP_7_10_FN, FN_IP6_9_8,
		GP_7_9_FN, FN_IP16_11_10,
		GP_7_8_FN, FN_IP16_9_8,
		GP_7_7_FN, FN_IP16_7_6,
		GP_7_6_FN, FN_IP16_5_3,
		GP_7_5_FN, FN_IP16_2_0,
		GP_7_4_FN, FN_IP15_29_27,
		GP_7_3_FN, FN_IP15_26_24,
		GP_7_2_FN, FN_IP15_23_21,
		GP_7_1_FN, FN_IP15_20_18,
		GP_7_0_FN, FN_IP15_17_15 }
	},

	/* IPSR0 - 5 */

	{ PINMUX_CFG_REG_VAR("IPSR6", 0xE6060038, 32,
			     2, 3, 3, 3, 2, 3, 2, 2, 2, 2, 2, 3, 3) {
		/* IP6_31_30 [2] */
		0, 0, 0, 0,
		/* IP6_29_27 [3] */
		FN_IRQ8, FN_HRTS1_N_C, FN_MSIOF1_RXD_B,
		FN_GPS_SIGN_C, FN_GPS_SIGN_D,
		0, 0, 0,
		/* IP6_26_24 [3] */
		FN_IRQ7, FN_HCTS1_N_C, FN_MSIOF1_TXD_B,
		FN_GPS_CLK_C, FN_GPS_CLK_D,
		0, 0, 0,
		/* IP6_23_21 [3] */
		FN_IRQ6, FN_HSCK1_C, FN_MSIOF1_SS2_B,
		FN_SDA1_E, FN_MSIOF2_SYNC_E,
		0, 0, 0,
		/* IP6_20_19 [2] */
		FN_IRQ5, FN_HTX1_C, FN_SCL1_E, FN_MSIOF2_SCK_E,
		/* IP6_18_16 [3] */
		FN_IRQ4, FN_HRX1_C, FN_SDA4_C, FN_MSIOF2_RXD_E, FN_INTC_IRQ4_N,
		0, 0, 0,
		/* IP6_15_14 [2] */
		FN_IRQ3, FN_SCL4_C, FN_MSIOF2_TXD_E, FN_INTC_IRQ3_N,
		/* IP6_13_12 [2] */
		FN_IRQ2, FN_SCIFB1_TXD_D, FN_INTC_IRQ2_N, 0,
		/* IP6_11_10 [2] */
		FN_IRQ1, FN_SCIFB1_SCK_C, FN_INTC_IRQ1_N, 0,
		/* IP6_9_8 [2] */
		FN_IRQ0, FN_SCIFB1_RXD_D, FN_INTC_IRQ0_N, 0,
		/* IP6_7_6 [2] */
		FN_AUDIO_CLKOUT, FN_MSIOF1_SS1_B, FN_TX2, FN_SCIFA2_TXD,
		/* IP6_5_3 [3] */
		FN_AUDIO_CLKC, FN_SCIFB0_SCK_C, FN_MSIOF1_SYNC_B, FN_RX2,
		FN_SCIFA2_RXD, FN_FMIN_E,
		0, 0,
		/* IP6_2_0 [3] */
		FN_AUDIO_CLKB, FN_STP_OPWM_0_B, FN_MSIOF1_SCK_B,
		FN_SCIF_CLK, 0, FN_BPFCLK_E,
		0, 0, }
	},

	/* IPSR7 - 10 */

	{ PINMUX_CFG_REG_VAR("IPSR11", 0xE606004C, 32,
			     2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2,
			     3, 3, 3, 3, 3) {
		/* IP11_31_30 [2] */
		FN_ETH_CRS_DV, FN_AVB_LINK, FN_SDA2_C, 0,
		/* IP11_29_28 [2] */
		FN_ETH_MDIO, FN_AVB_RX_CLK, FN_SCL2_C, 0,
		/* IP11_27 [1] */
		FN_VI1_DATA7, FN_AVB_MDC,
		/* IP11_26 [1] */
		FN_VI1_DATA6, FN_AVB_MAGIC,
		/* IP11_25 [1] */
		FN_VI1_DATA5, FN_AVB_RX_DV,
		/* IP11_24 [1] */
		FN_VI1_DATA4, FN_AVB_MDIO,
		/* IP11_23 [1] */
		FN_VI1_DATA3, FN_AVB_RX_ER,
		/* IP11_22 [1] */
		FN_VI1_DATA2, FN_AVB_RXD7,
		/* IP11_21 [1] */
		FN_VI1_DATA1, FN_AVB_RXD6,
		/* IP11_20 [1] */
		FN_VI1_DATA0, FN_AVB_RXD5,
		/* IP11_19 [1] */
		FN_VI1_CLK, FN_AVB_RXD4,
		/* IP11_18_17 [2] */
		FN_VI1_FIELD, FN_AVB_RXD3, FN_TS_SPSYNC0_B, 0,
		/* IP11_16_15 [2] */
		FN_VI1_CLKENB, FN_AVB_RXD2, FN_TS_SDEN0_B, 0,
		/* IP11_14_12 [3] */
		FN_VI1_VSYNC_N, FN_AVB_RXD1, FN_TS_SCK0_B,
		FN_RX4_B, FN_SCIFA4_RXD_B,
		0, 0, 0,
		/* IP11_11_9 [3] */
		FN_VI1_HSYNC_N, FN_AVB_RXD0, FN_TS_SDATA0_B,
		FN_TX4_B, FN_SCIFA4_TXD_B,
		0, 0, 0,
		/* IP11_8_6 [3] */
		FN_VI0_R7, FN_GLO_RFON_B, FN_RX1_C, FN_CAN0_RX_E,
		FN_SDA4_B, FN_HRX1_D, FN_SCIFB0_RXD_D, 0,
		/* IP11_5_3 [3] */
		FN_VI0_R6, FN_VI2_DATA7, FN_GLO_SS_B, FN_TX1_C, FN_SCL4_B,
		0, 0, 0,
		/* IP11_2_0 [3] */
		FN_VI0_R5, FN_VI2_DATA6, FN_GLO_SDATA_B, FN_RX0_C, FN_SDA1_D,
		0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR12", 0xE6060050, 32,
			     2, 3, 3, 2, 2, 2, 2, 3, 3, 3, 3, 2, 2) {
		/* IP12_31_30 [2] */
		0, 0, 0, 0,
		/* IP12_29_27 [3] */
		FN_STP_ISCLK_0, FN_AVB_TX_EN, FN_SCIFB2_RXD_D,
		FN_ADICS_SAMP_B, FN_MSIOF0_SCK_C,
		0, 0, 0,
		/* IP12_26_24 [3] */
		FN_STP_IVCXO27_0, FN_AVB_TXD7, FN_SCIFB2_TXD_D,
		FN_ADIDATA_B, FN_MSIOF0_SYNC_C,
		0, 0, 0,
		/* IP12_23_22 [2] */
		FN_ETH_MDC, FN_AVB_TXD6, FN_IERX_C, 0,
		/* IP12_21_20 [2] */
		FN_ETH_TXD0, FN_AVB_TXD5, FN_IECLK_C, 0,
		/* IP12_19_18 [2] */
		FN_ETH_MAGIC, FN_AVB_TXD4, FN_IETX_C, 0,
		/* IP12_17_16 [2] */
		FN_ETH_TX_EN, FN_AVB_TXD3, FN_TCLK1_B, FN_CAN_CLK_B,
		/* IP12_15_13 [3] */
		FN_ETH_TXD1, FN_AVB_TXD2, FN_SCIFA3_TXD_B,
		FN_CAN1_TX_C, FN_MSIOF1_TXD_E,
		0, 0, 0,
		/* IP12_12_10 [3] */
		FN_ETH_REFCLK, FN_AVB_TXD1, FN_SCIFA3_RXD_B,
		FN_CAN1_RX_C, FN_MSIOF1_SYNC_E,
		0, 0, 0,
		/* IP12_9_7 [3] */
		FN_ETH_LINK, FN_AVB_TXD0, FN_CAN0_RX_C,
		FN_SDA2_D, FN_MSIOF1_SCK_E,
		0, 0, 0,
		/* IP12_6_4 [3] */
		FN_ETH_RXD1, FN_AVB_GTXREFCLK, FN_CAN0_TX_C,
		FN_SCL2_D, FN_MSIOF1_RXD_E,
		0, 0, 0,
		/* IP12_3_2 [2] */
		FN_ETH_RXD0, FN_AVB_PHY_INT, FN_SDA3, FN_SDA7,
		/* IP12_1_0 [2] */
		FN_ETH_RX_ER, FN_AVB_CRS, FN_SCL3, FN_SCL7, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR13", 0xE6060054, 32,
			     1, 3, 1, 1, 1, 2, 1, 3, 3, 1, 1, 1, 1, 1, 1,
			     3, 2, 2, 3) {
		/* IP13_31 [1] */
		0, 0,
		/* IP13_30_28 [3] */
		FN_SD1_CD, FN_PWM0, FN_TPU_TO0, FN_SCL1_C,
		0, 0, 0, 0,
		/* IP13_27 [1] */
		FN_SD1_DATA3, FN_IERX_B,
		/* IP13_26 [1] */
		FN_SD1_DATA2, FN_IECLK_B,
		/* IP13_25 [1] */
		FN_SD1_DATA1, FN_IETX_B,
		/* IP13_24_23 [2] */
		FN_SD1_DATA0, FN_SPEEDIN_B, 0, 0,
		/* IP13_22 [1] */
		FN_SD1_CMD, FN_REMOCON_B,
		/* IP13_21_19 [3] */
		FN_SD0_WP, FN_MMC_D7_B, FN_SIM0_D_B, FN_CAN0_TX_F,
		FN_SCIFA5_RXD_B, FN_RX3_C,
		0, 0,
		/* IP13_18_16 [3] */
		FN_SD0_CD, FN_MMC_D6_B, FN_SIM0_RST_B, FN_CAN0_RX_F,
		FN_SCIFA5_TXD_B, FN_TX3_C,
		0, 0,
		/* IP13_15 [1] */
		FN_SD0_DATA3, FN_SSL_B,
		/* IP13_14 [1] */
		FN_SD0_DATA2, FN_IO3_B,
		/* IP13_13 [1] */
		FN_SD0_DATA1, FN_IO2_B,
		/* IP13_12 [1] */
		FN_SD0_DATA0, FN_MISO_IO1_B,
		/* IP13_11 [1] */
		FN_SD0_CMD, FN_MOSI_IO0_B,
		/* IP13_10 [1] */
		FN_SD0_CLK, FN_SPCLK_B,
		/* IP13_9_7 [3] */
		FN_STP_OPWM_0, FN_AVB_GTX_CLK, FN_PWM0_B,
		FN_ADICHS2_B, FN_MSIOF0_TXD_C,
		0, 0, 0,
		/* IP13_6_5 [2] */
		FN_STP_ISSYNC_0, FN_AVB_COL, FN_ADICHS1_B, FN_MSIOF0_RXD_C,
		/* IP13_4_3 [2] */
		FN_STP_ISEN_0, FN_AVB_TX_CLK, FN_ADICHS0_B, FN_MSIOF0_SS2_C,
		/* IP13_2_0 [3] */
		FN_STP_ISD_0, FN_AVB_TX_ER, FN_SCIFB2_SCK_C,
		FN_ADICLK_B, FN_MSIOF0_SS1_C,
		0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR14", 0xE6060058, 32,
			     3, 3, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 2) {
		/* IP14_31_29 [3] */
		FN_MSIOF0_SS2, FN_MMC_D7, FN_ADICHS2, FN_RX0_E,
		FN_VI1_VSYNC_N_C, FN_SDA7_C, FN_VI1_G5_B, 0,
		/* IP14_28_26 [3] */
		FN_MSIOF0_SS1, FN_MMC_D6, FN_ADICHS1, FN_TX0_E,
		FN_VI1_HSYNC_N_C, FN_SCL7_C, FN_VI1_G4_B, 0,
		/* IP14_25_23 [3] */
		FN_MSIOF0_RXD, FN_ADICHS0, 0, FN_VI1_DATA0_C, FN_VI1_G3_B,
		0, 0, 0,
		/* IP14_22_20 [3] */
		FN_MSIOF0_TXD, FN_ADICLK, 0, FN_VI1_FIELD_C, FN_VI1_G2_B,
		0, 0, 0,
		/* IP14_19_17 [3] */
		FN_MSIOF0_SYNC, FN_TX2_C, FN_ADICS_SAMP, 0,
		FN_VI1_CLKENB_C, FN_VI1_G1_B,
		0, 0,
		/* IP14_16_14 [3] */
		FN_MSIOF0_SCK, FN_RX2_C, FN_ADIDATA, 0,
		FN_VI1_CLK_C, FN_VI1_G0_B,
		0, 0,
		/* IP14_13_11 [3] */
		FN_SD2_WP, FN_MMC_D5, FN_SDA8_C, FN_RX5_B, FN_SCIFA5_RXD_C,
		0, 0, 0,
		/* IP14_10_8 [3] */
		FN_SD2_CD, FN_MMC_D4, FN_SCL8_C, FN_TX5_B, FN_SCIFA5_TXD_C,
		0, 0, 0,
		/* IP14_7 [1] */
		FN_SD2_DATA3, FN_MMC_D3,
		/* IP14_6 [1] */
		FN_SD2_DATA2, FN_MMC_D2,
		/* IP14_5 [1] */
		FN_SD2_DATA1, FN_MMC_D1,
		/* IP14_4 [1] */
		FN_SD2_DATA0, FN_MMC_D0,
		/* IP14_3 [1] */
		FN_SD2_CMD, FN_MMC_CMD,
		/* IP14_2 [1] */
		FN_SD2_CLK, FN_MMC_CLK,
		/* IP14_1_0 [2] */
		FN_SD1_WP, FN_PWM1_B, FN_SDA1_C, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR15", 0xE606005C, 32,
			     2, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2) {
		/* IP15_31_30 [2] */
		0, 0, 0, 0,
		/* IP15_29_27 [3] */
		FN_HTX0, FN_SCIFB0_TXD, 0, FN_GLO_SCLK_C,
		FN_CAN0_TX_B, FN_VI1_DATA5_C,
		0, 0,
		/* IP15_26_24 [3] */
		FN_HRX0, FN_SCIFB0_RXD, 0, FN_GLO_Q1_C,
		FN_CAN0_RX_B, FN_VI1_DATA4_C,
		0, 0,
		/* IP15_23_21 [3] */
		FN_HSCK0, FN_SCIFB0_SCK, 0, FN_GLO_Q0_C, FN_CAN_CLK,
		FN_TCLK2, FN_VI1_DATA3_C, 0,
		/* IP15_20_18 [3] */
		FN_HRTS0_N, FN_SCIFB0_RTS_N, 0, FN_GLO_I1_C, FN_VI1_DATA2_C,
		0, 0, 0,
		/* IP15_17_15 [3] */
		FN_HCTS0_N, FN_SCIFB0_CTS_N, 0, FN_GLO_I0_C,
		FN_TCLK1, FN_VI1_DATA1_C,
		0, 0,
		/* IP15_14_12 [3] */
		FN_GPS_MAG, FN_RX4_C, FN_SCIFA4_RXD_C, FN_PWM6,
		FN_VI1_G7_B, FN_SCIFA3_SCK_C,
		0, 0,
		/* IP15_11_9 [3] */
		FN_GPS_SIGN, FN_TX4_C, FN_SCIFA4_TXD_C, FN_PWM5,
		FN_VI1_G6_B, FN_SCIFA3_RXD_C,
		0, 0,
		/* IP15_8_6 [3] */
		FN_GPS_CLK, FN_DU1_DOTCLKIN_C, FN_AUDIO_CLKB_B,
		FN_PWM5_B, FN_SCIFA3_TXD_C,
		0, 0, 0,
		/* IP15_5_4 [2] */
		FN_SIM0_D, FN_IERX, FN_CAN1_RX_D, 0,
		/* IP15_3_2 [2] */
		FN_SIM0_CLK, FN_IECLK, FN_CAN_CLK_C, 0,
		/* IP15_1_0 [2] */
		FN_SIM0_RST, FN_IETX, FN_CAN1_TX_D, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR16", 0xE6060160, 32,
			     4, 4, 4, 4, 4, 2, 2, 2, 3, 3) {
		/* IP16_31_28 [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* IP16_27_24 [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* IP16_23_20 [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* IP16_19_16 [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* IP16_15_12 [4] */
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0,
		/* IP16_11_10 [2] */
		FN_HRTS1_N, FN_SCIFB1_RTS_N, FN_MLB_DAT, FN_CAN1_RX_B,
		/* IP16_9_8 [2] */
		FN_HCTS1_N, FN_SCIFB1_CTS_N, FN_MLB_SIG, FN_CAN1_TX_B,
		/* IP16_7_6 [2] */
		FN_HSCK1, FN_SCIFB1_SCK, FN_MLB_CK, FN_GLO_RFON_C,
		/* IP16_5_3 [3] */
		FN_HTX1, FN_SCIFB1_TXD, FN_VI1_R1_B,
		FN_GLO_SS_C, FN_VI1_DATA7_C,
		0, 0, 0,
		/* IP16_2_0 [3] */
		FN_HRX1, FN_SCIFB1_RXD, FN_VI1_R0_B,
		FN_GLO_SDATA_C, FN_VI1_DATA6_C,
		0, 0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL", 0xE6060090, 32,
			     1, 2, 2, 2, 3, 2, 1, 1, 1, 1,
			     3, 2, 2, 2, 1, 2, 2, 2) {
		/* RESEVED [1] */
		0, 0,
		/* SEL_SCIF1 [2] */
		FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, FN_SEL_SCIF1_3,
		/* SEL_SCIFB [2] */
		FN_SEL_SCIFB_0, FN_SEL_SCIFB_1, FN_SEL_SCIFB_2, FN_SEL_SCIFB_3,
		/* SEL_SCIFB2 [2] */
		FN_SEL_SCIFB2_0, FN_SEL_SCIFB2_1,
		FN_SEL_SCIFB2_2, FN_SEL_SCIFB2_3,
		/* SEL_SCIFB1 [3] */
		FN_SEL_SCIFB1_0, FN_SEL_SCIFB1_1,
		FN_SEL_SCIFB1_2, FN_SEL_SCIFB1_3,
		0, 0, 0, 0,
		/* SEL_SCIFA1 [2] */
		FN_SEL_SCIFA1_0, FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2, 0,
		/* SEL_SSI9 [1] */
		FN_SEL_SSI9_0, FN_SEL_SSI9_1,
		/* SEL_SCFA [1] */
		FN_SEL_SCFA_0, FN_SEL_SCFA_1,
		/* SEL_QSP [1] */
		FN_SEL_QSP_0, FN_SEL_QSP_1,
		/* SEL_SSI7 [1] */
		FN_SEL_SSI7_0, FN_SEL_SSI7_1,
		/* SEL_HSCIF1 [3] */
		FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1, FN_SEL_HSCIF1_2,
		FN_SEL_HSCIF1_3, FN_SEL_HSCIF1_4,
		0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_VI1 [2] */
		FN_SEL_VI1_0, FN_SEL_VI1_1, FN_SEL_VI1_2, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_TMU [1] */
		FN_SEL_TMU1_0, FN_SEL_TMU1_1,
		/* SEL_LBS [2] */
		FN_SEL_LBS_0, FN_SEL_LBS_1, FN_SEL_LBS_2, FN_SEL_LBS_3,
		/* SEL_TSIF0 [2] */
		FN_SEL_TSIF0_0, FN_SEL_TSIF0_1, FN_SEL_TSIF0_2, FN_SEL_TSIF0_3,
		/* SEL_SOF0 [2] */
		FN_SEL_SOF0_0, FN_SEL_SOF0_1, FN_SEL_SOF0_2, 0, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL2", 0xE6060094, 32,
			     3, 1, 1, 3, 2, 1, 1, 2, 2,
			     1, 3, 2, 1, 2, 2, 2, 1, 1, 1) {
		/* SEL_SCIF0 [3] */
		FN_SEL_SCIF0_0, FN_SEL_SCIF0_1, FN_SEL_SCIF0_2,
		FN_SEL_SCIF0_3, FN_SEL_SCIF0_4,
		0, 0, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_SCIF [1] */
		FN_SEL_SCIF_0, FN_SEL_SCIF_1,
		/* SEL_CAN0 [3] */
		FN_SEL_CAN0_0, FN_SEL_CAN0_1, FN_SEL_CAN0_2, FN_SEL_CAN0_3,
		FN_SEL_CAN0_4, FN_SEL_CAN0_5,
		0, 0,
		/* SEL_CAN1 [2] */
		FN_SEL_CAN1_0, FN_SEL_CAN1_1, FN_SEL_CAN1_2, FN_SEL_CAN1_3,
		/* RESEVED [1] */
		0, 0,
		/* SEL_SCIFA2 [1] */
		FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1,
		/* SEL_SCIF4 [2] */
		FN_SEL_SCIF4_0, FN_SEL_SCIF4_1, FN_SEL_SCIF4_2, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_ADG [1] */
		FN_SEL_ADG_0, FN_SEL_ADG_1,
		/* SEL_FM [3] */
		FN_SEL_FM_0, FN_SEL_FM_1, FN_SEL_FM_2,
		FN_SEL_FM_3, FN_SEL_FM_4,
		0, 0, 0,
		/* SEL_SCIFA5 [2] */
		FN_SEL_SCIFA5_0, FN_SEL_SCIFA5_1, FN_SEL_SCIFA5_2, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_GPS [2] */
		FN_SEL_GPS_0, FN_SEL_GPS_1, FN_SEL_GPS_2, FN_SEL_GPS_3,
		/* SEL_SCIFA4 [2] */
		FN_SEL_SCIFA4_0, FN_SEL_SCIFA4_1, FN_SEL_SCIFA4_2, 0,
		/* SEL_SCIFA3 [2] */
		FN_SEL_SCIFA3_0, FN_SEL_SCIFA3_1, FN_SEL_SCIFA3_2, 0,
		/* SEL_SIM [1] */
		FN_SEL_SIM_0, FN_SEL_SIM_1,
		/* RESEVED [1] */
		0, 0,
		/* SEL_SSI8 [1] */
		FN_SEL_SSI8_0, FN_SEL_SSI8_1, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL3", 0xE6060098, 32,
			     2, 2, 2, 2, 2, 2, 2, 2,
			     1, 1, 2, 2, 3, 2, 2, 2, 1) {
		/* SEL_HSCIF2 [2] */
		FN_SEL_HSCIF2_0, FN_SEL_HSCIF2_1,
		FN_SEL_HSCIF2_2, FN_SEL_HSCIF2_3,
		/* SEL_CANCLK [2] */
		FN_SEL_CANCLK_0, FN_SEL_CANCLK_1,
		FN_SEL_CANCLK_2, FN_SEL_CANCLK_3,
		/* SEL_IIC8 [2] */
		FN_SEL_IIC8_0, FN_SEL_IIC8_1, FN_SEL_IIC8_2, 0,
		/* SEL_IIC7 [2] */
		FN_SEL_IIC7_0, FN_SEL_IIC7_1, FN_SEL_IIC7_2, 0,
		/* SEL_IIC4 [2] */
		FN_SEL_IIC4_0, FN_SEL_IIC4_1, FN_SEL_IIC4_2, 0,
		/* SEL_IIC3 [2] */
		FN_SEL_IIC3_0, FN_SEL_IIC3_1, FN_SEL_IIC3_2, FN_SEL_IIC3_3,
		/* SEL_SCIF3 [2] */
		FN_SEL_SCIF3_0, FN_SEL_SCIF3_1, FN_SEL_SCIF3_2, FN_SEL_SCIF3_3,
		/* SEL_IEB [2] */
		FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2, 0,
		/* SEL_MMC [1] */
		FN_SEL_MMC_0, FN_SEL_MMC_1,
		/* SEL_SCIF5 [1] */
		FN_SEL_SCIF5_0, FN_SEL_SCIF5_1,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_IIC2 [2] */
		FN_SEL_IIC2_0, FN_SEL_IIC2_1, FN_SEL_IIC2_2, FN_SEL_IIC2_3,
		/* SEL_IIC1 [3] */
		FN_SEL_IIC1_0, FN_SEL_IIC1_1, FN_SEL_IIC1_2, FN_SEL_IIC1_3,
		FN_SEL_IIC1_4,
		0, 0, 0,
		/* SEL_IIC0 [2] */
		FN_SEL_IIC0_0, FN_SEL_IIC0_1, FN_SEL_IIC0_2, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [1] */
		0, 0, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL4", 0xE606009C, 32,
			     3, 2, 2, 1, 1, 1, 1, 3, 2,
			     2, 3, 1, 1, 1, 2, 2, 2, 2) {
		/* SEL_SOF1 [3] */
		FN_SEL_SOF1_0, FN_SEL_SOF1_1, FN_SEL_SOF1_2, FN_SEL_SOF1_3,
		FN_SEL_SOF1_4,
		0, 0, 0,
		/* SEL_HSCIF0 [2] */
		FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1, FN_SEL_HSCIF0_2, 0,
		/* SEL_DIS [2] */
		FN_SEL_DIS_0, FN_SEL_DIS_1, FN_SEL_DIS_2, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_RAD [1] */
		FN_SEL_RAD_0, FN_SEL_RAD_1,
		/* SEL_RCN [1] */
		FN_SEL_RCN_0, FN_SEL_RCN_1,
		/* SEL_RSP [1] */
		FN_SEL_RSP_0, FN_SEL_RSP_1,
		/* SEL_SCIF2 [3] */
		FN_SEL_SCIF2_0, FN_SEL_SCIF2_1, FN_SEL_SCIF2_2,
		FN_SEL_SCIF2_3, FN_SEL_SCIF2_4,
		0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* SEL_SOF2 [3] */
		FN_SEL_SOF2_0, FN_SEL_SOF2_1, FN_SEL_SOF2_2,
		FN_SEL_SOF2_3, FN_SEL_SOF2_4,
		0, 0, 0,
		/* RESEVED [1] */
		0, 0,
		/* SEL_SSI1 [1] */
		FN_SEL_SSI1_0, FN_SEL_SSI1_1,
		/* SEL_SSI0 [1] */
		FN_SEL_SSI0_0, FN_SEL_SSI0_1,
		/* SEL_SSP [2] */
		FN_SEL_SSP_0, FN_SEL_SSP_1, FN_SEL_SSP_2, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0,
		/* RESEVED [2] */
		0, 0, 0, 0, }
	},
	{ PINMUX_CFG_REG("INOUTSEL0", 0xE6050004, 32, 1) { GP_INOUTSEL(0) } },
	{ PINMUX_CFG_REG("INOUTSEL1", 0xE6051004, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_1_25_IN, GP_1_25_OUT,
		GP_1_24_IN, GP_1_24_OUT,
		GP_1_23_IN, GP_1_23_OUT,
		GP_1_22_IN, GP_1_22_OUT,
		GP_1_21_IN, GP_1_21_OUT,
		GP_1_20_IN, GP_1_20_OUT,
		GP_1_19_IN, GP_1_19_OUT,
		GP_1_18_IN, GP_1_18_OUT,
		GP_1_17_IN, GP_1_17_OUT,
		GP_1_16_IN, GP_1_16_OUT,
		GP_1_15_IN, GP_1_15_OUT,
		GP_1_14_IN, GP_1_14_OUT,
		GP_1_13_IN, GP_1_13_OUT,
		GP_1_12_IN, GP_1_12_OUT,
		GP_1_11_IN, GP_1_11_OUT,
		GP_1_10_IN, GP_1_10_OUT,
		GP_1_9_IN, GP_1_9_OUT,
		GP_1_8_IN, GP_1_8_OUT,
		GP_1_7_IN, GP_1_7_OUT,
		GP_1_6_IN, GP_1_6_OUT,
		GP_1_5_IN, GP_1_5_OUT,
		GP_1_4_IN, GP_1_4_OUT,
		GP_1_3_IN, GP_1_3_OUT,
		GP_1_2_IN, GP_1_2_OUT,
		GP_1_1_IN, GP_1_1_OUT,
		GP_1_0_IN, GP_1_0_OUT, }
	},
	{ PINMUX_CFG_REG("INOUTSEL2", 0xE6052004, 32, 1) { GP_INOUTSEL(2) } },
	{ PINMUX_CFG_REG("INOUTSEL3", 0xE6053004, 32, 1) { GP_INOUTSEL(3) } },
	{ PINMUX_CFG_REG("INOUTSEL4", 0xE6054004, 32, 1) { GP_INOUTSEL(4) } },
	{ PINMUX_CFG_REG("INOUTSEL5", 0xE6055004, 32, 1) { GP_INOUTSEL(5) } },
	{ PINMUX_CFG_REG("INOUTSEL6", 0xE6055404, 32, 1) { GP_INOUTSEL(6) } },
	{ PINMUX_CFG_REG("INOUTSEL7", 0xE6055804, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_7_25_IN, GP_7_25_OUT,
		GP_7_24_IN, GP_7_24_OUT,
		GP_7_23_IN, GP_7_23_OUT,
		GP_7_22_IN, GP_7_22_OUT,
		GP_7_21_IN, GP_7_21_OUT,
		GP_7_20_IN, GP_7_20_OUT,
		GP_7_19_IN, GP_7_19_OUT,
		GP_7_18_IN, GP_7_18_OUT,
		GP_7_17_IN, GP_7_17_OUT,
		GP_7_16_IN, GP_7_16_OUT,
		GP_7_15_IN, GP_7_15_OUT,
		GP_7_14_IN, GP_7_14_OUT,
		GP_7_13_IN, GP_7_13_OUT,
		GP_7_12_IN, GP_7_12_OUT,
		GP_7_11_IN, GP_7_11_OUT,
		GP_7_10_IN, GP_7_10_OUT,
		GP_7_9_IN, GP_7_9_OUT,
		GP_7_8_IN, GP_7_8_OUT,
		GP_7_7_IN, GP_7_7_OUT,
		GP_7_6_IN, GP_7_6_OUT,
		GP_7_5_IN, GP_7_5_OUT,
		GP_7_4_IN, GP_7_4_OUT,
		GP_7_3_IN, GP_7_3_OUT,
		GP_7_2_IN, GP_7_2_OUT,
		GP_7_1_IN, GP_7_1_OUT,
		GP_7_0_IN, GP_7_0_OUT, }
	},
	{ },
};

static struct pinmux_data_reg pinmux_data_regs[] = {
	{ PINMUX_DATA_REG("INDT0", 0xE6050008, 32) { GP_INDT(0) } },
	{ PINMUX_DATA_REG("INDT1", 0xE6051008, 32) {
		0, 0, 0, 0,
		0, 0, GP_1_25_DATA, GP_1_24_DATA,
		GP_1_23_DATA, GP_1_22_DATA, GP_1_21_DATA, GP_1_20_DATA,
		GP_1_19_DATA, GP_1_18_DATA, GP_1_17_DATA, GP_1_16_DATA,
		GP_1_15_DATA, GP_1_14_DATA, GP_1_13_DATA, GP_1_12_DATA,
		GP_1_11_DATA, GP_1_10_DATA, GP_1_9_DATA, GP_1_8_DATA,
		GP_1_7_DATA, GP_1_6_DATA, GP_1_5_DATA, GP_1_4_DATA,
		GP_1_3_DATA, GP_1_2_DATA, GP_1_1_DATA, GP_1_0_DATA }
	},
	{ PINMUX_DATA_REG("INDT2", 0xE6052008, 32) { GP_INDT(2) } },
	{ PINMUX_DATA_REG("INDT3", 0xE6053008, 32) { GP_INDT(3) } },
	{ PINMUX_DATA_REG("INDT4", 0xE6054008, 32) { GP_INDT(4) } },
	{ PINMUX_DATA_REG("INDT5", 0xE6055008, 32) { GP_INDT(5) } },
	{ PINMUX_DATA_REG("INDT6", 0xE6055408, 32) { GP_INDT(6) } },
	{ PINMUX_DATA_REG("INDT7", 0xE6055808, 32) {
		0, 0, 0, 0,
		0, 0, GP_7_25_DATA, GP_7_24_DATA,
		GP_7_23_DATA, GP_7_22_DATA, GP_7_21_DATA, GP_7_20_DATA,
		GP_7_19_DATA, GP_7_18_DATA, GP_7_17_DATA, GP_7_16_DATA,
		GP_7_15_DATA, GP_7_14_DATA, GP_7_13_DATA, GP_7_12_DATA,
		GP_7_11_DATA, GP_7_10_DATA, GP_7_9_DATA, GP_7_8_DATA,
		GP_7_7_DATA, GP_7_6_DATA, GP_7_5_DATA, GP_7_4_DATA,
		GP_7_3_DATA, GP_7_2_DATA, GP_7_1_DATA, GP_7_0_DATA }
	},
	{ },
};

static struct pinmux_info r8a7793_pinmux_info = {
	.name = "r8a7793_pfc",

	.unlock_reg = 0xe6060000, /* PMMR */

	.reserved_id = PINMUX_RESERVED,
	.data = { PINMUX_DATA_BEGIN, PINMUX_DATA_END },
	.input = { PINMUX_INPUT_BEGIN, PINMUX_INPUT_END },
	.output = { PINMUX_OUTPUT_BEGIN, PINMUX_OUTPUT_END },
	.mark = { PINMUX_MARK_BEGIN, PINMUX_MARK_END },
	.function = { PINMUX_FUNCTION_BEGIN, PINMUX_FUNCTION_END },

	.first_gpio = GPIO_GP_0_0,
	.last_gpio = GPIO_FN_CAN1_RX_B,

	.gpios = pinmux_gpios,
	.cfg_regs = pinmux_config_regs,
	.data_regs = pinmux_data_regs,

	.gpio_data = pinmux_data,
	.gpio_data_size = ARRAY_SIZE(pinmux_data),
};

void r8a7793_pinmux_init(void)
{
	register_pinmux(&r8a7793_pinmux_info);
}
