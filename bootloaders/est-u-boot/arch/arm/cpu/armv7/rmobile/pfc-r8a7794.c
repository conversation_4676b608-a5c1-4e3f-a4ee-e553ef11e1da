/*
 * arch/arm/cpu/armv7/rmobile/pfc-r8a7794.c
 *     This file is r8a7794 processor support - PFC hardware block.
 *
 * Copyright (C) 2014 Renesas Electronics Corporation
 *
 * SPDX-License-Identifier: GPL-2.0
 */

#include <common.h>
#include <sh_pfc.h>
#include <asm/gpio.h>

#define CPU_32_PORT(fn, pfx, sfx)				\
	PORT_10(fn, pfx, sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_10(fn, pfx##2, sfx), PORT_1(fn, pfx##30, sfx),	\
	PORT_1(fn, pfx##31, sfx)

#define CPU_26_PORT(fn, pfx, sfx)				\
	PORT_10(fn, pfx, sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_1(fn, pfx##20, sfx), PORT_1(fn, pfx##21, sfx),	\
	PORT_1(fn, pfx##22, sfx), PORT_1(fn, pfx##23, sfx),	\
	PORT_1(fn, pfx##24, sfx), PORT_1(fn, pfx##25, sfx)

#define CPU_28_PORT(fn, pfx, sfx)				\
	PORT_10(fn, pfx, sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_1(fn, pfx##20, sfx), PORT_1(fn, pfx##21, sfx),	\
	PORT_1(fn, pfx##22, sfx), PORT_1(fn, pfx##23, sfx),	\
	PORT_1(fn, pfx##24, sfx), PORT_1(fn, pfx##25, sfx),	\
	PORT_1(fn, pfx##26, sfx), PORT_1(fn, pfx##27, sfx)

/*
 * GP_0_0_DATA -> GP_6_25_DATA
 * (except for GP1[26],GP1[27],GP1[28],GP1[29]),GP1[30],GP1[31]
 *  GP5[28],GP5[29]),GP5[30],GP5[31],GP6[26],GP6[27],GP6[28],
 *  GP6[29]),GP6[30],GP6[31])
 */
#define CPU_ALL_PORT(fn, pfx, sfx)			\
	CPU_32_PORT(fn, pfx##_0_, sfx),			\
	CPU_26_PORT(fn, pfx##_1_, sfx),			\
	CPU_32_PORT(fn, pfx##_2_, sfx),			\
	CPU_32_PORT(fn, pfx##_3_, sfx),			\
	CPU_32_PORT(fn, pfx##_4_, sfx),			\
	CPU_28_PORT(fn, pfx##_5_, sfx),			\
	CPU_26_PORT(fn, pfx##_6_, sfx)

#define _GP_GPIO(pfx, sfx) PINMUX_GPIO(GPIO_GP##pfx, GP##pfx##_DATA)
#define _GP_DATA(pfx, sfx) PINMUX_DATA(GP##pfx##_DATA, GP##pfx##_FN,	\
				       GP##pfx##_IN, GP##pfx##_OUT)

#define _GP_INOUTSEL(pfx, sfx) GP##pfx##_IN, GP##pfx##_OUT
#define _GP_INDT(pfx, sfx) GP##pfx##_DATA

#define GP_ALL(str)	CPU_ALL_PORT(_PORT_ALL, GP, str)
#define PINMUX_GPIO_GP_ALL()	CPU_ALL_PORT(_GP_GPIO, , unused)
#define PINMUX_DATA_GP_ALL()	CPU_ALL_PORT(_GP_DATA, , unused)


#define PORT_10_REV(fn, pfx, sfx)				\
	PORT_1(fn, pfx##9, sfx), PORT_1(fn, pfx##8, sfx),	\
	PORT_1(fn, pfx##7, sfx), PORT_1(fn, pfx##6, sfx),	\
	PORT_1(fn, pfx##5, sfx), PORT_1(fn, pfx##4, sfx),	\
	PORT_1(fn, pfx##3, sfx), PORT_1(fn, pfx##2, sfx),	\
	PORT_1(fn, pfx##1, sfx), PORT_1(fn, pfx##0, sfx)

#define CPU_32_PORT_REV(fn, pfx, sfx)					\
	PORT_1(fn, pfx##31, sfx), PORT_1(fn, pfx##30, sfx),		\
	PORT_10_REV(fn, pfx##2, sfx), PORT_10_REV(fn, pfx##1, sfx),	\
	PORT_10_REV(fn, pfx, sfx)

#define GP_INOUTSEL(bank) CPU_32_PORT_REV(_GP_INOUTSEL, _##bank##_, unused)
#define GP_INDT(bank) CPU_32_PORT_REV(_GP_INDT, _##bank##_, unused)

#define PINMUX_IPSR_DATA(ipsr, fn) PINMUX_DATA(fn##_MARK, FN_##ipsr, FN_##fn)
#define PINMUX_IPSR_MODSEL_DATA(ipsr, fn, ms) PINMUX_DATA(fn##_MARK, FN_##ms, \
							  FN_##ipsr, FN_##fn)

enum {
	PINMUX_RESERVED = 0,

	PINMUX_DATA_BEGIN,
	GP_ALL(DATA),
	PINMUX_DATA_END,

	PINMUX_INPUT_BEGIN,
	GP_ALL(IN),
	PINMUX_INPUT_END,

	PINMUX_OUTPUT_BEGIN,
	GP_ALL(OUT),
	PINMUX_OUTPUT_END,

	PINMUX_FUNCTION_BEGIN,
	GP_ALL(FN),

	/* GPSR0 */
	FN_IP0_23_22, FN_IP0_24, FN_IP0_25, FN_IP0_27_26, FN_IP0_29_28,
	FN_IP0_31_30, FN_IP1_1_0, FN_IP1_3_2, FN_IP1_5_4, FN_IP1_7_6,
	FN_IP1_10_8, FN_IP1_12_11, FN_IP1_14_13, FN_IP1_17_15, FN_IP1_19_18,
	FN_IP1_21_20, FN_IP1_23_22, FN_IP1_24, FN_A2, FN_IP1_26, FN_IP1_27,
	FN_IP1_29_28, FN_IP1_31_30, FN_IP2_1_0, FN_IP2_3_2, FN_IP2_5_4,
	FN_IP2_7_6, FN_IP2_9_8, FN_IP2_11_10, FN_IP2_13_12, FN_IP2_15_14,
	FN_IP2_17_16,

	/* GPSR1 */
	FN_IP2_20_18, FN_IP2_23_21, FN_IP2_26_24, FN_IP2_29_27, FN_IP2_31_30,
	FN_IP3_1_0, FN_IP3_3_2, FN_IP3_5_4, FN_IP3_7_6, FN_IP3_9_8, FN_IP3_10,
	FN_IP3_11, FN_IP3_12, FN_IP3_14_13, FN_IP3_17_15, FN_IP3_20_18,
	FN_IP3_23_21, FN_IP3_26_24, FN_IP3_29_27, FN_IP3_30, FN_IP3_31,
	FN_WE0_N, FN_WE1_N, FN_IP4_1_0 , FN_IP7_31, FN_DACK0,

	/* GPSR2 */
	FN_IP4_4_2, FN_IP4_7_5, FN_IP4_9_8, FN_IP4_11_10, FN_IP4_13_12,
	FN_IP4_15_14, FN_IP4_17_16, FN_IP4_19_18, FN_IP4_22_20, FN_IP4_25_23,
	FN_IP4_27_26, FN_IP4_29_28, FN_IP4_31_30, FN_IP5_1_0, FN_IP5_3_2,
	FN_IP5_5_4, FN_IP5_8_6, FN_IP5_11_9, FN_IP5_13_12, FN_IP5_15_14,
	FN_IP5_17_16, FN_IP5_19_18, FN_IP5_21_20, FN_IP5_23_22, FN_IP5_25_24,
	FN_IP5_27_26, FN_IP5_29_28, FN_IP5_31_30, FN_IP6_1_0, FN_IP6_3_2,
	FN_IP6_5_4, FN_IP6_7_6,

	/* GPSR3 */
	FN_IP6_8, FN_IP6_9, FN_IP6_10, FN_IP6_11, FN_IP6_12, FN_IP6_13,
	FN_IP6_14, FN_IP6_15, FN_IP6_16, FN_IP6_19_17, FN_IP6_22_20,
	FN_IP6_25_23, FN_IP6_28_26, FN_IP6_31_29, FN_IP7_2_0, FN_IP7_5_3,
	FN_IP7_8_6, FN_IP7_11_9, FN_IP7_14_12, FN_IP7_17_15, FN_IP7_20_18,
	FN_IP7_23_21, FN_IP7_26_24, FN_IP7_29_27, FN_IP8_2_0, FN_IP8_5_3,
	FN_IP8_8_6, FN_IP8_11_9, FN_IP8_14_12, FN_IP8_16_15, FN_IP8_19_17,
	FN_IP8_22_20,

	/* GPSR4 */
	FN_IP8_25_23, FN_IP8_28_26, FN_IP8_31_29, FN_IP9_2_0, FN_IP9_5_3,
	FN_IP9_8_6, FN_IP9_11_9, FN_IP9_14_12, FN_IP9_16_15, FN_IP9_18_17,
	FN_IP9_21_19, FN_IP9_24_22, FN_IP9_27_25, FN_IP9_30_28, FN_IP10_2_0,
	FN_IP10_5_3, FN_IP10_8_6, FN_IP10_11_9, FN_IP10_14_12, FN_IP10_17_15,
	FN_IP10_20_18, FN_IP10_23_21, FN_IP10_26_24, FN_IP10_29_27,
	FN_IP10_31_30, FN_IP11_2_0, FN_IP11_5_3, FN_IP11_7_6, FN_IP11_10_8,
	FN_IP11_13_11, FN_IP11_15_14, FN_IP11_17_16,

	/* GPSR5 */
	FN_IP11_20_18, FN_IP11_23_21, FN_IP11_26_24, FN_IP11_29_27, FN_IP12_2_0,
	FN_IP12_5_3, FN_IP12_8_6, FN_IP12_10_9, FN_IP12_12_11, FN_IP12_14_13,
	FN_IP12_17_15, FN_IP12_20_18, FN_IP12_23_21, FN_IP12_26_24,
	FN_IP12_29_27, FN_IP13_2_0, FN_IP13_5_3, FN_IP13_8_6, FN_IP13_11_9,
	FN_IP13_14_12, FN_IP13_17_15, FN_IP13_20_18, FN_IP13_23_21,
	FN_IP13_26_24, FN_USB0_PWEN, FN_USB0_OVC, FN_USB1_PWEN, FN_USB1_OVC,

	/* GPSR6 */
	FN_SD0_CLK, FN_SD0_CMD, FN_SD0_DATA0, FN_SD0_DATA1, FN_SD0_DATA2,
	FN_SD0_DATA3, FN_SD0_CD, FN_SD0_WP, FN_SD1_CLK, FN_SD1_CMD,
	FN_SD1_DATA0, FN_SD1_DATA1, FN_SD1_DATA2, FN_SD1_DATA3, FN_IP0_0,
	FN_IP0_9_8, FN_IP0_10, FN_IP0_11, FN_IP0_12, FN_IP0_13, FN_IP0_14,
	FN_IP0_15, FN_IP0_16, FN_IP0_17, FN_IP0_19_18, FN_IP0_21_20,

	/* IPSR0 */
	FN_SD1_CD, FN_CAN0_RX, FN_SD1_WP, FN_IRQ7, FN_CAN0_TX, FN_MMC_CLK,
	FN_SD2_CLK, FN_MMC_CMD, FN_SD2_CMD, FN_MMC_D0, FN_SD2_DATA0, FN_MMC_D1,
	FN_SD2_DATA1, FN_MMC_D2, FN_SD2_DATA2, FN_MMC_D3, FN_SD2_DATA3,
	FN_MMC_D4, FN_SD2_CD, FN_MMC_D5, FN_SD2_WP, FN_MMC_D6, FN_SCIF0_RXD,
	FN_I2C2_SCL_B, FN_CAN1_RX, FN_MMC_D7, FN_SCIF0_TXD, FN_I2C2_SDA_B,
	FN_CAN1_TX, FN_D0, FN_SCIFA3_SCK_B, FN_IRQ4, FN_D1, FN_SCIFA3_RXD_B,
	FN_D2, FN_SCIFA3_TXD_B, FN_D3, FN_I2C3_SCL_B, FN_SCIF5_RXD_B, FN_D4,
	FN_I2C3_SDA_B, FN_SCIF5_TXD_B, FN_D5, FN_SCIF4_RXD_B, FN_I2C0_SCL_D,

	/*
	 * From IPSR1 to IPSR5 have been removed because they does not use.
	 */

	/* IPSR6 */
	FN_DU0_EXVSYNC_DU0_VSYNC, FN_QSTB_QHE, FN_CC50_STATE28,
	FN_DU0_EXODDF_DU0_ODDF_DISP_CDE, FN_QCPV_QDE, FN_CC50_STATE29,
	FN_DU0_DISP, FN_QPOLA, FN_CC50_STATE30, FN_DU0_CDE, FN_QPOLB,
	FN_CC50_STATE31, FN_VI0_CLK, FN_AVB_RX_CLK, FN_VI0_DATA0_VI0_B0,
	FN_AVB_RX_DV, FN_VI0_DATA1_VI0_B1, FN_AVB_RXD0, FN_VI0_DATA2_VI0_B2,
	FN_AVB_RXD1, FN_VI0_DATA3_VI0_B3, FN_AVB_RXD2, FN_VI0_DATA4_VI0_B4,
	FN_AVB_RXD3, FN_VI0_DATA5_VI0_B5, FN_AVB_RXD4, FN_VI0_DATA6_VI0_B6,
	FN_AVB_RXD5, FN_VI0_DATA7_VI0_B7, FN_AVB_RXD6, FN_VI0_CLKENB,
	FN_I2C3_SCL, FN_SCIFA5_RXD_C, FN_IETX_C, FN_AVB_RXD7, FN_VI0_FIELD,
	FN_I2C3_SDA, FN_SCIFA5_TXD_C, FN_IECLK_C, FN_AVB_RX_ER, FN_VI0_HSYNC_N,
	FN_SCIF0_RXD_B, FN_I2C0_SCL_C, FN_IERX_C, FN_AVB_COL, FN_VI0_VSYNC_N,
	FN_SCIF0_TXD_B, FN_I2C0_SDA_C, FN_AUDIO_CLKOUT_B, FN_AVB_TX_EN,
	FN_ETH_MDIO, FN_VI0_G0, FN_MSIOF2_RXD_B, FN_IIC0_SCL_D, FN_AVB_TX_CLK,
	FN_ADIDATA, FN_AD_DI,

	/* IPSR7 */
	FN_ETH_CRS_DV, FN_VI0_G1, FN_MSIOF2_TXD_B, FN_IIC0_SDA_D, FN_AVB_TXD0,
	FN_ADICS_SAMP, FN_AD_DO, FN_ETH_RX_ER, FN_VI0_G2, FN_MSIOF2_SCK_B,
	FN_CAN0_RX_B, FN_AVB_TXD1, FN_ADICLK, FN_AD_CLK, FN_ETH_RXD0, FN_VI0_G3,
	FN_MSIOF2_SYNC_B, FN_CAN0_TX_B, FN_AVB_TXD2, FN_ADICHS0, FN_AD_NCS_N,
	FN_ETH_RXD1, FN_VI0_G4, FN_MSIOF2_SS1_B, FN_SCIF4_RXD_D, FN_AVB_TXD3,
	FN_ADICHS1, FN_ETH_LINK, FN_VI0_G5, FN_MSIOF2_SS2_B, FN_SCIF4_TXD_D,
	FN_AVB_TXD4, FN_ADICHS2, FN_ETH_REFCLK, FN_VI0_G6, FN_SCIF2_SCK_C,
	FN_AVB_TXD5, FN_SSI_SCK5_B, FN_ETH_TXD1, FN_VI0_G7, FN_SCIF2_RXD_C,
	FN_IIC1_SCL_D, FN_AVB_TXD6, FN_SSI_WS5_B, FN_ETH_TX_EN, FN_VI0_R0,
	FN_SCIF2_TXD_C, FN_IIC1_SDA_D, FN_AVB_TXD7, FN_SSI_SDATA5_B,
	FN_ETH_MAGIC, FN_VI0_R1, FN_SCIF3_SCK_B, FN_AVB_TX_ER, FN_SSI_SCK6_B,
	FN_ETH_TXD0, FN_VI0_R2, FN_SCIF3_RXD_B, FN_I2C4_SCL_E, FN_AVB_GTX_CLK,
	FN_SSI_WS6_B, FN_DREQ0_N, FN_SCIFB1_RXD,

	/* IPSR8 */
	FN_ETH_MDC, FN_VI0_R3, FN_SCIF3_TXD_B, FN_I2C4_SDA_E, FN_AVB_MDC,
	FN_SSI_SDATA6_B, FN_HSCIF0_HRX, FN_VI0_R4, FN_I2C1_SCL_C,
	FN_AUDIO_CLKA_B, FN_AVB_MDIO, FN_SSI_SCK78_B, FN_HSCIF0_HTX,
	FN_VI0_R5, FN_I2C1_SDA_C, FN_AUDIO_CLKB_B, FN_AVB_LINK, FN_SSI_WS78_B,
	FN_HSCIF0_HCTS_N, FN_VI0_R6, FN_SCIF0_RXD_D, FN_I2C0_SCL_E,
	FN_AVB_MAGIC, FN_SSI_SDATA7_B, FN_HSCIF0_HRTS_N, FN_VI0_R7,
	FN_SCIF0_TXD_D, FN_I2C0_SDA_E, FN_AVB_PHY_INT, FN_SSI_SDATA8_B,
	FN_HSCIF0_HSCK, FN_SCIF_CLK_B, FN_AVB_CRS, FN_AUDIO_CLKC_B,
	FN_I2C0_SCL, FN_SCIF0_RXD_C, FN_PWM5, FN_TCLK1_B, FN_AVB_GTXREFCLK,
	FN_CAN1_RX_D, FN_TPUTO0_B, FN_I2C0_SDA, FN_SCIF0_TXD_C, FN_TPUTO0,
	FN_CAN_CLK, FN_DVC_MUTE, FN_CAN1_TX_D, FN_I2C1_SCL, FN_SCIF4_RXD,
	FN_PWM5_B, FN_DU1_DR0, FN_RIF1_SYNC_B, FN_TS_SDATA_D, FN_TPUTO1_B,
	FN_I2C1_SDA, FN_SCIF4_TXD, FN_IRQ5, FN_DU1_DR1, FN_RIF1_CLK_B,
	FN_TS_SCK_D, FN_BPFCLK_C, FN_MSIOF0_RXD, FN_SCIF5_RXD, FN_I2C2_SCL_C,
	FN_DU1_DR2, FN_RIF1_D0_B, FN_TS_SDEN_D, FN_FMCLK_C, FN_RDS_CLK,

	/*
	 * From IPSR9 to IPSR10 have been removed because they does not use.
	 */

	/* IPSR11 */
	FN_SSI_WS5, FN_SCIFA3_RXD, FN_I2C3_SCL_C, FN_DU1_DOTCLKOUT0,
	FN_CAN_DEBUGOUT11, FN_SSI_SDATA5, FN_SCIFA3_TXD, FN_I2C3_SDA_C,
	FN_DU1_DOTCLKOUT1, FN_CAN_DEBUGOUT12, FN_SSI_SCK6, FN_SCIFA1_SCK_B,
	FN_DU1_EXHSYNC_DU1_HSYNC, FN_CAN_DEBUGOUT13, FN_SSI_WS6,
	FN_SCIFA1_RXD_B, FN_I2C4_SCL_C, FN_DU1_EXVSYNC_DU1_VSYNC,
	FN_CAN_DEBUGOUT14, FN_SSI_SDATA6, FN_SCIFA1_TXD_B, FN_I2C4_SDA_C,
	FN_DU1_EXODDF_DU1_ODDF_DISP_CDE, FN_CAN_DEBUGOUT15, FN_SSI_SCK78,
	FN_SCIFA2_SCK_B, FN_IIC0_SDA_C, FN_DU1_DISP, FN_SSI_WS78,
	FN_SCIFA2_RXD_B, FN_IIC0_SCL_C, FN_DU1_CDE, FN_SSI_SDATA7,
	FN_SCIFA2_TXD_B, FN_IRQ8, FN_AUDIO_CLKA_D, FN_CAN_CLK_D, FN_PCMOE_N,
	FN_SSI_SCK0129, FN_MSIOF1_RXD_B, FN_SCIF5_RXD_D, FN_ADIDATA_B,
	FN_AD_DI_B, FN_PCMWE_N, FN_SSI_WS0129, FN_MSIOF1_TXD_B, FN_SCIF5_TXD_D,
	FN_ADICS_SAMP_B, FN_AD_DO_B, FN_SSI_SDATA0, FN_MSIOF1_SCK_B, FN_PWM0_B,
	FN_ADICLK_B, FN_AD_CLK_B,

	/*
	 * From IPSR12 to IPSR13 have been removed because they does not use.
	 */

	/* MOD_SEL */
	FN_SEL_ADG_0, FN_SEL_ADG_1, FN_SEL_ADG_2, FN_SEL_ADG_3,
	FN_SEL_ADI_0, FN_SEL_ADI_1, FN_SEL_CAN_0, FN_SEL_CAN_1,
	FN_SEL_CAN_2, FN_SEL_CAN_3, FN_SEL_DARC_0, FN_SEL_DARC_1,
	FN_SEL_DARC_2, FN_SEL_DARC_3, FN_SEL_DARC_4, FN_SEL_DR0_0,
	FN_SEL_DR0_1, FN_SEL_DR1_0, FN_SEL_DR1_1, FN_SEL_DR2_0, FN_SEL_DR2_1,
	FN_SEL_DR3_0, FN_SEL_DR3_1, FN_SEL_ETH_0, FN_SEL_ETH_1, FN_SEL_FSN_0,
	FN_SEL_FSN_1, FN_SEL_I2C00_0, FN_SEL_I2C00_1, FN_SEL_I2C00_2,
	FN_SEL_I2C00_3, FN_SEL_I2C00_4, FN_SEL_I2C01_0, FN_SEL_I2C01_1,
	FN_SEL_I2C01_2, FN_SEL_I2C01_3, FN_SEL_I2C01_4, FN_SEL_I2C02_0,
	FN_SEL_I2C02_1, FN_SEL_I2C02_2, FN_SEL_I2C02_3, FN_SEL_I2C02_4,
	FN_SEL_I2C03_0, FN_SEL_I2C03_1, FN_SEL_I2C03_2, FN_SEL_I2C03_3,
	FN_SEL_I2C03_4, FN_SEL_I2C04_0, FN_SEL_I2C04_1, FN_SEL_I2C04_2,
	FN_SEL_I2C04_3, FN_SEL_I2C04_4, FN_SEL_IIC00_0, FN_SEL_IIC00_1,
	FN_SEL_IIC00_2, FN_SEL_IIC00_3, FN_SEL_AVB_0, FN_SEL_AVB_1,

	/* MOD_SEL2 */
	FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2, FN_SEL_IIC01_0,
	FN_SEL_IIC01_1, FN_SEL_IIC01_2, FN_SEL_IIC01_3, FN_SEL_LBS_0,
	FN_SEL_LBS_1, FN_SEL_MSI1_0, FN_SEL_MSI1_1, FN_SEL_MSI2_0,
	FN_SEL_MSI2_1, FN_SEL_RAD_0, FN_SEL_RAD_1, FN_SEL_RCN_0,
	FN_SEL_RCN_1, FN_SEL_RSP_0, FN_SEL_RSP_1, FN_SEL_SCIFA0_0,
	FN_SEL_SCIFA0_1, FN_SEL_SCIFA0_2, FN_SEL_SCIFA0_3, FN_SEL_SCIFA1_0,
	FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2, FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1,
	FN_SEL_SCIFA3_0, FN_SEL_SCIFA3_1, FN_SEL_SCIFA4_0, FN_SEL_SCIFA4_1,
	FN_SEL_SCIFA4_2, FN_SEL_SCIFA4_3, FN_SEL_SCIFA5_0, FN_SEL_SCIFA5_1,
	FN_SEL_SCIFA5_2, FN_SEL_SCIFA5_3, FN_SEL_SPDM_0, FN_SEL_SPDM_1,
	FN_SEL_TMU_0, FN_SEL_TMU_1, FN_SEL_TSIF0_0, FN_SEL_TSIF0_1,
	FN_SEL_TSIF0_2, FN_SEL_TSIF0_3, FN_SEL_CAN0_0, FN_SEL_CAN0_1,
	FN_SEL_CAN0_2, FN_SEL_CAN0_3, FN_SEL_CAN1_0, FN_SEL_CAN1_1,
	FN_SEL_CAN1_2, FN_SEL_CAN1_3, FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1,
	FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1, FN_SEL_RDS_0, FN_SEL_RDS_1,
	FN_SEL_RDS_2, FN_SEL_RDS_3,

	/* MOD_SEL3 */
	FN_SEL_SCIF0_0, FN_SEL_SCIF0_1, FN_SEL_SCIF0_2, FN_SEL_SCIF0_3,
	FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, FN_SEL_SCIF2_0,
	FN_SEL_SCIF2_1, FN_SEL_SCIF2_2, FN_SEL_SCIF3_0, FN_SEL_SCIF3_1,
	FN_SEL_SCIF4_0, FN_SEL_SCIF4_1, FN_SEL_SCIF4_2, FN_SEL_SCIF4_3,
	FN_SEL_SCIF4_4, FN_SEL_SCIF5_0, FN_SEL_SCIF5_1, FN_SEL_SCIF5_2,
	FN_SEL_SCIF5_3, FN_SEL_SSI1_0, FN_SEL_SSI1_1, FN_SEL_SSI2_0,
	FN_SEL_SSI2_1, FN_SEL_SSI4_0, FN_SEL_SSI4_1, FN_SEL_SSI5_0,
	FN_SEL_SSI5_1, FN_SEL_SSI6_0, FN_SEL_SSI6_1, FN_SEL_SSI7_0,
	FN_SEL_SSI7_1, FN_SEL_SSI8_0, FN_SEL_SSI8_1, FN_SEL_SSI9_0,
	FN_SEL_SSI9_1,
	PINMUX_FUNCTION_END,

	PINMUX_MARK_BEGIN,
	A2_MARK, WE0_N_MARK, WE1_N_MARK, DACK0_MARK,

	USB0_PWEN_MARK, USB0_OVC_MARK, USB1_PWEN_MARK, USB1_OVC_MARK,

	SD0_CLK_MARK, SD0_CMD_MARK, SD0_DATA0_MARK, SD0_DATA1_MARK,
	SD0_DATA2_MARK, SD0_DATA3_MARK, SD0_CD_MARK, SD0_WP_MARK,

	SD1_CLK_MARK, SD1_CMD_MARK, SD1_DATA0_MARK, SD1_DATA1_MARK,
	SD1_DATA2_MARK, SD1_DATA3_MARK,

	/* IPSR0 */
	SD1_CD_MARK, CAN0_RX_MARK, SD1_WP_MARK, IRQ7_MARK, CAN0_TX_MARK,
	MMC_CLK_MARK, SD2_CLK_MARK, MMC_CMD_MARK, SD2_CMD_MARK, MMC_D0_MARK,
	SD2_DATA0_MARK, MMC_D1_MARK, SD2_DATA1_MARK, MMC_D2_MARK,
	SD2_DATA2_MARK, MMC_D3_MARK, SD2_DATA3_MARK, MMC_D4_MARK, SD2_CD_MARK,
	MMC_D5_MARK, SD2_WP_MARK, MMC_D6_MARK, SCIF0_RXD_MARK, I2C2_SCL_B_MARK,
	CAN1_RX_MARK, MMC_D7_MARK, SCIF0_TXD_MARK, I2C2_SDA_B_MARK,
	CAN1_TX_MARK, D0_MARK, SCIFA3_SCK_B_MARK, IRQ4_MARK, D1_MARK,
	SCIFA3_RXD_B_MARK, D2_MARK, SCIFA3_TXD_B_MARK, D3_MARK, I2C3_SCL_B_MARK,
	SCIF5_RXD_B_MARK, D4_MARK, I2C3_SDA_B_MARK, SCIF5_TXD_B_MARK, D5_MARK,
	SCIF4_RXD_B_MARK, I2C0_SCL_D_MARK,

	/*
	 * From IPSR1 to IPSR5 have been removed because they does not use.
	 */

	/* IPSR6 */
	DU0_EXVSYNC_DU0_VSYNC_MARK, QSTB_QHE_MARK, CC50_STATE28_MARK,
	DU0_EXODDF_DU0_ODDF_DISP_CDE_MARK, QCPV_QDE_MARK, CC50_STATE29_MARK,
	DU0_DISP_MARK, QPOLA_MARK, CC50_STATE30_MARK, DU0_CDE_MARK, QPOLB_MARK,
	CC50_STATE31_MARK, VI0_CLK_MARK, AVB_RX_CLK_MARK, VI0_DATA0_VI0_B0_MARK,
	AVB_RX_DV_MARK, VI0_DATA1_VI0_B1_MARK, AVB_RXD0_MARK,
	VI0_DATA2_VI0_B2_MARK, AVB_RXD1_MARK, VI0_DATA3_VI0_B3_MARK,
	AVB_RXD2_MARK, VI0_DATA4_VI0_B4_MARK, AVB_RXD3_MARK,
	VI0_DATA5_VI0_B5_MARK, AVB_RXD4_MARK, VI0_DATA6_VI0_B6_MARK,
	AVB_RXD5_MARK, VI0_DATA7_VI0_B7_MARK, AVB_RXD6_MARK, VI0_CLKENB_MARK,
	I2C3_SCL_MARK, SCIFA5_RXD_C_MARK, IETX_C_MARK, AVB_RXD7_MARK,
	VI0_FIELD_MARK, I2C3_SDA_MARK, SCIFA5_TXD_C_MARK, IECLK_C_MARK,
	AVB_RX_ER_MARK, VI0_HSYNC_N_MARK, SCIF0_RXD_B_MARK, I2C0_SCL_C_MARK,
	IERX_C_MARK, AVB_COL_MARK, VI0_VSYNC_N_MARK, SCIF0_TXD_B_MARK,
	I2C0_SDA_C_MARK, AUDIO_CLKOUT_B_MARK, AVB_TX_EN_MARK, ETH_MDIO_MARK,
	VI0_G0_MARK, MSIOF2_RXD_B_MARK, IIC0_SCL_D_MARK, AVB_TX_CLK_MARK,
	ADIDATA_MARK, AD_DI_MARK,

	/* IPSR7 */
	ETH_CRS_DV_MARK, VI0_G1_MARK, MSIOF2_TXD_B_MARK, IIC0_SDA_D_MARK,
	AVB_TXD0_MARK, ADICS_SAMP_MARK, AD_DO_MARK, ETH_RX_ER_MARK, VI0_G2_MARK,
	MSIOF2_SCK_B_MARK, CAN0_RX_B_MARK, AVB_TXD1_MARK, ADICLK_MARK,
	AD_CLK_MARK, ETH_RXD0_MARK, VI0_G3_MARK, MSIOF2_SYNC_B_MARK,
	CAN0_TX_B_MARK, AVB_TXD2_MARK, ADICHS0_MARK, AD_NCS_N_MARK,
	ETH_RXD1_MARK, VI0_G4_MARK, MSIOF2_SS1_B_MARK, SCIF4_RXD_D_MARK,
	AVB_TXD3_MARK, ADICHS1_MARK, ETH_LINK_MARK, VI0_G5_MARK,
	MSIOF2_SS2_B_MARK, SCIF4_TXD_D_MARK, AVB_TXD4_MARK, ADICHS2_MARK,
	ETH_REFCLK_MARK, VI0_G6_MARK, SCIF2_SCK_C_MARK, AVB_TXD5_MARK,
	SSI_SCK5_B_MARK, ETH_TXD1_MARK, VI0_G7_MARK, SCIF2_RXD_C_MARK,
	IIC1_SCL_D_MARK, AVB_TXD6_MARK, SSI_WS5_B_MARK, ETH_TX_EN_MARK,
	VI0_R0_MARK, SCIF2_TXD_C_MARK, IIC1_SDA_D_MARK, AVB_TXD7_MARK,
	SSI_SDATA5_B_MARK, ETH_MAGIC_MARK, VI0_R1_MARK, SCIF3_SCK_B_MARK,
	AVB_TX_ER_MARK, SSI_SCK6_B_MARK, ETH_TXD0_MARK, VI0_R2_MARK,
	SCIF3_RXD_B_MARK, I2C4_SCL_E_MARK, AVB_GTX_CLK_MARK, SSI_WS6_B_MARK,
	DREQ0_N_MARK, SCIFB1_RXD_MARK,

	/* IPSR8 */
	ETH_MDC_MARK, VI0_R3_MARK, SCIF3_TXD_B_MARK, I2C4_SDA_E_MARK,
	AVB_MDC_MARK, SSI_SDATA6_B_MARK, HSCIF0_HRX_MARK, VI0_R4_MARK,
	I2C1_SCL_C_MARK, AUDIO_CLKA_B_MARK, AVB_MDIO_MARK, SSI_SCK78_B_MARK,
	HSCIF0_HTX_MARK, VI0_R5_MARK, I2C1_SDA_C_MARK, AUDIO_CLKB_B_MARK,
	AVB_LINK_MARK, SSI_WS78_B_MARK, HSCIF0_HCTS_N_MARK, VI0_R6_MARK,
	SCIF0_RXD_D_MARK, I2C0_SCL_E_MARK, AVB_MAGIC_MARK, SSI_SDATA7_B_MARK,
	HSCIF0_HRTS_N_MARK, VI0_R7_MARK, SCIF0_TXD_D_MARK, I2C0_SDA_E_MARK,
	AVB_PHY_INT_MARK, SSI_SDATA8_B_MARK,
	HSCIF0_HSCK_MARK, SCIF_CLK_B_MARK, AVB_CRS_MARK, AUDIO_CLKC_B_MARK,
	I2C0_SCL_MARK, SCIF0_RXD_C_MARK, PWM5_MARK, TCLK1_B_MARK,
	AVB_GTXREFCLK_MARK, CAN1_RX_D_MARK, TPUTO0_B_MARK, I2C0_SDA_MARK,
	SCIF0_TXD_C_MARK, TPUTO0_MARK, CAN_CLK_MARK, DVC_MUTE_MARK,
	CAN1_TX_D_MARK, I2C1_SCL_MARK, SCIF4_RXD_MARK, PWM5_B_MARK,
	DU1_DR0_MARK, RIF1_SYNC_B_MARK, TS_SDATA_D_MARK, TPUTO1_B_MARK,
	I2C1_SDA_MARK, SCIF4_TXD_MARK, IRQ5_MARK, DU1_DR1_MARK, RIF1_CLK_B_MARK,
	TS_SCK_D_MARK, BPFCLK_C_MARK, MSIOF0_RXD_MARK, SCIF5_RXD_MARK,
	I2C2_SCL_C_MARK, DU1_DR2_MARK, RIF1_D0_B_MARK, TS_SDEN_D_MARK,
	FMCLK_C_MARK, RDS_CLK_MARK,

	/*
	 * From IPSR9 to IPSR10 have been removed because they does not use.
	 */

	/* IPSR11 */
	SSI_WS5_MARK, SCIFA3_RXD_MARK, I2C3_SCL_C_MARK, DU1_DOTCLKOUT0_MARK,
	CAN_DEBUGOUT11_MARK, SSI_SDATA5_MARK, SCIFA3_TXD_MARK, I2C3_SDA_C_MARK,
	DU1_DOTCLKOUT1_MARK, CAN_DEBUGOUT12_MARK, SSI_SCK6_MARK,
	SCIFA1_SCK_B_MARK, DU1_EXHSYNC_DU1_HSYNC_MARK, CAN_DEBUGOUT13_MARK,
	SSI_WS6_MARK, SCIFA1_RXD_B_MARK, I2C4_SCL_C_MARK,
	DU1_EXVSYNC_DU1_VSYNC_MARK, CAN_DEBUGOUT14_MARK, SSI_SDATA6_MARK,
	SCIFA1_TXD_B_MARK, I2C4_SDA_C_MARK, DU1_EXODDF_DU1_ODDF_DISP_CDE_MARK,
	CAN_DEBUGOUT15_MARK, SSI_SCK78_MARK, SCIFA2_SCK_B_MARK, IIC0_SDA_C_MARK,
	DU1_DISP_MARK, SSI_WS78_MARK, SCIFA2_RXD_B_MARK, IIC0_SCL_C_MARK,
	DU1_CDE_MARK, SSI_SDATA7_MARK, SCIFA2_TXD_B_MARK, IRQ8_MARK,
	AUDIO_CLKA_D_MARK, CAN_CLK_D_MARK, PCMOE_N_MARK, SSI_SCK0129_MARK,
	MSIOF1_RXD_B_MARK, SCIF5_RXD_D_MARK, ADIDATA_B_MARK, AD_DI_B_MARK,
	PCMWE_N_MARK, SSI_WS0129_MARK, MSIOF1_TXD_B_MARK, SCIF5_TXD_D_MARK,
	ADICS_SAMP_B_MARK, AD_DO_B_MARK, SSI_SDATA0_MARK, MSIOF1_SCK_B_MARK,
	PWM0_B_MARK, ADICLK_B_MARK, AD_CLK_B_MARK,

	/*
	 * From IPSR12 to IPSR13 have been removed because they does not use.
	 */

	PINMUX_MARK_END,
};

static pinmux_enum_t pinmux_data[] = {
	PINMUX_DATA_GP_ALL(), /* PINMUX_DATA(GP_M_N_DATA, GP_M_N_FN...), */

	PINMUX_DATA(A2_MARK, FN_A2),
	PINMUX_DATA(WE0_N_MARK, FN_WE0_N),
	PINMUX_DATA(WE1_N_MARK, FN_WE1_N),
	PINMUX_DATA(DACK0_MARK, FN_DACK0),
	PINMUX_DATA(USB0_PWEN_MARK, FN_USB0_PWEN),
	PINMUX_DATA(USB0_OVC_MARK, FN_USB0_OVC),
	PINMUX_DATA(USB1_PWEN_MARK, FN_USB1_PWEN),
	PINMUX_DATA(USB1_OVC_MARK, FN_USB1_OVC),
	PINMUX_DATA(SD0_CLK_MARK, FN_SD0_CLK),
	PINMUX_DATA(SD0_CMD_MARK, FN_SD0_CMD),
	PINMUX_DATA(SD0_DATA0_MARK, FN_SD0_DATA0),
	PINMUX_DATA(SD0_DATA1_MARK, FN_SD0_DATA1),
	PINMUX_DATA(SD0_DATA2_MARK, FN_SD0_DATA2),
	PINMUX_DATA(SD0_DATA3_MARK, FN_SD0_DATA3),
	PINMUX_DATA(SD0_CD_MARK, FN_SD0_CD),
	PINMUX_DATA(SD0_WP_MARK, FN_SD0_WP),
	PINMUX_DATA(SD1_CLK_MARK, FN_SD1_CLK),
	PINMUX_DATA(SD1_CMD_MARK, FN_SD1_CMD),
	PINMUX_DATA(SD1_DATA0_MARK, FN_SD1_DATA0),
	PINMUX_DATA(SD1_DATA1_MARK, FN_SD1_DATA1),
	PINMUX_DATA(SD1_DATA2_MARK, FN_SD1_DATA2),
	PINMUX_DATA(SD1_DATA3_MARK, FN_SD1_DATA3),

	/* IPSR0 */
	PINMUX_IPSR_DATA(IP0_0, SD1_CD),
	PINMUX_IPSR_MODSEL_DATA(IP0_0, CAN0_RX, SEL_CAN0_0),
	PINMUX_IPSR_DATA(IP0_9_8, SD1_WP),
	PINMUX_IPSR_DATA(IP0_9_8, IRQ7),
	PINMUX_IPSR_MODSEL_DATA(IP0_9_8, CAN0_TX, SEL_CAN0_0),
	PINMUX_IPSR_DATA(IP0_10, MMC_CLK),
	PINMUX_IPSR_DATA(IP0_10, SD2_CLK),
	PINMUX_IPSR_DATA(IP0_11, MMC_CMD),
	PINMUX_IPSR_DATA(IP0_11, SD2_CMD),
	PINMUX_IPSR_DATA(IP0_12, MMC_D0),
	PINMUX_IPSR_DATA(IP0_12, SD2_DATA0),
	PINMUX_IPSR_DATA(IP0_13, MMC_D1),
	PINMUX_IPSR_DATA(IP0_13, SD2_DATA1),
	PINMUX_IPSR_DATA(IP0_14, MMC_D2),
	PINMUX_IPSR_DATA(IP0_14, SD2_DATA2),
	PINMUX_IPSR_DATA(IP0_15, MMC_D3),
	PINMUX_IPSR_DATA(IP0_15, SD2_DATA3),
	PINMUX_IPSR_DATA(IP0_16, MMC_D4),
	PINMUX_IPSR_DATA(IP0_16, SD2_CD),
	PINMUX_IPSR_DATA(IP0_17, MMC_D5),
	PINMUX_IPSR_DATA(IP0_17, SD2_WP),
	PINMUX_IPSR_DATA(IP0_19_18, MMC_D6),
	PINMUX_IPSR_MODSEL_DATA(IP0_19_18, SCIF0_RXD, SEL_SCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP0_19_18, I2C2_SCL_B, SEL_I2C02_1),
	PINMUX_IPSR_MODSEL_DATA(IP0_19_18, CAN1_RX, SEL_CAN1_0),
	PINMUX_IPSR_DATA(IP0_21_20, MMC_D7),
	PINMUX_IPSR_MODSEL_DATA(IP0_21_20, SCIF0_TXD, SEL_SCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP0_21_20, I2C2_SDA_B, SEL_I2C02_1),
	PINMUX_IPSR_MODSEL_DATA(IP0_21_20, CAN1_TX, SEL_CAN1_0),
	PINMUX_IPSR_DATA(IP0_23_22, D0),
	PINMUX_IPSR_MODSEL_DATA(IP0_23_22, SCIFA3_SCK_B, SEL_SCIFA3_1),
	PINMUX_IPSR_DATA(IP0_23_22, IRQ4),
	PINMUX_IPSR_DATA(IP0_24, D1),
	PINMUX_IPSR_MODSEL_DATA(IP0_24, SCIFA3_RXD_B, SEL_SCIFA3_1),
	PINMUX_IPSR_DATA(IP0_25, D2),
	PINMUX_IPSR_MODSEL_DATA(IP0_25, SCIFA3_TXD_B, SEL_SCIFA3_1),
	PINMUX_IPSR_DATA(IP0_27_26, D3),
	PINMUX_IPSR_MODSEL_DATA(IP0_27_26, I2C3_SCL_B, SEL_I2C03_1),
	PINMUX_IPSR_MODSEL_DATA(IP0_27_26, SCIF5_RXD_B, SEL_SCIF5_1),
	PINMUX_IPSR_DATA(IP0_29_28, D4),
	PINMUX_IPSR_MODSEL_DATA(IP0_29_28, I2C3_SDA_B, SEL_I2C03_1),
	PINMUX_IPSR_MODSEL_DATA(IP0_29_28, SCIF5_TXD_B, SEL_SCIF5_1),
	PINMUX_IPSR_DATA(IP0_31_30, D5),
	PINMUX_IPSR_MODSEL_DATA(IP0_31_30, SCIF4_RXD_B, SEL_SCIF4_1),
	PINMUX_IPSR_MODSEL_DATA(IP0_31_30, I2C0_SCL_D, SEL_I2C00_3),

	/*
	 * From IPSR1 to IPSR5 have been removed because they does not use.
	 */

	/* IPSR6 */
	PINMUX_IPSR_DATA(IP6_1_0, DU0_EXVSYNC_DU0_VSYNC),
	PINMUX_IPSR_DATA(IP6_1_0, QSTB_QHE),
	PINMUX_IPSR_DATA(IP6_1_0, CC50_STATE28),
	PINMUX_IPSR_DATA(IP6_3_2, DU0_EXODDF_DU0_ODDF_DISP_CDE),
	PINMUX_IPSR_DATA(IP6_3_2, QCPV_QDE),
	PINMUX_IPSR_DATA(IP6_3_2, CC50_STATE29),
	PINMUX_IPSR_DATA(IP6_5_4, DU0_DISP),
	PINMUX_IPSR_DATA(IP6_5_4, QPOLA),
	PINMUX_IPSR_DATA(IP6_5_4, CC50_STATE30),
	PINMUX_IPSR_DATA(IP6_7_6, DU0_CDE),
	PINMUX_IPSR_DATA(IP6_7_6, QPOLB),
	PINMUX_IPSR_DATA(IP6_7_6, CC50_STATE31),
	PINMUX_IPSR_DATA(IP6_8, VI0_CLK),
	PINMUX_IPSR_DATA(IP6_8, AVB_RX_CLK),
	PINMUX_IPSR_DATA(IP6_9, VI0_DATA0_VI0_B0),
	PINMUX_IPSR_DATA(IP6_9, AVB_RX_DV),
	PINMUX_IPSR_DATA(IP6_10, VI0_DATA1_VI0_B1),
	PINMUX_IPSR_DATA(IP6_10, AVB_RXD0),
	PINMUX_IPSR_DATA(IP6_11, VI0_DATA2_VI0_B2),
	PINMUX_IPSR_DATA(IP6_11, AVB_RXD1),
	PINMUX_IPSR_DATA(IP6_12, VI0_DATA3_VI0_B3),
	PINMUX_IPSR_DATA(IP6_12, AVB_RXD2),
	PINMUX_IPSR_DATA(IP6_13, VI0_DATA4_VI0_B4),
	PINMUX_IPSR_DATA(IP6_13, AVB_RXD3),
	PINMUX_IPSR_DATA(IP6_14, VI0_DATA5_VI0_B5),
	PINMUX_IPSR_DATA(IP6_14, AVB_RXD4),
	PINMUX_IPSR_DATA(IP6_15, VI0_DATA6_VI0_B6),
	PINMUX_IPSR_DATA(IP6_15, AVB_RXD5),
	PINMUX_IPSR_DATA(IP6_16, VI0_DATA7_VI0_B7),
	PINMUX_IPSR_DATA(IP6_16, AVB_RXD6),
	PINMUX_IPSR_DATA(IP6_19_17, VI0_CLKENB),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, I2C3_SCL, SEL_I2C03_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, SCIFA5_RXD_C, SEL_SCIFA5_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_19_17, IETX_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP6_19_17, AVB_RXD7),
	PINMUX_IPSR_DATA(IP6_22_20, VI0_FIELD),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, I2C3_SDA, SEL_I2C03_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, SCIFA5_TXD_C, SEL_SCIFA5_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_22_20, IECLK_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP6_22_20, AVB_RX_ER),
	PINMUX_IPSR_DATA(IP6_25_23, VI0_HSYNC_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, SCIF0_RXD_B, SEL_SCIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, I2C0_SCL_C, SEL_I2C00_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_25_23, IERX_C, SEL_IEB_2),
	PINMUX_IPSR_DATA(IP6_25_23, AVB_COL),
	PINMUX_IPSR_DATA(IP6_28_26, VI0_VSYNC_N),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, SCIF0_TXD_B, SEL_SCIF0_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, I2C0_SDA_C, SEL_I2C00_2),
	PINMUX_IPSR_MODSEL_DATA(IP6_28_26, AUDIO_CLKOUT_B, SEL_ADG_1),
	PINMUX_IPSR_DATA(IP6_28_26, AVB_TX_EN),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, ETH_MDIO, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP6_31_29, VI0_G0),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, MSIOF2_RXD_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, IIC0_SCL_D, SEL_IIC00_3),
	PINMUX_IPSR_DATA(IP6_31_29, AVB_TX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, ADIDATA, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP6_31_29, AD_DI, SEL_ADI_0),

	/* IPSR7 */
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, ETH_CRS_DV, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_2_0, VI0_G1),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, MSIOF2_TXD_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, IIC0_SDA_D, SEL_IIC00_3),
	PINMUX_IPSR_DATA(IP7_2_0, AVB_TXD0),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, ADICS_SAMP, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_2_0, AD_DO, SEL_ADI_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, ETH_RX_ER, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_5_3, VI0_G2),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, MSIOF2_SCK_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, CAN0_RX_B, SEL_CAN0_1),
	PINMUX_IPSR_DATA(IP7_5_3, AVB_TXD1),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, ADICLK, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_5_3, AD_CLK, SEL_ADI_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_8_6, ETH_RXD0, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_8_6, VI0_G3),
	PINMUX_IPSR_MODSEL_DATA(IP7_8_6, MSIOF2_SYNC_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_8_6, CAN0_TX_B, SEL_CAN0_1),
	PINMUX_IPSR_DATA(IP7_8_6, AVB_TXD2),
	PINMUX_IPSR_MODSEL_DATA(IP7_8_6, ADICHS0, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_8_6, AD_NCS_N, SEL_ADI_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_11_9, ETH_RXD1, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_11_9, VI0_G4),
	PINMUX_IPSR_MODSEL_DATA(IP7_11_9, MSIOF2_SS1_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_11_9, SCIF4_RXD_D, SEL_SCIF4_3),
	PINMUX_IPSR_DATA(IP7_11_9, AVB_TXD3),
	PINMUX_IPSR_MODSEL_DATA(IP7_11_9, ADICHS1, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_14_12, ETH_LINK, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_14_12, VI0_G5),
	PINMUX_IPSR_MODSEL_DATA(IP7_14_12, MSIOF2_SS2_B, SEL_MSI2_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_14_12, SCIF4_TXD_D, SEL_SCIF4_3),
	PINMUX_IPSR_DATA(IP7_14_12, AVB_TXD4),
	PINMUX_IPSR_MODSEL_DATA(IP7_14_12, ADICHS2, SEL_RAD_0),
	PINMUX_IPSR_MODSEL_DATA(IP7_17_15, ETH_REFCLK, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_17_15, VI0_G6),
	PINMUX_IPSR_MODSEL_DATA(IP7_17_15, SCIF2_SCK_C, SEL_SCIF2_2),
	PINMUX_IPSR_DATA(IP7_17_15, AVB_TXD5),
	PINMUX_IPSR_MODSEL_DATA(IP7_17_15, SSI_SCK5_B, SEL_SSI5_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_20_18, ETH_TXD1, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_20_18, VI0_G7),
	PINMUX_IPSR_MODSEL_DATA(IP7_20_18, SCIF2_RXD_C, SEL_SCIF2_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_20_18, IIC1_SCL_D, SEL_IIC01_3),
	PINMUX_IPSR_DATA(IP7_20_18, AVB_TXD6),
	PINMUX_IPSR_MODSEL_DATA(IP7_20_18, SSI_WS5_B, SEL_SSI5_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_23_21, ETH_TX_EN, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_23_21, VI0_R0),
	PINMUX_IPSR_MODSEL_DATA(IP7_23_21, SCIF2_TXD_C, SEL_SCIF2_2),
	PINMUX_IPSR_MODSEL_DATA(IP7_23_21, IIC1_SDA_D, SEL_IIC01_3),
	PINMUX_IPSR_DATA(IP7_23_21, AVB_TXD7),
	PINMUX_IPSR_MODSEL_DATA(IP7_23_21, SSI_SDATA5_B, SEL_SSI5_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_26_24, ETH_MAGIC, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_26_24, VI0_R1),
	PINMUX_IPSR_MODSEL_DATA(IP7_26_24, SCIF3_SCK_B, SEL_SCIF3_1),
	PINMUX_IPSR_DATA(IP7_26_24, AVB_TX_ER),
	PINMUX_IPSR_MODSEL_DATA(IP7_26_24, SSI_SCK6_B, SEL_SSI6_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_29_27, ETH_TXD0, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP7_29_27, VI0_R2),
	PINMUX_IPSR_MODSEL_DATA(IP7_29_27, SCIF3_RXD_B, SEL_SCIF3_1),
	PINMUX_IPSR_MODSEL_DATA(IP7_29_27, I2C4_SCL_E, SEL_I2C04_4),
	PINMUX_IPSR_DATA(IP7_29_27, AVB_GTX_CLK),
	PINMUX_IPSR_MODSEL_DATA(IP7_29_27, SSI_WS6_B, SEL_SSI6_1),
	PINMUX_IPSR_DATA(IP7_31, DREQ0_N),
	PINMUX_IPSR_DATA(IP7_31, SCIFB1_RXD),

	/* IPSR8 */
	PINMUX_IPSR_MODSEL_DATA(IP8_2_0, ETH_MDC, SEL_ETH_0),
	PINMUX_IPSR_DATA(IP8_2_0, VI0_R3),
	PINMUX_IPSR_MODSEL_DATA(IP8_2_0, SCIF3_TXD_B, SEL_SCIF3_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_2_0, I2C4_SDA_E, SEL_I2C04_4),
	PINMUX_IPSR_DATA(IP8_2_0, AVB_MDC),
	PINMUX_IPSR_MODSEL_DATA(IP8_2_0, SSI_SDATA6_B, SEL_SSI6_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_5_3, HSCIF0_HRX, SEL_HSCIF0_0),
	PINMUX_IPSR_DATA(IP8_5_3, VI0_R4),
	PINMUX_IPSR_MODSEL_DATA(IP8_5_3, I2C1_SCL_C, SEL_I2C01_2),
	PINMUX_IPSR_MODSEL_DATA(IP8_5_3, AUDIO_CLKA_B, SEL_ADG_1),
	PINMUX_IPSR_DATA(IP8_5_3, AVB_MDIO),
	PINMUX_IPSR_MODSEL_DATA(IP8_5_3, SSI_SCK78_B, SEL_SSI7_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_8_6, HSCIF0_HTX, SEL_HSCIF0_0),
	PINMUX_IPSR_DATA(IP8_8_6, VI0_R5),
	PINMUX_IPSR_MODSEL_DATA(IP8_8_6, I2C1_SDA_C, SEL_I2C01_2),
	PINMUX_IPSR_MODSEL_DATA(IP8_8_6, AUDIO_CLKB_B, SEL_ADG_1),
	PINMUX_IPSR_DATA(IP8_5_3, AVB_LINK),
	PINMUX_IPSR_MODSEL_DATA(IP8_8_6, SSI_WS78_B, SEL_SSI7_1),
	PINMUX_IPSR_DATA(IP8_11_9, HSCIF0_HCTS_N),
	PINMUX_IPSR_DATA(IP8_11_9, VI0_R6),
	PINMUX_IPSR_MODSEL_DATA(IP8_11_9, SCIF0_RXD_D, SEL_SCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_11_9, I2C0_SCL_E, SEL_I2C00_4),
	PINMUX_IPSR_DATA(IP8_11_9, AVB_MAGIC),
	PINMUX_IPSR_MODSEL_DATA(IP8_11_9, SSI_SDATA7_B, SEL_SSI7_1),
	PINMUX_IPSR_DATA(IP8_14_12, HSCIF0_HRTS_N),
	PINMUX_IPSR_DATA(IP8_14_12, VI0_R7),
	PINMUX_IPSR_MODSEL_DATA(IP8_14_12, SCIF0_TXD_D, SEL_SCIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_14_12, I2C0_SDA_E, SEL_I2C00_4),
	PINMUX_IPSR_DATA(IP8_14_12, AVB_PHY_INT),
	PINMUX_IPSR_MODSEL_DATA(IP8_14_12, SSI_SDATA8_B, SEL_SSI8_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_16_15, HSCIF0_HSCK, SEL_HSCIF0_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_16_15, SCIF_CLK_B, SEL_SCIF0_1),
	PINMUX_IPSR_DATA(IP8_16_15, AVB_CRS),
	PINMUX_IPSR_MODSEL_DATA(IP8_16_15, AUDIO_CLKC_B, SEL_ADG_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_17, I2C0_SCL, SEL_I2C00_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_17, SCIF0_RXD_C, SEL_SCIF0_2),
	PINMUX_IPSR_DATA(IP8_19_17, PWM5),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_17, TCLK1_B, SEL_TMU_1),
	PINMUX_IPSR_DATA(IP8_19_17, AVB_GTXREFCLK),
	PINMUX_IPSR_MODSEL_DATA(IP8_19_17, CAN1_RX_D, SEL_CAN1_3),
	PINMUX_IPSR_DATA(IP8_19_17, TPUTO0_B),
	PINMUX_IPSR_MODSEL_DATA(IP8_22_20, I2C0_SDA, SEL_I2C00_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_22_20, SCIF0_TXD_C, SEL_SCIF0_2),
	PINMUX_IPSR_DATA(IP8_22_20, TPUTO0),
	PINMUX_IPSR_MODSEL_DATA(IP8_22_20, CAN_CLK, SEL_CAN_0),
	PINMUX_IPSR_DATA(IP8_22_20, DVC_MUTE),
	PINMUX_IPSR_MODSEL_DATA(IP8_22_20, CAN1_TX_D, SEL_CAN1_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_23, I2C1_SCL, SEL_I2C01_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_23, SCIF4_RXD, SEL_SCIF4_0),
	PINMUX_IPSR_DATA(IP8_25_23, PWM5_B),
	PINMUX_IPSR_DATA(IP8_25_23, DU1_DR0),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_23, RIF1_SYNC_B, SEL_DR2_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_25_23, TS_SDATA_D, SEL_TSIF0_3),
	PINMUX_IPSR_DATA(IP8_25_23, TPUTO1_B),
	PINMUX_IPSR_MODSEL_DATA(IP8_28_26, I2C1_SDA, SEL_I2C01_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_28_26, SCIF4_TXD, SEL_SCIF4_0),
	PINMUX_IPSR_DATA(IP8_28_26, IRQ5),
	PINMUX_IPSR_DATA(IP8_28_26, DU1_DR1),
	PINMUX_IPSR_MODSEL_DATA(IP8_28_26, RIF1_CLK_B, SEL_DR2_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_28_26, TS_SCK_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_28_26, BPFCLK_C, SEL_DARC_2),
	PINMUX_IPSR_DATA(IP8_31_29, MSIOF0_RXD),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, SCIF5_RXD, SEL_SCIF5_0),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, I2C2_SCL_C, SEL_I2C02_2),
	PINMUX_IPSR_DATA(IP8_31_29, DU1_DR2),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, RIF1_D0_B, SEL_DR2_1),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, TS_SDEN_D, SEL_TSIF0_3),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, FMCLK_C, SEL_DARC_2),
	PINMUX_IPSR_MODSEL_DATA(IP8_31_29, RDS_CLK, SEL_RDS_0),

	/*
	 * From IPSR9 to IPSR10 have been removed because they does not use.
	 */

	/* IPSR11 */
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, SSI_WS5, SEL_SSI5_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, SCIFA3_RXD, SEL_SCIFA3_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_2_0, I2C3_SCL_C, SEL_I2C03_2),
	PINMUX_IPSR_DATA(IP11_2_0, DU1_DOTCLKOUT0),
	PINMUX_IPSR_DATA(IP11_2_0, CAN_DEBUGOUT11),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, SSI_SDATA5, SEL_SSI5_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, SCIFA3_TXD, SEL_SCIFA3_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_5_3, I2C3_SDA_C, SEL_I2C03_2),
	PINMUX_IPSR_DATA(IP11_5_3, DU1_DOTCLKOUT1),
	PINMUX_IPSR_DATA(IP11_5_3, CAN_DEBUGOUT12),
	PINMUX_IPSR_MODSEL_DATA(IP11_7_6, SSI_SCK6, SEL_SSI6_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_7_6, SCIFA1_SCK_B, SEL_SCIFA1_1),
	PINMUX_IPSR_DATA(IP11_7_6, DU1_EXHSYNC_DU1_HSYNC),
	PINMUX_IPSR_DATA(IP11_7_6, CAN_DEBUGOUT13),
	PINMUX_IPSR_MODSEL_DATA(IP11_10_8, SSI_WS6, SEL_SSI6_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_10_8, SCIFA1_RXD_B, SEL_SCIFA1_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_10_8, I2C4_SCL_C, SEL_I2C04_2),
	PINMUX_IPSR_DATA(IP11_10_8, DU1_EXVSYNC_DU1_VSYNC),
	PINMUX_IPSR_DATA(IP11_10_8, CAN_DEBUGOUT14),
	PINMUX_IPSR_MODSEL_DATA(IP11_13_11, SSI_SDATA6, SEL_SSI6_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_13_11, SCIFA1_TXD_B, SEL_SCIFA1_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_13_11, I2C4_SDA_C, SEL_I2C04_2),
	PINMUX_IPSR_DATA(IP11_13_11, DU1_EXODDF_DU1_ODDF_DISP_CDE),
	PINMUX_IPSR_DATA(IP11_13_11, CAN_DEBUGOUT15),
	PINMUX_IPSR_MODSEL_DATA(IP11_15_14, SSI_SCK78, SEL_SSI7_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_15_14, SCIFA2_SCK_B, SEL_SCIFA2_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_15_14, IIC0_SDA_C, SEL_IIC00_2),
	PINMUX_IPSR_DATA(IP11_15_14, DU1_DISP),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_16, SSI_WS78, SEL_SSI7_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_16, SCIFA2_RXD_B, SEL_SCIFA2_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_17_16, IIC0_SCL_C, SEL_IIC00_2),
	PINMUX_IPSR_DATA(IP11_17_16, DU1_CDE),
	PINMUX_IPSR_MODSEL_DATA(IP11_20_18, SSI_SDATA7, SEL_SSI7_0),
	PINMUX_IPSR_MODSEL_DATA(IP11_20_18, SCIFA2_TXD_B, SEL_SCIFA2_1),
	PINMUX_IPSR_DATA(IP11_20_18, IRQ8),
	PINMUX_IPSR_MODSEL_DATA(IP11_20_18, AUDIO_CLKA_D, SEL_ADG_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_20_18, CAN_CLK_D, SEL_CAN_3),
	PINMUX_IPSR_DATA(IP11_20_18, PCMOE_N),
	PINMUX_IPSR_DATA(IP11_23_21, SSI_SCK0129),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_21, MSIOF1_RXD_B, SEL_MSI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_21, SCIF5_RXD_D, SEL_SCIF5_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_21, ADIDATA_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_23_21, AD_DI_B, SEL_ADI_1),
	PINMUX_IPSR_DATA(IP11_23_21, PCMWE_N),
	PINMUX_IPSR_DATA(IP11_26_24, SSI_WS0129),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, MSIOF1_TXD_B, SEL_MSI1_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, SCIF5_TXD_D, SEL_SCIF5_3),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, ADICS_SAMP_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_26_24, AD_DO_B, SEL_ADI_1),
	PINMUX_IPSR_DATA(IP11_29_27, SSI_SDATA0),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, MSIOF1_SCK_B, SEL_MSI1_1),
	PINMUX_IPSR_DATA(IP11_29_27, PWM0_B),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, ADICLK_B, SEL_RAD_1),
	PINMUX_IPSR_MODSEL_DATA(IP11_29_27, AD_CLK_B, SEL_ADI_1),

	/*
	 * From IPSR12 to IPSR13 have been removed because they does not use.
	 */
};

static struct pinmux_gpio pinmux_gpios[] = {
	PINMUX_GPIO_GP_ALL(),

	GPIO_FN(A2), GPIO_FN(WE0_N), GPIO_FN(WE1_N), GPIO_FN(DACK0),
	GPIO_FN(USB0_PWEN), GPIO_FN(USB0_OVC), GPIO_FN(USB1_PWEN),
	GPIO_FN(USB1_OVC), GPIO_FN(SD0_CLK), GPIO_FN(SD0_CMD),
	GPIO_FN(SD0_DATA0), GPIO_FN(SD0_DATA1), GPIO_FN(SD0_DATA2),
	GPIO_FN(SD0_DATA3), GPIO_FN(SD0_CD), GPIO_FN(SD0_WP),
	GPIO_FN(SD1_CLK), GPIO_FN(SD1_CMD), GPIO_FN(SD1_DATA0),
	GPIO_FN(SD1_DATA1), GPIO_FN(SD1_DATA2), GPIO_FN(SD1_DATA3),

	/* IPSR0 */
	GPIO_FN(SD1_CD), GPIO_FN(CAN0_RX), GPIO_FN(SD1_WP), GPIO_FN(IRQ7),
	GPIO_FN(CAN0_TX), GPIO_FN(MMC_CLK), GPIO_FN(SD2_CLK), GPIO_FN(MMC_CMD),
	GPIO_FN(SD2_CMD), GPIO_FN(MMC_D0), GPIO_FN(SD2_DATA0), GPIO_FN(MMC_D1),
	GPIO_FN(SD2_DATA1), GPIO_FN(MMC_D2), GPIO_FN(SD2_DATA2),
	GPIO_FN(MMC_D3), GPIO_FN(SD2_DATA3), GPIO_FN(MMC_D4),
	GPIO_FN(SD2_CD), GPIO_FN(MMC_D5), GPIO_FN(SD2_WP), GPIO_FN(MMC_D6),
	GPIO_FN(SCIF0_RXD), GPIO_FN(I2C2_SCL_B), GPIO_FN(CAN1_RX),
	GPIO_FN(MMC_D7), GPIO_FN(SCIF0_TXD), GPIO_FN(I2C2_SDA_B),
	GPIO_FN(CAN1_TX), GPIO_FN(D0), GPIO_FN(SCIFA3_SCK_B), GPIO_FN(IRQ4),
	GPIO_FN(D1), GPIO_FN(SCIFA3_RXD_B), GPIO_FN(D2), GPIO_FN(SCIFA3_TXD_B),
	GPIO_FN(D3), GPIO_FN(I2C3_SCL_B), GPIO_FN(SCIF5_RXD_B), GPIO_FN(D4),
	GPIO_FN(I2C3_SDA_B), GPIO_FN(SCIF5_TXD_B), GPIO_FN(D5),
	GPIO_FN(SCIF4_RXD_B), GPIO_FN(I2C0_SCL_D),

	/*
	 * From IPSR1 to IPSR5 have been removed because they does not use.
	 */

	/* IPSR6 */
	GPIO_FN(DU0_EXVSYNC_DU0_VSYNC), GPIO_FN(QSTB_QHE),
	GPIO_FN(CC50_STATE28), GPIO_FN(DU0_EXODDF_DU0_ODDF_DISP_CDE),
	GPIO_FN(QCPV_QDE), GPIO_FN(CC50_STATE29), GPIO_FN(DU0_DISP),
	GPIO_FN(QPOLA), GPIO_FN(CC50_STATE30), GPIO_FN(DU0_CDE), GPIO_FN(QPOLB),
	GPIO_FN(CC50_STATE31), GPIO_FN(VI0_CLK), GPIO_FN(AVB_RX_CLK),
	GPIO_FN(VI0_DATA0_VI0_B0), GPIO_FN(AVB_RX_DV),
	GPIO_FN(VI0_DATA1_VI0_B1), GPIO_FN(AVB_RXD0), GPIO_FN(VI0_DATA2_VI0_B2),
	GPIO_FN(AVB_RXD1), GPIO_FN(VI0_DATA3_VI0_B3), GPIO_FN(AVB_RXD2),
	GPIO_FN(VI0_DATA4_VI0_B4), GPIO_FN(AVB_RXD3), GPIO_FN(VI0_DATA5_VI0_B5),
	GPIO_FN(AVB_RXD4), GPIO_FN(VI0_DATA6_VI0_B6), GPIO_FN(AVB_RXD5),
	GPIO_FN(VI0_DATA7_VI0_B7), GPIO_FN(AVB_RXD6), GPIO_FN(VI0_CLKENB),
	GPIO_FN(I2C3_SCL), GPIO_FN(SCIFA5_RXD_C), GPIO_FN(IETX_C),
	GPIO_FN(AVB_RXD7), GPIO_FN(VI0_FIELD), GPIO_FN(I2C3_SDA),
	GPIO_FN(SCIFA5_TXD_C), GPIO_FN(IECLK_C), GPIO_FN(AVB_RX_ER),
	GPIO_FN(VI0_HSYNC_N), GPIO_FN(SCIF0_RXD_B), GPIO_FN(I2C0_SCL_C),
	GPIO_FN(IERX_C), GPIO_FN(AVB_COL), GPIO_FN(VI0_VSYNC_N),
	GPIO_FN(SCIF0_TXD_B), GPIO_FN(I2C0_SDA_C), GPIO_FN(AUDIO_CLKOUT_B),
	GPIO_FN(AVB_TX_EN), GPIO_FN(ETH_MDIO), GPIO_FN(VI0_G0),
	GPIO_FN(MSIOF2_RXD_B), GPIO_FN(IIC0_SCL_D), GPIO_FN(AVB_TX_CLK),
	GPIO_FN(ADIDATA), GPIO_FN(AD_DI),

	/* IPSR7 */
	GPIO_FN(ETH_CRS_DV), GPIO_FN(VI0_G1), GPIO_FN(MSIOF2_TXD_B),
	GPIO_FN(IIC0_SDA_D), GPIO_FN(AVB_TXD0), GPIO_FN(ADICS_SAMP),
	GPIO_FN(AD_DO), GPIO_FN(ETH_RX_ER), GPIO_FN(VI0_G2),
	GPIO_FN(MSIOF2_SCK_B), GPIO_FN(CAN0_RX_B), GPIO_FN(AVB_TXD1),
	GPIO_FN(ADICLK), GPIO_FN(AD_CLK), GPIO_FN(ETH_RXD0), GPIO_FN(VI0_G3),
	GPIO_FN(MSIOF2_SYNC_B), GPIO_FN(CAN0_TX_B), GPIO_FN(AVB_TXD2),
	GPIO_FN(ADICHS0), GPIO_FN(AD_NCS_N), GPIO_FN(ETH_RXD1),
	GPIO_FN(VI0_G4), GPIO_FN(MSIOF2_SS1_B), GPIO_FN(SCIF4_RXD_D),
	GPIO_FN(AVB_TXD3), GPIO_FN(ADICHS1), GPIO_FN(ETH_LINK), GPIO_FN(VI0_G5),
	GPIO_FN(MSIOF2_SS2_B), GPIO_FN(SCIF4_TXD_D), GPIO_FN(AVB_TXD4),
	GPIO_FN(ADICHS2), GPIO_FN(ETH_REFCLK), GPIO_FN(VI0_G6),
	GPIO_FN(SCIF2_SCK_C), GPIO_FN(AVB_TXD5), GPIO_FN(SSI_SCK5_B),
	GPIO_FN(ETH_TXD1), GPIO_FN(VI0_G7), GPIO_FN(SCIF2_RXD_C),
	GPIO_FN(IIC1_SCL_D), GPIO_FN(AVB_TXD6), GPIO_FN(SSI_WS5_B),
	GPIO_FN(ETH_TX_EN), GPIO_FN(VI0_R0), GPIO_FN(SCIF2_TXD_C),
	GPIO_FN(IIC1_SDA_D), GPIO_FN(AVB_TXD7), GPIO_FN(SSI_SDATA5_B),
	GPIO_FN(ETH_MAGIC), GPIO_FN(VI0_R1), GPIO_FN(SCIF3_SCK_B),
	GPIO_FN(AVB_TX_ER), GPIO_FN(SSI_SCK6_B), GPIO_FN(ETH_TXD0),
	GPIO_FN(VI0_R2), GPIO_FN(SCIF3_RXD_B), GPIO_FN(I2C4_SCL_E),
	GPIO_FN(AVB_GTX_CLK), GPIO_FN(SSI_WS6_B), GPIO_FN(DREQ0_N),
	GPIO_FN(SCIFB1_RXD),

	/* IPSR8 */
	GPIO_FN(ETH_MDC), GPIO_FN(VI0_R3), GPIO_FN(SCIF3_TXD_B),
	GPIO_FN(I2C4_SDA_E), GPIO_FN(AVB_MDC), GPIO_FN(SSI_SDATA6_B),
	GPIO_FN(HSCIF0_HRX), GPIO_FN(VI0_R4), GPIO_FN(I2C1_SCL_C),
	GPIO_FN(AUDIO_CLKA_B), GPIO_FN(AVB_MDIO), GPIO_FN(SSI_SCK78_B),
	GPIO_FN(HSCIF0_HTX), GPIO_FN(VI0_R5), GPIO_FN(I2C1_SDA_C),
	GPIO_FN(AUDIO_CLKB_B), GPIO_FN(AVB_LINK), GPIO_FN(SSI_WS78_B),
	GPIO_FN(HSCIF0_HCTS_N), GPIO_FN(VI0_R6), GPIO_FN(SCIF0_RXD_D),
	GPIO_FN(I2C0_SCL_E), GPIO_FN(AVB_MAGIC), GPIO_FN(SSI_SDATA7_B),
	GPIO_FN(HSCIF0_HRTS_N), GPIO_FN(VI0_R7), GPIO_FN(SCIF0_TXD_D),
	GPIO_FN(I2C0_SDA_E), GPIO_FN(AVB_PHY_INT), GPIO_FN(SSI_SDATA8_B),
	GPIO_FN(HSCIF0_HSCK), GPIO_FN(SCIF_CLK_B), GPIO_FN(AVB_CRS),
	GPIO_FN(AUDIO_CLKC_B), GPIO_FN(I2C0_SCL), GPIO_FN(SCIF0_RXD_C),
	GPIO_FN(PWM5), GPIO_FN(TCLK1_B), GPIO_FN(AVB_GTXREFCLK),
	GPIO_FN(CAN1_RX_D), GPIO_FN(TPUTO0_B), GPIO_FN(I2C0_SDA),
	GPIO_FN(SCIF0_TXD_C), GPIO_FN(TPUTO0), GPIO_FN(CAN_CLK),
	GPIO_FN(DVC_MUTE), GPIO_FN(CAN1_TX_D), GPIO_FN(I2C1_SCL),
	GPIO_FN(SCIF4_RXD), GPIO_FN(PWM5_B), GPIO_FN(DU1_DR0),
	GPIO_FN(RIF1_SYNC_B), GPIO_FN(TS_SDATA_D), GPIO_FN(TPUTO1_B),
	GPIO_FN(I2C1_SDA), GPIO_FN(SCIF4_TXD), GPIO_FN(IRQ5),
	GPIO_FN(DU1_DR1), GPIO_FN(RIF1_CLK_B), GPIO_FN(TS_SCK_D),
	GPIO_FN(BPFCLK_C), GPIO_FN(MSIOF0_RXD), GPIO_FN(SCIF5_RXD),
	GPIO_FN(I2C2_SCL_C), GPIO_FN(DU1_DR2), GPIO_FN(RIF1_D0_B),
	GPIO_FN(TS_SDEN_D), GPIO_FN(FMCLK_C), GPIO_FN(RDS_CLK),

	/*
	 * From IPSR9 to IPSR10 have been removed because they does not use.
	 */

	/* IPSR11 */
	GPIO_FN(SSI_WS5), GPIO_FN(SCIFA3_RXD), GPIO_FN(I2C3_SCL_C),
	GPIO_FN(DU1_DOTCLKOUT0), GPIO_FN(CAN_DEBUGOUT11), GPIO_FN(SSI_SDATA5),
	GPIO_FN(SCIFA3_TXD), GPIO_FN(I2C3_SDA_C), GPIO_FN(DU1_DOTCLKOUT1),
	GPIO_FN(CAN_DEBUGOUT12), GPIO_FN(SSI_SCK6), GPIO_FN(SCIFA1_SCK_B),
	GPIO_FN(DU1_EXHSYNC_DU1_HSYNC), GPIO_FN(CAN_DEBUGOUT13),
	GPIO_FN(SSI_WS6), GPIO_FN(SCIFA1_RXD_B), GPIO_FN(I2C4_SCL_C),
	GPIO_FN(DU1_EXVSYNC_DU1_VSYNC), GPIO_FN(CAN_DEBUGOUT14),
	GPIO_FN(SSI_SDATA6), GPIO_FN(SCIFA1_TXD_B), GPIO_FN(I2C4_SDA_C),
	GPIO_FN(DU1_EXODDF_DU1_ODDF_DISP_CDE), GPIO_FN(CAN_DEBUGOUT15),
	GPIO_FN(SSI_SCK78), GPIO_FN(SCIFA2_SCK_B), GPIO_FN(IIC0_SDA_C),
	GPIO_FN(DU1_DISP), GPIO_FN(SSI_WS78), GPIO_FN(SCIFA2_RXD_B),
	GPIO_FN(IIC0_SCL_C), GPIO_FN(DU1_CDE), GPIO_FN(SSI_SDATA7),
	GPIO_FN(SCIFA2_TXD_B), GPIO_FN(IRQ8), GPIO_FN(AUDIO_CLKA_D),
	GPIO_FN(CAN_CLK_D), GPIO_FN(PCMOE_N), GPIO_FN(SSI_SCK0129),
	GPIO_FN(MSIOF1_RXD_B), GPIO_FN(SCIF5_RXD_D), GPIO_FN(ADIDATA_B),
	GPIO_FN(AD_DI_B), GPIO_FN(PCMWE_N), GPIO_FN(SSI_WS0129),
	GPIO_FN(MSIOF1_TXD_B), GPIO_FN(SCIF5_TXD_D), GPIO_FN(ADICS_SAMP_B),
	GPIO_FN(AD_DO_B), GPIO_FN(SSI_SDATA0), GPIO_FN(MSIOF1_SCK_B),
	GPIO_FN(PWM0_B), GPIO_FN(ADICLK_B), GPIO_FN(AD_CLK_B),

	/*
	 * From IPSR12 to IPSR13 have been removed because they does not use.
	 */
};

static struct pinmux_cfg_reg pinmux_config_regs[] = {
	{ PINMUX_CFG_REG("GPSR0", 0xE6060004, 32, 1) {
		GP_0_31_FN, FN_IP2_17_16,
		GP_0_30_FN, FN_IP2_15_14,
		GP_0_29_FN, FN_IP2_13_12,
		GP_0_28_FN, FN_IP2_11_10,
		GP_0_27_FN, FN_IP2_9_8,
		GP_0_26_FN, FN_IP2_7_6,
		GP_0_25_FN, FN_IP2_5_4,
		GP_0_24_FN, FN_IP2_3_2,
		GP_0_23_FN, FN_IP2_1_0,
		GP_0_22_FN, FN_IP1_31_30,
		GP_0_21_FN, FN_IP1_29_28,
		GP_0_20_FN, FN_IP1_27,
		GP_0_19_FN, FN_IP1_26,
		GP_0_18_FN, FN_A2,
		GP_0_17_FN, FN_IP1_24,
		GP_0_16_FN, FN_IP1_23_22,
		GP_0_15_FN, FN_IP1_21_20,
		GP_0_14_FN, FN_IP1_19_18,
		GP_0_13_FN, FN_IP1_17_15,
		GP_0_12_FN, FN_IP1_14_13,
		GP_0_11_FN, FN_IP1_12_11,
		GP_0_10_FN, FN_IP1_10_8,
		GP_0_9_FN, FN_IP1_7_6,
		GP_0_8_FN, FN_IP1_5_4,
		GP_0_7_FN, FN_IP1_3_2,
		GP_0_6_FN, FN_IP1_1_0,
		GP_0_5_FN, FN_IP0_31_30,
		GP_0_4_FN, FN_IP0_29_28,
		GP_0_3_FN, FN_IP0_27_26,
		GP_0_2_FN, FN_IP0_25,
		GP_0_1_FN, FN_IP0_24,
		GP_0_0_FN, FN_IP0_23_22, }
	},
	{ PINMUX_CFG_REG("GPSR1", 0xE6060008, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_1_25_FN, FN_DACK0,
		GP_1_24_FN, FN_IP7_31,
		GP_1_23_FN, FN_IP4_1_0,
		GP_1_22_FN, FN_WE1_N,
		GP_1_21_FN, FN_WE0_N,
		GP_1_20_FN, FN_IP3_31,
		GP_1_19_FN, FN_IP3_30,
		GP_1_18_FN, FN_IP3_29_27,
		GP_1_17_FN, FN_IP3_26_24,
		GP_1_16_FN, FN_IP3_23_21,
		GP_1_15_FN, FN_IP3_20_18,
		GP_1_14_FN, FN_IP3_17_15,
		GP_1_13_FN, FN_IP3_14_13,
		GP_1_12_FN, FN_IP3_12,
		GP_1_11_FN, FN_IP3_11,
		GP_1_10_FN, FN_IP3_10,
		GP_1_9_FN, FN_IP3_9_8,
		GP_1_8_FN, FN_IP3_7_6,
		GP_1_7_FN, FN_IP3_5_4,
		GP_1_6_FN, FN_IP3_3_2,
		GP_1_5_FN, FN_IP3_1_0,
		GP_1_4_FN, FN_IP2_31_30,
		GP_1_3_FN, FN_IP2_29_27,
		GP_1_2_FN, FN_IP2_26_24,
		GP_1_1_FN, FN_IP2_23_21,
		GP_1_0_FN, FN_IP2_20_18, }
	},
	{ PINMUX_CFG_REG("GPSR2", 0xE606000C, 32, 1) {
		GP_2_31_FN, FN_IP6_7_6,
		GP_2_30_FN, FN_IP6_5_4,
		GP_2_29_FN, FN_IP6_3_2,
		GP_2_28_FN, FN_IP6_1_0,
		GP_2_27_FN, FN_IP5_31_30,
		GP_2_26_FN, FN_IP5_29_28,
		GP_2_25_FN, FN_IP5_27_26,
		GP_2_24_FN, FN_IP5_25_24,
		GP_2_23_FN, FN_IP5_23_22,
		GP_2_22_FN, FN_IP5_21_20,
		GP_2_21_FN, FN_IP5_19_18,
		GP_2_20_FN, FN_IP5_17_16,
		GP_2_19_FN, FN_IP5_15_14,
		GP_2_18_FN, FN_IP5_13_12,
		GP_2_17_FN, FN_IP5_11_9,
		GP_2_16_FN, FN_IP5_8_6,
		GP_2_15_FN, FN_IP5_5_4,
		GP_2_14_FN, FN_IP5_3_2,
		GP_2_13_FN, FN_IP5_1_0,
		GP_2_12_FN, FN_IP4_31_30,
		GP_2_11_FN, FN_IP4_29_28,
		GP_2_10_FN, FN_IP4_27_26,
		GP_2_9_FN, FN_IP4_25_23,
		GP_2_8_FN, FN_IP4_22_20,
		GP_2_7_FN, FN_IP4_19_18,
		GP_2_6_FN, FN_IP4_17_16,
		GP_2_5_FN, FN_IP4_15_14,
		GP_2_4_FN, FN_IP4_13_12,
		GP_2_3_FN, FN_IP4_11_10,
		GP_2_2_FN, FN_IP4_9_8,
		GP_2_1_FN, FN_IP4_7_5,
		GP_2_0_FN, FN_IP4_4_2 }
	},
	{ PINMUX_CFG_REG("GPSR3", 0xE6060010, 32, 1) {
		GP_3_31_FN, FN_IP8_22_20,
		GP_3_30_FN, FN_IP8_19_17,
		GP_3_29_FN, FN_IP8_16_15,
		GP_3_28_FN, FN_IP8_14_12,
		GP_3_27_FN, FN_IP8_11_9,
		GP_3_26_FN, FN_IP8_8_6,
		GP_3_25_FN, FN_IP8_5_3,
		GP_3_24_FN, FN_IP8_2_0,
		GP_3_23_FN, FN_IP7_29_27,
		GP_3_22_FN, FN_IP7_26_24,
		GP_3_21_FN, FN_IP7_23_21,
		GP_3_20_FN, FN_IP7_20_18,
		GP_3_19_FN, FN_IP7_17_15,
		GP_3_18_FN, FN_IP7_14_12,
		GP_3_17_FN, FN_IP7_11_9,
		GP_3_16_FN, FN_IP7_8_6,
		GP_3_15_FN, FN_IP7_5_3,
		GP_3_14_FN, FN_IP7_2_0,
		GP_3_13_FN, FN_IP6_31_29,
		GP_3_12_FN, FN_IP6_28_26,
		GP_3_11_FN, FN_IP6_25_23,
		GP_3_10_FN, FN_IP6_22_20,
		GP_3_9_FN, FN_IP6_19_17,
		GP_3_8_FN, FN_IP6_16,
		GP_3_7_FN, FN_IP6_15,
		GP_3_6_FN, FN_IP6_14,
		GP_3_5_FN, FN_IP6_13,
		GP_3_4_FN, FN_IP6_12,
		GP_3_3_FN, FN_IP6_11,
		GP_3_2_FN, FN_IP6_10,
		GP_3_1_FN, FN_IP6_9,
		GP_3_0_FN, FN_IP6_8 }
	},
	{ PINMUX_CFG_REG("GPSR4", 0xE6060014, 32, 1) {
		GP_4_31_FN, FN_IP11_17_16,
		GP_4_30_FN, FN_IP11_15_14,
		GP_4_29_FN, FN_IP11_13_11,
		GP_4_28_FN, FN_IP11_10_8,
		GP_4_27_FN, FN_IP11_7_6,
		GP_4_26_FN, FN_IP11_5_3,
		GP_4_25_FN, FN_IP11_2_0,
		GP_4_24_FN, FN_IP10_31_30,
		GP_4_23_FN, FN_IP10_29_27,
		GP_4_22_FN, FN_IP10_26_24,
		GP_4_21_FN, FN_IP10_23_21,
		GP_4_20_FN, FN_IP10_20_18,
		GP_4_19_FN, FN_IP10_17_15,
		GP_4_18_FN, FN_IP10_14_12,
		GP_4_17_FN, FN_IP10_11_9,
		GP_4_16_FN, FN_IP10_8_6,
		GP_4_15_FN, FN_IP10_5_3,
		GP_4_14_FN, FN_IP10_2_0,
		GP_4_13_FN, FN_IP9_30_28,
		GP_4_12_FN, FN_IP9_27_25,
		GP_4_11_FN, FN_IP9_24_22,
		GP_4_10_FN, FN_IP9_21_19,
		GP_4_9_FN, FN_IP9_18_17,
		GP_4_8_FN, FN_IP9_16_15,
		GP_4_7_FN, FN_IP9_14_12,
		GP_4_6_FN, FN_IP9_11_9,
		GP_4_5_FN, FN_IP9_8_6,
		GP_4_4_FN, FN_IP9_5_3,
		GP_4_3_FN, FN_IP9_2_0,
		GP_4_2_FN, FN_IP8_31_29,
		GP_4_1_FN, FN_IP8_28_26,
		GP_4_0_FN, FN_IP8_25_23 }
	},
	{ PINMUX_CFG_REG("GPSR5", 0xE6060018, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_5_27_FN, FN_USB1_OVC,
		GP_5_26_FN, FN_USB1_PWEN,
		GP_5_25_FN, FN_USB0_OVC,
		GP_5_24_FN, FN_USB0_PWEN,
		GP_5_23_FN, FN_IP13_26_24,
		GP_5_22_FN, FN_IP13_23_21,
		GP_5_21_FN, FN_IP13_20_18,
		GP_5_20_FN, FN_IP13_17_15,
		GP_5_19_FN, FN_IP13_14_12,
		GP_5_18_FN, FN_IP13_11_9,
		GP_5_17_FN, FN_IP13_8_6,
		GP_5_16_FN, FN_IP13_5_3,
		GP_5_15_FN, FN_IP13_2_0,
		GP_5_14_FN, FN_IP12_29_27,
		GP_5_13_FN, FN_IP12_26_24,
		GP_5_12_FN, FN_IP12_23_21,
		GP_5_11_FN, FN_IP12_20_18,
		GP_5_10_FN, FN_IP12_17_15,
		GP_5_9_FN, FN_IP12_14_13,
		GP_5_8_FN, FN_IP12_12_11,
		GP_5_7_FN, FN_IP12_10_9,
		GP_5_6_FN, FN_IP12_8_6,
		GP_5_5_FN, FN_IP12_5_3,
		GP_5_4_FN, FN_IP12_2_0,
		GP_5_3_FN, FN_IP11_29_27,
		GP_5_2_FN, FN_IP11_26_24,
		GP_5_1_FN, FN_IP11_23_21,
		GP_5_0_FN, FN_IP11_20_18 }
	},
	{ PINMUX_CFG_REG("GPSR6", 0xE606001C, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_6_25_FN, FN_IP0_21_20,
		GP_6_24_FN, FN_IP0_19_18,
		GP_6_23_FN, FN_IP0_17,
		GP_6_22_FN, FN_IP0_16,
		GP_6_21_FN, FN_IP0_15,
		GP_6_20_FN, FN_IP0_14,
		GP_6_19_FN, FN_IP0_13,
		GP_6_18_FN, FN_IP0_12,
		GP_6_17_FN, FN_IP0_11,
		GP_6_16_FN, FN_IP0_10,
		GP_6_15_FN, FN_IP0_9_8,
		GP_6_14_FN, FN_IP0_0,
		GP_6_13_FN, FN_SD1_DATA3,
		GP_6_12_FN, FN_SD1_DATA2,
		GP_6_11_FN, FN_SD1_DATA1,
		GP_6_10_FN, FN_SD1_DATA0,
		GP_6_9_FN, FN_SD1_CMD,
		GP_6_8_FN, FN_SD1_CLK,
		GP_6_7_FN, FN_SD0_WP,
		GP_6_6_FN, FN_SD0_CD,
		GP_6_5_FN, FN_SD0_DATA3,
		GP_6_4_FN, FN_SD0_DATA2,
		GP_6_3_FN, FN_SD0_DATA1,
		GP_6_2_FN, FN_SD0_DATA0,
		GP_6_1_FN, FN_SD0_CMD,
		GP_6_0_FN, FN_SD0_CLK }
	},
	{ PINMUX_CFG_REG_VAR("IPSR0", 0xE6060020, 32,
			     2, 2, 2, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1,
			     2, 1, 1, 1, 1, 1, 1, 1, 1) {
		/* IP0_31_30 [2] */
		FN_D5, FN_SCIF4_RXD_B, FN_I2C0_SCL_D, 0,
		/* IP0_29_28 [2] */
		FN_D4, FN_I2C3_SDA_B, FN_SCIF5_TXD_B, 0,
		/* IP0_27_26 [2] */
		FN_D3, FN_I2C3_SCL_B, FN_SCIF5_RXD_B, 0,
		/* IP0_25 [1] */
		FN_D2, FN_SCIFA3_TXD_B,
		/* IP0_24 [1] */
		FN_D1, FN_SCIFA3_RXD_B,
		/* IP0_23_22 [2] */
		FN_D0, FN_SCIFA3_SCK_B, FN_IRQ4, 0,
		/* IP0_21_20 [2] */
		FN_MMC_D7, FN_SCIF0_TXD, FN_I2C2_SDA_B, FN_CAN1_TX,
		/* IP0_19_18 [2] */
		FN_MMC_D6, FN_SCIF0_RXD, FN_I2C2_SCL_B,	FN_CAN1_RX,
		/* IP0_17 [1] */
		FN_MMC_D5, FN_SD2_WP,
		/* IP0_16 [1] */
		FN_MMC_D4, FN_SD2_CD,
		/* IP0_15 [1] */
		FN_MMC_D3, FN_SD2_DATA3,
		/* IP0_14 [1] */
		FN_MMC_D2, FN_SD2_DATA2,
		/* IP0_13 [1] */
		FN_MMC_D1, FN_SD2_DATA1,
		/* IP0_12 [1] */
		FN_MMC_D0, FN_SD2_DATA0,
		/* IP0_11 [1] */
		FN_MMC_CMD, FN_SD2_CMD,
		/* IP0_10 [1] */
		FN_MMC_CLK, FN_SD2_CLK,
		/* IP0_9_8 [2] */
		FN_SD1_WP, FN_IRQ7, FN_CAN0_TX, 0,
		/* IP0_7 [1] */
		0, 0,
		/* IP0_6 [1] */
		0, 0,
		/* IP0_5 [1] */
		0, 0,
		/* IP0_4 [1] */
		0, 0,
		/* IP0_3 [1] */
		0, 0,
		/* IP0_2 [1] */
		0, 0,
		/* IP0_1 [1] */
		0, 0,
		/* IP0_0 [1] */
		FN_SD1_CD, FN_CAN0_RX, }
	},

	/*
	 * From IPSR1 to IPSR5 have been removed because they does not use.
	 */

	{ PINMUX_CFG_REG_VAR("IPSR6", 0xE6060038, 32,
			     3, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2,
			     2, 2) {
		/* IP6_31_29 [3] */
		FN_ETH_MDIO, FN_VI0_G0, FN_MSIOF2_RXD_B, FN_IIC0_SCL_D,
		FN_AVB_TX_CLK, FN_ADIDATA, FN_AD_DI, 0,
		/* IP6_28_26 [3] */
		FN_VI0_VSYNC_N, FN_SCIF0_TXD_B, FN_I2C0_SDA_C,
		FN_AUDIO_CLKOUT_B, FN_AVB_TX_EN, 0, 0, 0,
		/* IP6_25_23 [3] */
		FN_VI0_HSYNC_N, FN_SCIF0_RXD_B, FN_I2C0_SCL_C, FN_IERX_C,
		FN_AVB_COL, 0, 0, 0,
		/* IP6_22_20 [3] */
		FN_VI0_FIELD, FN_I2C3_SDA, FN_SCIFA5_TXD_C, FN_IECLK_C,
		FN_AVB_RX_ER, 0, 0, 0,
		/* IP6_19_17 [3] */
		FN_VI0_CLKENB, FN_I2C3_SCL, FN_SCIFA5_RXD_C, FN_IETX_C,
		FN_AVB_RXD7, 0, 0, 0,
		/* IP6_16 [1] */
		FN_VI0_DATA7_VI0_B7, FN_AVB_RXD6,
		/* IP6_15 [1] */
		FN_VI0_DATA6_VI0_B6, FN_AVB_RXD5,
		/* IP6_14 [1] */
		FN_VI0_DATA5_VI0_B5, FN_AVB_RXD4,
		/* IP6_13 [1] */
		FN_VI0_DATA4_VI0_B4, FN_AVB_RXD3,
		/* IP6_12 [1] */
		FN_VI0_DATA3_VI0_B3, FN_AVB_RXD2,
		/* IP6_11 [1] */
		FN_VI0_DATA2_VI0_B2, FN_AVB_RXD1,
		/* IP6_10 [1] */
		FN_VI0_DATA1_VI0_B1, FN_AVB_RXD0,
		/* IP6_9 [1] */
		FN_VI0_DATA0_VI0_B0, FN_AVB_RX_DV,
		/* IP6_8 [1] */
		FN_VI0_CLK, FN_AVB_RX_CLK,
		/* IP6_7_6 [2] */
		FN_DU0_CDE, FN_QPOLB, FN_CC50_STATE31, 0,
		/* IP6_5_4 [2] */
		FN_DU0_DISP, FN_QPOLA, FN_CC50_STATE30, 0,
		/* IP6_3_2 [2] */
		FN_DU0_EXODDF_DU0_ODDF_DISP_CDE, FN_QCPV_QDE, FN_CC50_STATE29,
		/* IP6_1_0 [2] */
		FN_DU0_EXVSYNC_DU0_VSYNC, FN_QSTB_QHE, FN_CC50_STATE28, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR7", 0xE606003C, 32,
			     1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3) {
		/* IP7_31 [1] */
		FN_DREQ0_N, FN_SCIFB1_RXD,
		/* IP7_30 [1] */
		0, 0,
		/* IP7_29_27 [3] */
		FN_ETH_TXD0, FN_VI0_R2, FN_SCIF3_RXD_B, FN_I2C4_SCL_E,
		FN_AVB_GTX_CLK, FN_SSI_WS6_B, 0, 0,
		/* IP7_26_24 [3] */
		FN_ETH_MAGIC, FN_VI0_R1, FN_SCIF3_SCK_B, FN_AVB_TX_ER,
		FN_SSI_SCK6_B, 0, 0, 0,
		/* IP7_23_21 [3] */
		FN_ETH_TX_EN, FN_VI0_R0, FN_SCIF2_TXD_C, FN_IIC1_SDA_D,
		FN_AVB_TXD7, FN_SSI_SDATA5_B, 0, 0,
		/* IP7_20_18 [3] */
		FN_ETH_TXD1, FN_VI0_G7, FN_SCIF2_RXD_C, FN_IIC1_SCL_D,
		FN_AVB_TXD6, FN_SSI_WS5_B, 0, 0,
		/* IP7_17_15 [3] */
		FN_ETH_REFCLK, FN_VI0_G6, FN_SCIF2_SCK_C, FN_AVB_TXD5,
		FN_SSI_SCK5_B, 0, 0, 0,
		/* IP7_14_12 [3] */
		FN_ETH_LINK, FN_VI0_G5, FN_MSIOF2_SS2_B, FN_SCIF4_TXD_D,
		FN_AVB_TXD4, FN_ADICHS2, 0, 0,
		/* IP7_11_9 [3] */
		FN_ETH_RXD1, FN_VI0_G4, FN_MSIOF2_SS1_B, FN_SCIF4_RXD_D,
		FN_AVB_TXD3, FN_ADICHS1, 0, 0,
		/* IP7_8_6 [3] */
		FN_ETH_RXD0, FN_VI0_G3, FN_MSIOF2_SYNC_B, FN_CAN0_TX_B,
		FN_AVB_TXD2, FN_ADICHS0, FN_AD_NCS_N, 0,
		/* IP7_5_3 [3] */
		FN_ETH_RX_ER, FN_VI0_G2, FN_MSIOF2_SCK_B, FN_CAN0_RX_B,
		FN_AVB_TXD1, FN_ADICLK, FN_AD_CLK, 0,
		/* IP7_2_0 [3] */
		FN_ETH_CRS_DV, FN_VI0_G1, FN_MSIOF2_TXD_B, FN_IIC0_SDA_D,
		FN_AVB_TXD0, FN_ADICS_SAMP, FN_AD_DO, 0, }
	},
	{ PINMUX_CFG_REG_VAR("IPSR8", 0xE6060040, 32,
			     3, 3, 3, 3, 3, 2, 3, 3, 3, 3, 3) {
		/* IP8_31_29 [3] */
		FN_MSIOF0_RXD, FN_SCIF5_RXD, FN_I2C2_SCL_C, FN_DU1_DR2,
		FN_RIF1_D0_B, FN_TS_SDEN_D, FN_FMCLK_C, FN_RDS_CLK,
		/* IP8_28_26 [3] */
		FN_I2C1_SDA, FN_SCIF4_TXD, FN_IRQ5, FN_DU1_DR1,
		FN_RIF1_CLK_B, FN_TS_SCK_D, FN_BPFCLK_C, 0,
		/* IP8_25_23 [3] */
		FN_I2C1_SCL, FN_SCIF4_RXD, FN_PWM5_B, FN_DU1_DR0,
		FN_RIF1_SYNC_B, FN_TS_SDATA_D, FN_TPUTO1_B, 0,
		/* IP8_22_20 [3] */
		FN_I2C0_SDA, FN_SCIF0_TXD_C, FN_TPUTO0, FN_CAN_CLK,
		FN_DVC_MUTE, FN_CAN1_TX_D, 0, 0,
		/* IP8_19_17 [3] */
		FN_I2C0_SCL, FN_SCIF0_RXD_C, FN_PWM5, FN_TCLK1_B,
		FN_AVB_GTXREFCLK, FN_CAN1_RX_D, FN_TPUTO0_B, 0,
		/* IP8_16_15 [2] */
		FN_HSCIF0_HSCK, FN_SCIF_CLK_B, FN_AVB_CRS, FN_AUDIO_CLKC_B,
		/* IP8_14_12 [3] */
		FN_HSCIF0_HRTS_N, FN_VI0_R7, FN_SCIF0_TXD_D, FN_I2C0_SDA_E,
		FN_AVB_PHY_INT, FN_SSI_SDATA8_B, 0, 0,
		/* IP8_11_9 [3] */
		FN_HSCIF0_HCTS_N, FN_VI0_R6, FN_SCIF0_RXD_D, FN_I2C0_SCL_E,
		FN_AVB_MAGIC, FN_SSI_SDATA7_B, 0, 0,
		/* IP8_8_6 [3] */
		FN_HSCIF0_HTX, FN_VI0_R5, FN_I2C1_SDA_C, FN_AUDIO_CLKB_B,
		FN_AVB_LINK, FN_SSI_WS78_B, 0, 0,
		/* IP8_5_3 [3] */
		FN_HSCIF0_HRX, FN_VI0_R4, FN_I2C1_SCL_C, FN_AUDIO_CLKA_B,
		FN_AVB_MDIO, FN_SSI_SCK78_B, 0, 0,
		/* IP8_2_0 [3] */
		FN_ETH_MDC, FN_VI0_R3, FN_SCIF3_TXD_B, FN_I2C4_SDA_E,
		FN_AVB_MDC, FN_SSI_SDATA6_B, 0, 0, }
	},

	/*
	 * From IPSR9 to IPSR10 have been removed because they does not use.
	 */

	{ PINMUX_CFG_REG_VAR("IPSR11", 0xE606004C, 32,
			     2, 3, 3, 3, 3, 2, 2, 3, 3, 2, 3, 3) {
		/* IP11_31_30 [2] */
		0, 0, 0, 0,
		/* IP11_29_27 [3] */
		FN_SSI_SDATA0, FN_MSIOF1_SCK_B, FN_PWM0_B, FN_ADICLK_B,
		FN_AD_CLK_B, 0, 0, 0,
		/* IP11_26_24 [3] */
		FN_SSI_WS0129, FN_MSIOF1_TXD_B, FN_SCIF5_TXD_D, FN_ADICS_SAMP_B,
		FN_AD_DO_B, 0, 0, 0,
		/* IP11_23_21 [3] */
		FN_SSI_SCK0129, FN_MSIOF1_RXD_B, FN_SCIF5_RXD_D, FN_ADIDATA_B,
		FN_AD_DI_B, FN_PCMWE_N, 0, 0,
		/* IP11_20_18 [3] */
		FN_SSI_SDATA7, FN_SCIFA2_TXD_B, FN_IRQ8, FN_AUDIO_CLKA_D,
		FN_CAN_CLK_D, FN_PCMOE_N, 0, 0,
		/* IP11_17_16 [2] */
		FN_SSI_WS78, FN_SCIFA2_RXD_B, FN_IIC0_SCL_C, FN_DU1_CDE,
		/* IP11_15_14 [2] */
		FN_SSI_SCK78, FN_SCIFA2_SCK_B, FN_IIC0_SDA_C, FN_DU1_DISP,
		/* IP11_13_11 [3] */
		FN_SSI_SDATA6, FN_SCIFA1_TXD_B, FN_I2C4_SDA_C,
		FN_DU1_EXODDF_DU1_ODDF_DISP_CDE, FN_CAN_DEBUGOUT15, 0, 0, 0,
		/* IP11_10_8 [3] */
		FN_SSI_WS6, FN_SCIFA1_RXD_B, FN_I2C4_SCL_C,
		FN_DU1_EXVSYNC_DU1_VSYNC, FN_CAN_DEBUGOUT14, 0, 0, 0,
		/* IP11_7_6 [2] */
		FN_SSI_SCK6, FN_SCIFA1_SCK_B, FN_DU1_EXHSYNC_DU1_HSYNC,
		FN_CAN_DEBUGOUT13,
		/* IP11_5_3 [3] */
		FN_SSI_SDATA5, FN_SCIFA3_TXD, FN_I2C3_SDA_C, FN_DU1_DOTCLKOUT1,
		FN_CAN_DEBUGOUT12, 0, 0, 0,
		/* IP11_2_0 [3] */
		FN_SSI_WS5, FN_SCIFA3_RXD, FN_I2C3_SCL_C, FN_DU1_DOTCLKOUT0,
		FN_CAN_DEBUGOUT11, 0, 0, 0, }
	},

	/*
	 * From IPSR12 to IPSR13 have been removed because they does not use.
	 */

	{ PINMUX_CFG_REG_VAR("MOD_SEL", 0xE6060090, 32,
			     2, 1, 2, 3, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3, 3,
			     2, 1) {
		/* SEL_ADG [2] */
		FN_SEL_ADG_0, FN_SEL_ADG_1, FN_SEL_ADG_2, FN_SEL_ADG_3,
		/* SEL_ADI [1] */
		FN_SEL_ADI_0, FN_SEL_ADI_1,
		/* SEL_CAN [2] */
		FN_SEL_CAN_0, FN_SEL_CAN_1, FN_SEL_CAN_2, FN_SEL_CAN_3,
		/* SEL_DARC [3] */
		FN_SEL_DARC_0, FN_SEL_DARC_1, FN_SEL_DARC_2, FN_SEL_DARC_3,
		FN_SEL_DARC_4, 0, 0, 0,
		/* SEL_DR0 [1] */
		FN_SEL_DR0_0, FN_SEL_DR0_1,
		/* SEL_DR1 [1] */
		FN_SEL_DR1_0, FN_SEL_DR1_1,
		/* SEL_DR2 [1] */
		FN_SEL_DR2_0, FN_SEL_DR2_1,
		/* SEL_DR3 [1] */
		FN_SEL_DR3_0, FN_SEL_DR3_1,
		/* SEL_ETH [1] */
		FN_SEL_ETH_0, FN_SEL_ETH_1,
		/* SLE_FSN [1] */
		FN_SEL_FSN_0, FN_SEL_FSN_1,
		/* SEL_IC200 [3] */
		FN_SEL_I2C00_0, FN_SEL_I2C00_1, FN_SEL_I2C00_2, FN_SEL_I2C00_3,
		FN_SEL_I2C00_4, 0, 0, 0,
		/* SEL_I2C01 [3] */
		FN_SEL_I2C01_0, FN_SEL_I2C01_1, FN_SEL_I2C01_2, FN_SEL_I2C01_3,
		FN_SEL_I2C01_4, 0, 0, 0,
		/* SEL_I2C02 [3] */
		FN_SEL_I2C02_0, FN_SEL_I2C02_1, FN_SEL_I2C02_2, FN_SEL_I2C02_3,
		FN_SEL_I2C02_4, 0, 0, 0,
		/* SEL_I2C03 [3] */
		FN_SEL_I2C03_0, FN_SEL_I2C03_1, FN_SEL_I2C03_2, FN_SEL_I2C03_3,
		FN_SEL_I2C03_4, 0, 0, 0,
		/* SEL_I2C04 [3] */
		FN_SEL_I2C04_0, FN_SEL_I2C04_1, FN_SEL_I2C04_2, FN_SEL_I2C04_3,
		FN_SEL_I2C04_4, 0, 0, 0,
		/* SEL_IIC00 [2] */
		FN_SEL_IIC00_0, FN_SEL_IIC00_1, FN_SEL_IIC00_2, FN_SEL_IIC00_3,
		/* SEL_AVB [1] */
		FN_SEL_AVB_0, FN_SEL_AVB_1, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL2", 0xE6060094, 32,
			     2, 2, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 2, 2, 1, 1,
			     2, 2, 2, 1, 1, 2) {
		/* SEL_IEB [2] */
		FN_SEL_IEB_0, FN_SEL_IEB_1, FN_SEL_IEB_2, 0,
		/* SEL_IIC0 [2] */
		FN_SEL_IIC01_0, FN_SEL_IIC01_1, FN_SEL_IIC01_2, FN_SEL_IIC01_3,
		/* SEL_LBS [1] */
		FN_SEL_LBS_0, FN_SEL_LBS_1,
		/* SEL_MSI1 [1] */
		FN_SEL_MSI1_0, FN_SEL_MSI1_1,
		/* SEL_MSI2 [1] */
		FN_SEL_MSI2_0, FN_SEL_MSI2_1,
		/* SEL_RAD [1] */
		FN_SEL_RAD_0, FN_SEL_RAD_1,
		/* SEL_RCN [1] */
		FN_SEL_RCN_0, FN_SEL_RCN_1,
		/* SEL_RSP [1] */
		FN_SEL_RSP_0, FN_SEL_RSP_1,
		/* SEL_SCIFA0 [2] */
		FN_SEL_SCIFA0_0, FN_SEL_SCIFA0_1, FN_SEL_SCIFA0_2,
		FN_SEL_SCIFA0_3,
		/* SEL_SCIFA1 [2] */
		FN_SEL_SCIFA1_0, FN_SEL_SCIFA1_1, FN_SEL_SCIFA1_2, 0,
		/* SEL_SCIFA2 [1] */
		FN_SEL_SCIFA2_0, FN_SEL_SCIFA2_1,
		/* SEL_SCIFA3 [1] */
		FN_SEL_SCIFA3_0, FN_SEL_SCIFA3_1,
		/* SEL_SCIFA4 [2] */
		FN_SEL_SCIFA4_0, FN_SEL_SCIFA4_1, FN_SEL_SCIFA4_2,
		FN_SEL_SCIFA4_3,
		/* SEL_SCIFA5 [2] */
		FN_SEL_SCIFA5_0, FN_SEL_SCIFA5_1, FN_SEL_SCIFA5_2,
		FN_SEL_SCIFA5_3,
		/* SEL_SPDM [1] */
		FN_SEL_SPDM_0, FN_SEL_SPDM_1,
		/* SEL_TMU [1] */
		FN_SEL_TMU_0, FN_SEL_TMU_1,
		/* SEL_TSIF0 [2] */
		FN_SEL_TSIF0_0, FN_SEL_TSIF0_1, FN_SEL_TSIF0_2, FN_SEL_TSIF0_3,
		/* SEL_CAN0 [2] */
		FN_SEL_CAN0_0, FN_SEL_CAN0_1, FN_SEL_CAN0_2, FN_SEL_CAN0_3,
		/* SEL_CAN1 [2] */
		FN_SEL_CAN1_0, FN_SEL_CAN1_1, FN_SEL_CAN1_2, FN_SEL_CAN1_3,
		/* SEL_HSCIF0 [1] */
		FN_SEL_HSCIF0_0, FN_SEL_HSCIF0_1,
		/* SEL_HSCIF1 [1] */
		FN_SEL_HSCIF1_0, FN_SEL_HSCIF1_1,
		/* SEL_RDS [2] */
		FN_SEL_RDS_0, FN_SEL_RDS_1, FN_SEL_RDS_2, FN_SEL_RDS_3, }
	},
	{ PINMUX_CFG_REG_VAR("MOD_SEL3", 0xE6060098, 32,
			     2, 2, 2, 1, 3, 2, 1, 1, 1, 1, 1, 1, 1, 1,
			     1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1) {
		/* SEL_SCIF0 [2] */
		FN_SEL_SCIF0_0, FN_SEL_SCIF0_1, FN_SEL_SCIF0_2, FN_SEL_SCIF0_3,
		/* SEL_SCIF1 [2] */
		FN_SEL_SCIF1_0, FN_SEL_SCIF1_1, FN_SEL_SCIF1_2, 0,
		/* SEL_SCIF2 [2] */
		FN_SEL_SCIF2_0, FN_SEL_SCIF2_1, FN_SEL_SCIF2_2, 0,
		/* SEL_SCIF3 [1] */
		FN_SEL_SCIF3_0, FN_SEL_SCIF3_1,
		/* SEL_SCIF4 [3] */
		FN_SEL_SCIF4_0, FN_SEL_SCIF4_1, FN_SEL_SCIF4_2, FN_SEL_SCIF4_3,
		FN_SEL_SCIF4_4, 0, 0, 0,
		/* SEL_SCIF5 [2] */
		FN_SEL_SCIF5_0, FN_SEL_SCIF5_1, FN_SEL_SCIF5_2, FN_SEL_SCIF5_3,
		/* SEL_SSI1 [1] */
		FN_SEL_SSI1_0, FN_SEL_SSI1_1,
		/* SEL_SSI2 [1] */
		FN_SEL_SSI2_0, FN_SEL_SSI2_1,
		/* SEL_SSI4 [1] */
		FN_SEL_SSI4_0, FN_SEL_SSI4_1,
		/* SEL_SSI5 [1] */
		FN_SEL_SSI5_0, FN_SEL_SSI5_1,
		/* SEL_SSI6 [1] */
		FN_SEL_SSI6_0, FN_SEL_SSI6_1,
		/* SEL_SSI7 [1] */
		FN_SEL_SSI7_0, FN_SEL_SSI7_1,
		/* SEL_SSI8 [1] */
		FN_SEL_SSI8_0, FN_SEL_SSI8_1,
		/* SEL_SSI9 [1] */
		FN_SEL_SSI9_0, FN_SEL_SSI9_1,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0,
		/* RESEVED [1] */
		0, 0, }
	},
	{ PINMUX_CFG_REG("INOUTSEL0", 0xE6050004, 32, 1) { GP_INOUTSEL(0) } },
	{ PINMUX_CFG_REG("INOUTSEL1", 0xE6051004, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_1_25_IN, GP_1_25_OUT,
		GP_1_24_IN, GP_1_24_OUT,
		GP_1_23_IN, GP_1_23_OUT,
		GP_1_22_IN, GP_1_22_OUT,
		GP_1_21_IN, GP_1_21_OUT,
		GP_1_20_IN, GP_1_20_OUT,
		GP_1_19_IN, GP_1_19_OUT,
		GP_1_18_IN, GP_1_18_OUT,
		GP_1_17_IN, GP_1_17_OUT,
		GP_1_16_IN, GP_1_16_OUT,
		GP_1_15_IN, GP_1_15_OUT,
		GP_1_14_IN, GP_1_14_OUT,
		GP_1_13_IN, GP_1_13_OUT,
		GP_1_12_IN, GP_1_12_OUT,
		GP_1_11_IN, GP_1_11_OUT,
		GP_1_10_IN, GP_1_10_OUT,
		GP_1_9_IN, GP_1_9_OUT,
		GP_1_8_IN, GP_1_8_OUT,
		GP_1_7_IN, GP_1_7_OUT,
		GP_1_6_IN, GP_1_6_OUT,
		GP_1_5_IN, GP_1_5_OUT,
		GP_1_4_IN, GP_1_4_OUT,
		GP_1_3_IN, GP_1_3_OUT,
		GP_1_2_IN, GP_1_2_OUT,
		GP_1_1_IN, GP_1_1_OUT,
		GP_1_0_IN, GP_1_0_OUT, }
	},
	{ PINMUX_CFG_REG("INOUTSEL2", 0xE6052004, 32, 1) { GP_INOUTSEL(2) } },
	{ PINMUX_CFG_REG("INOUTSEL3", 0xE6053004, 32, 1) { GP_INOUTSEL(3) } },
	{ PINMUX_CFG_REG("INOUTSEL4", 0xE6054004, 32, 1) { GP_INOUTSEL(4) } },
	{ PINMUX_CFG_REG("INOUTSEL5", 0xE6055004, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_5_27_IN, GP_5_27_OUT,
		GP_5_26_IN, GP_5_26_OUT,
		GP_5_25_IN, GP_5_25_OUT,
		GP_5_24_IN, GP_5_24_OUT,
		GP_5_23_IN, GP_5_23_OUT,
		GP_5_22_IN, GP_5_22_OUT,
		GP_5_21_IN, GP_5_21_OUT,
		GP_5_20_IN, GP_5_20_OUT,
		GP_5_19_IN, GP_5_19_OUT,
		GP_5_18_IN, GP_5_18_OUT,
		GP_5_17_IN, GP_5_17_OUT,
		GP_5_16_IN, GP_5_16_OUT,
		GP_5_15_IN, GP_5_15_OUT,
		GP_5_14_IN, GP_5_14_OUT,
		GP_5_13_IN, GP_5_13_OUT,
		GP_5_12_IN, GP_5_12_OUT,
		GP_5_11_IN, GP_5_11_OUT,
		GP_5_10_IN, GP_5_10_OUT,
		GP_5_9_IN, GP_5_9_OUT,
		GP_5_8_IN, GP_5_8_OUT,
		GP_5_7_IN, GP_5_7_OUT,
		GP_5_6_IN, GP_5_6_OUT,
		GP_5_5_IN, GP_5_5_OUT,
		GP_5_4_IN, GP_5_4_OUT,
		GP_5_3_IN, GP_5_3_OUT,
		GP_5_2_IN, GP_5_2_OUT,
		GP_5_1_IN, GP_5_1_OUT,
		GP_5_0_IN, GP_5_0_OUT, }
	},
	{ PINMUX_CFG_REG("INOUTSEL6", 0xE6055404, 32, 1) {
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		0, 0,
		GP_6_25_IN, GP_6_25_OUT,
		GP_6_24_IN, GP_6_24_OUT,
		GP_6_23_IN, GP_6_23_OUT,
		GP_6_22_IN, GP_6_22_OUT,
		GP_6_21_IN, GP_6_21_OUT,
		GP_6_20_IN, GP_6_20_OUT,
		GP_6_19_IN, GP_6_19_OUT,
		GP_6_18_IN, GP_6_18_OUT,
		GP_6_17_IN, GP_6_17_OUT,
		GP_6_16_IN, GP_6_16_OUT,
		GP_6_15_IN, GP_6_15_OUT,
		GP_6_14_IN, GP_6_14_OUT,
		GP_6_13_IN, GP_6_13_OUT,
		GP_6_12_IN, GP_6_12_OUT,
		GP_6_11_IN, GP_6_11_OUT,
		GP_6_10_IN, GP_6_10_OUT,
		GP_6_9_IN, GP_6_9_OUT,
		GP_6_8_IN, GP_6_8_OUT,
		GP_6_7_IN, GP_6_7_OUT,
		GP_6_6_IN, GP_6_6_OUT,
		GP_6_5_IN, GP_6_5_OUT,
		GP_6_4_IN, GP_6_4_OUT,
		GP_6_3_IN, GP_6_3_OUT,
		GP_6_2_IN, GP_6_2_OUT,
		GP_6_1_IN, GP_6_1_OUT,
		GP_6_0_IN, GP_6_0_OUT, }
	},
	{ },
};

static struct pinmux_data_reg pinmux_data_regs[] = {
	{ PINMUX_DATA_REG("INDT0", 0xE6050008, 32) { GP_INDT(0) } },
	{ PINMUX_DATA_REG("INDT1", 0xE6051008, 32) {
		0, 0, 0, 0,
		0, 0, GP_1_25_DATA, GP_1_24_DATA,
		GP_1_23_DATA, GP_1_22_DATA, GP_1_21_DATA, GP_1_20_DATA,
		GP_1_19_DATA, GP_1_18_DATA, GP_1_17_DATA, GP_1_16_DATA,
		GP_1_15_DATA, GP_1_14_DATA, GP_1_13_DATA, GP_1_12_DATA,
		GP_1_11_DATA, GP_1_10_DATA, GP_1_9_DATA, GP_1_8_DATA,
		GP_1_7_DATA, GP_1_6_DATA, GP_1_5_DATA, GP_1_4_DATA,
		GP_1_3_DATA, GP_1_2_DATA, GP_1_1_DATA, GP_1_0_DATA }
	},
	{ PINMUX_DATA_REG("INDT2", 0xE6052008, 32) { GP_INDT(2) } },
	{ PINMUX_DATA_REG("INDT3", 0xE6053008, 32) { GP_INDT(3) } },
	{ PINMUX_DATA_REG("INDT4", 0xE6054008, 32) { GP_INDT(4) } },
	{ PINMUX_DATA_REG("INDT5", 0xE6055008, 32) {
		0, 0, 0, 0,
		GP_5_27_DATA, GP_5_26_DATA, GP_5_25_DATA, GP_5_24_DATA,
		GP_5_23_DATA, GP_5_22_DATA, GP_5_21_DATA, GP_5_20_DATA,
		GP_5_19_DATA, GP_5_18_DATA, GP_5_17_DATA, GP_5_16_DATA,
		GP_5_15_DATA, GP_5_14_DATA, GP_5_13_DATA, GP_5_12_DATA,
		GP_5_11_DATA, GP_5_10_DATA, GP_5_9_DATA, GP_5_8_DATA,
		GP_5_7_DATA, GP_5_6_DATA, GP_5_5_DATA, GP_5_4_DATA,
		GP_5_3_DATA, GP_5_2_DATA, GP_5_1_DATA, GP_5_0_DATA }
	},
	{ PINMUX_DATA_REG("INDT6", 0xE6055408, 32) {
		0, 0, 0, 0,
		0, 0, GP_6_25_DATA, GP_6_24_DATA,
		GP_6_23_DATA, GP_6_22_DATA, GP_6_21_DATA, GP_6_20_DATA,
		GP_6_19_DATA, GP_6_18_DATA, GP_6_17_DATA, GP_6_16_DATA,
		GP_6_15_DATA, GP_6_14_DATA, GP_6_13_DATA, GP_6_12_DATA,
		GP_6_11_DATA, GP_6_10_DATA, GP_6_9_DATA, GP_6_8_DATA,
		GP_6_7_DATA, GP_6_6_DATA, GP_6_5_DATA, GP_6_4_DATA,
		GP_6_3_DATA, GP_6_2_DATA, GP_6_1_DATA, GP_6_0_DATA }
	},
	{ },
};

static struct pinmux_info r8a7794_pinmux_info = {
	.name = "r8a7794_pfc",

	.unlock_reg = 0xe6060000, /* PMMR */

	.reserved_id = PINMUX_RESERVED,
	.data = { PINMUX_DATA_BEGIN, PINMUX_DATA_END },
	.input = { PINMUX_INPUT_BEGIN, PINMUX_INPUT_END },
	.output = { PINMUX_OUTPUT_BEGIN, PINMUX_OUTPUT_END },
	.mark = { PINMUX_MARK_BEGIN, PINMUX_MARK_END },
	.function = { PINMUX_FUNCTION_BEGIN, PINMUX_FUNCTION_END },

	.first_gpio = GPIO_GP_0_0,
	.last_gpio = GPIO_FN_AD_CLK_B,

	.gpios = pinmux_gpios,
	.cfg_regs = pinmux_config_regs,
	.data_regs = pinmux_data_regs,

	.gpio_data = pinmux_data,
	.gpio_data_size = ARRAY_SIZE(pinmux_data),
};

void r8a7794_pinmux_init(void)
{
	register_pinmux(&r8a7794_pinmux_info);
}
