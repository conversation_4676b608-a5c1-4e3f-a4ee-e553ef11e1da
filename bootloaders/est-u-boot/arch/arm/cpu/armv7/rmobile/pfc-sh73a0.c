/*
 * sh73a0 processor support - PFC hardware block
 *
 * Copyright (C) 2010 Renesas Solutions Corp.
 * Copyright (C) 2010 NISHIMOTO Hiroki
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License as
 * published by the Free Software Foundation; version 2 of the
 * License.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA
 */

#include <common.h>
#include <sh_pfc.h>
#include <asm/arch/sh73a0-gpio.h>

#define CPU_ALL_PORT(fn, pfx, sfx)				\
	PORT_10(fn, pfx,    sfx), PORT_10(fn, pfx##1, sfx),	\
	PORT_10(fn, pfx##2, sfx), PORT_10(fn, pfx##3, sfx),	\
	PORT_10(fn, pfx##4, sfx), PORT_10(fn, pfx##5, sfx),	\
	PORT_10(fn, pfx##6, sfx), PORT_10(fn, pfx##7, sfx),	\
	PORT_10(fn, pfx##8, sfx), PORT_10(fn, pfx##9, sfx),	\
	PORT_10(fn, pfx##10, sfx),				\
	PORT_1(fn, pfx##110, sfx), PORT_1(fn, pfx##111, sfx),	\
	PORT_1(fn, pfx##112, sfx), PORT_1(fn, pfx##113, sfx),	\
	PORT_1(fn, pfx##114, sfx), PORT_1(fn, pfx##115, sfx),	\
	PORT_1(fn, pfx##116, sfx), PORT_1(fn, pfx##117, sfx),	\
	PORT_1(fn, pfx##118, sfx),				\
	PORT_1(fn, pfx##128, sfx), PORT_1(fn, pfx##129, sfx),	\
	PORT_10(fn, pfx##13, sfx), PORT_10(fn, pfx##14, sfx),	\
	PORT_10(fn, pfx##15, sfx),				\
	PORT_1(fn, pfx##160, sfx), PORT_1(fn, pfx##161, sfx),	\
	PORT_1(fn, pfx##162, sfx), PORT_1(fn, pfx##163, sfx),	\
	PORT_1(fn, pfx##164, sfx),				\
	PORT_1(fn, pfx##192, sfx), PORT_1(fn, pfx##193, sfx),	\
	PORT_1(fn, pfx##194, sfx), PORT_1(fn, pfx##195, sfx),	\
	PORT_1(fn, pfx##196, sfx), PORT_1(fn, pfx##197, sfx),	\
	PORT_1(fn, pfx##198, sfx), PORT_1(fn, pfx##199, sfx),	\
	PORT_10(fn, pfx##20, sfx), PORT_10(fn, pfx##21, sfx),	\
	PORT_10(fn, pfx##22, sfx), PORT_10(fn, pfx##23, sfx),	\
	PORT_10(fn, pfx##24, sfx), PORT_10(fn, pfx##25, sfx),	\
	PORT_10(fn, pfx##26, sfx), PORT_10(fn, pfx##27, sfx),	\
	PORT_1(fn, pfx##280, sfx), PORT_1(fn, pfx##281, sfx),	\
	PORT_1(fn, pfx##282, sfx),				\
	PORT_1(fn, pfx##288, sfx), PORT_1(fn, pfx##289, sfx),	\
	PORT_10(fn, pfx##29, sfx), PORT_10(fn, pfx##30, sfx)

enum {
	PINMUX_RESERVED = 0,

	PINMUX_DATA_BEGIN,
	PORT_ALL(DATA),			/* PORT0_DATA -> PORT309_DATA */
	PINMUX_DATA_END,

	PINMUX_INPUT_BEGIN,
	PORT_ALL(IN),			/* PORT0_IN -> PORT309_IN */
	PINMUX_INPUT_END,

	PINMUX_INPUT_PULLUP_BEGIN,
	PORT_ALL(IN_PU),		/* PORT0_IN_PU -> PORT309_IN_PU */
	PINMUX_INPUT_PULLUP_END,

	PINMUX_INPUT_PULLDOWN_BEGIN,
	PORT_ALL(IN_PD),		/* PORT0_IN_PD -> PORT309_IN_PD */
	PINMUX_INPUT_PULLDOWN_END,

	PINMUX_OUTPUT_BEGIN,
	PORT_ALL(OUT),			/* PORT0_OUT -> PORT309_OUT */
	PINMUX_OUTPUT_END,

	PINMUX_FUNCTION_BEGIN,
	PORT_ALL(FN_IN),		/* PORT0_FN_IN -> PORT309_FN_IN */
	PORT_ALL(FN_OUT),		/* PORT0_FN_OUT -> PORT309_FN_OUT */
	PORT_ALL(FN0),			/* PORT0_FN0 -> PORT309_FN0 */
	PORT_ALL(FN1),			/* PORT0_FN1 -> PORT309_FN1 */
	PORT_ALL(FN2),			/* PORT0_FN2 -> PORT309_FN2 */
	PORT_ALL(FN3),			/* PORT0_FN3 -> PORT309_FN3 */
	PORT_ALL(FN4),			/* PORT0_FN4 -> PORT309_FN4 */
	PORT_ALL(FN5),			/* PORT0_FN5 -> PORT309_FN5 */
	PORT_ALL(FN6),			/* PORT0_FN6 -> PORT309_FN6 */
	PORT_ALL(FN7),			/* PORT0_FN7 -> PORT309_FN7 */

	MSEL2CR_MSEL19_0, MSEL2CR_MSEL19_1,
	MSEL2CR_MSEL18_0, MSEL2CR_MSEL18_1,
	MSEL2CR_MSEL17_0, MSEL2CR_MSEL17_1,
	MSEL2CR_MSEL16_0, MSEL2CR_MSEL16_1,
	MSEL2CR_MSEL14_0, MSEL2CR_MSEL14_1,
	MSEL2CR_MSEL13_0, MSEL2CR_MSEL13_1,
	MSEL2CR_MSEL12_0, MSEL2CR_MSEL12_1,
	MSEL2CR_MSEL11_0, MSEL2CR_MSEL11_1,
	MSEL2CR_MSEL10_0, MSEL2CR_MSEL10_1,
	MSEL2CR_MSEL9_0, MSEL2CR_MSEL9_1,
	MSEL2CR_MSEL8_0, MSEL2CR_MSEL8_1,
	MSEL2CR_MSEL7_0, MSEL2CR_MSEL7_1,
	MSEL2CR_MSEL6_0, MSEL2CR_MSEL6_1,
	MSEL2CR_MSEL4_0, MSEL2CR_MSEL4_1,
	MSEL2CR_MSEL5_0, MSEL2CR_MSEL5_1,
	MSEL2CR_MSEL3_0, MSEL2CR_MSEL3_1,
	MSEL2CR_MSEL2_0, MSEL2CR_MSEL2_1,
	MSEL2CR_MSEL1_0, MSEL2CR_MSEL1_1,
	MSEL2CR_MSEL0_0, MSEL2CR_MSEL0_1,
	MSEL3CR_MSEL28_0, MSEL3CR_MSEL28_1,
	MSEL3CR_MSEL15_0, MSEL3CR_MSEL15_1,
	MSEL3CR_MSEL11_0, MSEL3CR_MSEL11_1,
	MSEL3CR_MSEL9_0, MSEL3CR_MSEL9_1,
	MSEL3CR_MSEL6_0, MSEL3CR_MSEL6_1,
	MSEL3CR_MSEL2_0, MSEL3CR_MSEL2_1,
	MSEL4CR_MSEL29_0, MSEL4CR_MSEL29_1,
	MSEL4CR_MSEL27_0, MSEL4CR_MSEL27_1,
	MSEL4CR_MSEL26_0, MSEL4CR_MSEL26_1,
	MSEL4CR_MSEL22_0, MSEL4CR_MSEL22_1,
	MSEL4CR_MSEL21_0, MSEL4CR_MSEL21_1,
	MSEL4CR_MSEL20_0, MSEL4CR_MSEL20_1,
	MSEL4CR_MSEL19_0, MSEL4CR_MSEL19_1,
	MSEL4CR_MSEL15_0, MSEL4CR_MSEL15_1,
	MSEL4CR_MSEL13_0, MSEL4CR_MSEL13_1,
	MSEL4CR_MSEL12_0, MSEL4CR_MSEL12_1,
	MSEL4CR_MSEL11_0, MSEL4CR_MSEL11_1,
	MSEL4CR_MSEL10_0, MSEL4CR_MSEL10_1,
	MSEL4CR_MSEL9_0, MSEL4CR_MSEL9_1,
	MSEL4CR_MSEL8_0, MSEL4CR_MSEL8_1,
	MSEL4CR_MSEL7_0, MSEL4CR_MSEL7_1,
	MSEL4CR_MSEL4_0, MSEL4CR_MSEL4_1,
	MSEL4CR_MSEL1_0, MSEL4CR_MSEL1_1,
	PINMUX_FUNCTION_END,

	PINMUX_MARK_BEGIN,
	/* Hardware manual Table 25-1 (Function 0-7) */
	VBUS_0_MARK,
	GPI0_MARK,
	GPI1_MARK,
	GPI2_MARK,
	GPI3_MARK,
	GPI4_MARK,
	GPI5_MARK,
	GPI6_MARK,
	GPI7_MARK,
	SCIFA7_RXD_MARK,
	SCIFA7_CTS__MARK,
	GPO7_MARK, MFG0_OUT2_MARK,
	GPO6_MARK, MFG1_OUT2_MARK,
	GPO5_MARK, SCIFA0_SCK_MARK, FSICOSLDT3_MARK, PORT16_VIO_CKOR_MARK,
	SCIFA0_TXD_MARK,
	SCIFA7_TXD_MARK,
	SCIFA7_RTS__MARK, PORT19_VIO_CKO2_MARK,
	GPO0_MARK,
	GPO1_MARK,
	GPO2_MARK, STATUS0_MARK,
	GPO3_MARK, STATUS1_MARK,
	GPO4_MARK, STATUS2_MARK,
	VINT_MARK,
	TCKON_MARK,
	XDVFS1_MARK, PORT27_I2C_SCL2_MARK, PORT27_I2C_SCL3_MARK, \
	MFG0_OUT1_MARK, PORT27_IROUT_MARK,
	XDVFS2_MARK, PORT28_I2C_SDA2_MARK, PORT28_I2C_SDA3_MARK, \
	PORT28_TPU1TO1_MARK,
	SIM_RST_MARK, PORT29_TPU1TO1_MARK,
	SIM_CLK_MARK, PORT30_VIO_CKOR_MARK,
	SIM_D_MARK, PORT31_IROUT_MARK,
	SCIFA4_TXD_MARK,
	SCIFA4_RXD_MARK, XWUP_MARK,
	SCIFA4_RTS__MARK,
	SCIFA4_CTS__MARK,
	FSIBOBT_MARK, FSIBIBT_MARK,
	FSIBOLR_MARK, FSIBILR_MARK,
	FSIBOSLD_MARK,
	FSIBISLD_MARK,
	VACK_MARK,
	XTAL1L_MARK,
	SCIFA0_RTS__MARK, FSICOSLDT2_MARK,
	SCIFA0_RXD_MARK,
	SCIFA0_CTS__MARK, FSICOSLDT1_MARK,
	FSICOBT_MARK, FSICIBT_MARK, FSIDOBT_MARK, FSIDIBT_MARK,
	FSICOLR_MARK, FSICILR_MARK, FSIDOLR_MARK, FSIDILR_MARK,
	FSICOSLD_MARK, PORT47_FSICSPDIF_MARK,
	FSICISLD_MARK, FSIDISLD_MARK,
	FSIACK_MARK, PORT49_IRDA_OUT_MARK, PORT49_IROUT_MARK, FSIAOMC_MARK,
	FSIAOLR_MARK, BBIF2_TSYNC2_MARK, TPU2TO2_MARK, FSIAILR_MARK,

	FSIAOBT_MARK, BBIF2_TSCK2_MARK, TPU2TO3_MARK, FSIAIBT_MARK,
	FSIAOSLD_MARK, BBIF2_TXD2_MARK,
	FSIASPDIF_MARK, PORT53_IRDA_IN_MARK, TPU3TO3_MARK, FSIBSPDIF_MARK, \
	PORT53_FSICSPDIF_MARK,
	FSIBCK_MARK, PORT54_IRDA_FIRSEL_MARK, TPU3TO2_MARK, FSIBOMC_MARK, \
	FSICCK_MARK, FSICOMC_MARK,
	FSIAISLD_MARK, TPU0TO0_MARK,
	A0_MARK, BS__MARK,
	A12_MARK, PORT58_KEYOUT7_MARK, TPU4TO2_MARK,
	A13_MARK, PORT59_KEYOUT6_MARK, TPU0TO1_MARK,
	A14_MARK, KEYOUT5_MARK,
	A15_MARK, KEYOUT4_MARK,
	A16_MARK, KEYOUT3_MARK, MSIOF0_SS1_MARK,
	A17_MARK, KEYOUT2_MARK, MSIOF0_TSYNC_MARK,
	A18_MARK, KEYOUT1_MARK, MSIOF0_TSCK_MARK,
	A19_MARK, KEYOUT0_MARK, MSIOF0_TXD_MARK,
	A20_MARK, KEYIN0_MARK, MSIOF0_RSCK_MARK,
	A21_MARK, KEYIN1_MARK, MSIOF0_RSYNC_MARK,
	A22_MARK, KEYIN2_MARK, MSIOF0_MCK0_MARK,
	A23_MARK, KEYIN3_MARK, MSIOF0_MCK1_MARK,
	A24_MARK, KEYIN4_MARK, MSIOF0_RXD_MARK,
	A25_MARK, KEYIN5_MARK, MSIOF0_SS2_MARK,
	A26_MARK, KEYIN6_MARK,
	KEYIN7_MARK,
	D0_NAF0_MARK,
	D1_NAF1_MARK,
	D2_NAF2_MARK,
	D3_NAF3_MARK,
	D4_NAF4_MARK,
	D5_NAF5_MARK,
	D6_NAF6_MARK,
	D7_NAF7_MARK,
	D8_NAF8_MARK,
	D9_NAF9_MARK,
	D10_NAF10_MARK,
	D11_NAF11_MARK,
	D12_NAF12_MARK,
	D13_NAF13_MARK,
	D14_NAF14_MARK,
	D15_NAF15_MARK,
	CS4__MARK,
	CS5A__MARK, PORT91_RDWR_MARK,
	CS5B__MARK, FCE1__MARK,
	CS6B__MARK, DACK0_MARK,
	FCE0__MARK, CS6A__MARK,
	WAIT__MARK, DREQ0_MARK,
	RD__FSC_MARK,
	WE0__FWE_MARK, RDWR_FWE_MARK,
	WE1__MARK,
	FRB_MARK,
	CKO_MARK,
	NBRSTOUT__MARK,
	NBRST__MARK,
	BBIF2_TXD_MARK,
	BBIF2_RXD_MARK,
	BBIF2_SYNC_MARK,
	BBIF2_SCK_MARK,
	SCIFA3_CTS__MARK, MFG3_IN2_MARK,
	SCIFA3_RXD_MARK, MFG3_IN1_MARK,
	BBIF1_SS2_MARK, SCIFA3_RTS__MARK, MFG3_OUT1_MARK,
	SCIFA3_TXD_MARK,
	HSI_RX_DATA_MARK, BBIF1_RXD_MARK,
	HSI_TX_WAKE_MARK, BBIF1_TSCK_MARK,
	HSI_TX_DATA_MARK, BBIF1_TSYNC_MARK,
	HSI_TX_READY_MARK, BBIF1_TXD_MARK,
	HSI_RX_READY_MARK, BBIF1_RSCK_MARK, PORT115_I2C_SCL2_MARK, \
	PORT115_I2C_SCL3_MARK,
	HSI_RX_WAKE_MARK, BBIF1_RSYNC_MARK, PORT116_I2C_SDA2_MARK, \
	PORT116_I2C_SDA3_MARK,
	HSI_RX_FLAG_MARK, BBIF1_SS1_MARK, BBIF1_FLOW_MARK,
	HSI_TX_FLAG_MARK,
	VIO_VD_MARK, PORT128_LCD2VSYN_MARK, VIO2_VD_MARK, LCD2D0_MARK,

	VIO_HD_MARK, PORT129_LCD2HSYN_MARK, PORT129_LCD2CS__MARK, \
	VIO2_HD_MARK, LCD2D1_MARK,
	VIO_D0_MARK, PORT130_MSIOF2_RXD_MARK, LCD2D10_MARK,
	VIO_D1_MARK, PORT131_KEYOUT6_MARK, PORT131_MSIOF2_SS1_MARK, \
	PORT131_KEYOUT11_MARK, LCD2D11_MARK,
	VIO_D2_MARK, PORT132_KEYOUT7_MARK, PORT132_MSIOF2_SS2_MARK, \
	PORT132_KEYOUT10_MARK, LCD2D12_MARK,
	VIO_D3_MARK, MSIOF2_TSYNC_MARK, LCD2D13_MARK,
	VIO_D4_MARK, MSIOF2_TXD_MARK, LCD2D14_MARK,
	VIO_D5_MARK, MSIOF2_TSCK_MARK, LCD2D15_MARK,
	VIO_D6_MARK, PORT136_KEYOUT8_MARK, LCD2D16_MARK,
	VIO_D7_MARK, PORT137_KEYOUT9_MARK, LCD2D17_MARK,
	VIO_D8_MARK, PORT138_KEYOUT8_MARK, VIO2_D0_MARK, LCD2D6_MARK,
	VIO_D9_MARK, PORT139_KEYOUT9_MARK, VIO2_D1_MARK, LCD2D7_MARK,
	VIO_D10_MARK, TPU0TO2_MARK, VIO2_D2_MARK, LCD2D8_MARK,
	VIO_D11_MARK, TPU0TO3_MARK, VIO2_D3_MARK, LCD2D9_MARK,
	VIO_D12_MARK, PORT142_KEYOUT10_MARK, VIO2_D4_MARK, LCD2D2_MARK,
	VIO_D13_MARK, PORT143_KEYOUT11_MARK, PORT143_KEYOUT6_MARK, \
	VIO2_D5_MARK, LCD2D3_MARK,
	VIO_D14_MARK, PORT144_KEYOUT7_MARK, VIO2_D6_MARK, LCD2D4_MARK,
	VIO_D15_MARK, TPU1TO3_MARK, PORT145_LCD2DISP_MARK, \
	PORT145_LCD2RS_MARK, VIO2_D7_MARK, LCD2D5_MARK,
	VIO_CLK_MARK, LCD2DCK_MARK, PORT146_LCD2WR__MARK, VIO2_CLK_MARK, \
	LCD2D18_MARK,
	VIO_FIELD_MARK, LCD2RD__MARK, VIO2_FIELD_MARK, LCD2D19_MARK,
	VIO_CKO_MARK,
	A27_MARK, PORT149_RDWR_MARK, MFG0_IN1_MARK, PORT149_KEYOUT9_MARK,
	MFG0_IN2_MARK,
	TS_SPSYNC3_MARK, MSIOF2_RSCK_MARK,
	TS_SDAT3_MARK, MSIOF2_RSYNC_MARK,
	TPU1TO2_MARK, TS_SDEN3_MARK, PORT153_MSIOF2_SS1_MARK,
	SCIFA2_TXD1_MARK, MSIOF2_MCK0_MARK,
	SCIFA2_RXD1_MARK, MSIOF2_MCK1_MARK,
	SCIFA2_RTS1__MARK, PORT156_MSIOF2_SS2_MARK,
	SCIFA2_CTS1__MARK, PORT157_MSIOF2_RXD_MARK,
	DINT__MARK, SCIFA2_SCK1_MARK, TS_SCK3_MARK,
	PORT159_SCIFB_SCK_MARK, PORT159_SCIFA5_SCK_MARK, NMI_MARK,
	PORT160_SCIFB_TXD_MARK, PORT160_SCIFA5_TXD_MARK,
	PORT161_SCIFB_CTS__MARK, PORT161_SCIFA5_CTS__MARK,
	PORT162_SCIFB_RXD_MARK, PORT162_SCIFA5_RXD_MARK,
	PORT163_SCIFB_RTS__MARK, PORT163_SCIFA5_RTS__MARK, TPU3TO0_MARK,
	LCDD0_MARK,
	LCDD1_MARK, PORT193_SCIFA5_CTS__MARK, BBIF2_TSYNC1_MARK,
	LCDD2_MARK, PORT194_SCIFA5_RTS__MARK, BBIF2_TSCK1_MARK,
	LCDD3_MARK, PORT195_SCIFA5_RXD_MARK, BBIF2_TXD1_MARK,
	LCDD4_MARK, PORT196_SCIFA5_TXD_MARK,
	LCDD5_MARK, PORT197_SCIFA5_SCK_MARK, MFG2_OUT2_MARK, TPU2TO1_MARK,
	LCDD6_MARK,
	LCDD7_MARK, TPU4TO1_MARK, MFG4_OUT2_MARK,
	LCDD8_MARK, D16_MARK,
	LCDD9_MARK, D17_MARK,
	LCDD10_MARK, D18_MARK,
	LCDD11_MARK, D19_MARK,
	LCDD12_MARK, D20_MARK,
	LCDD13_MARK, D21_MARK,
	LCDD14_MARK, D22_MARK,
	LCDD15_MARK, PORT207_MSIOF0L_SS1_MARK, D23_MARK,
	LCDD16_MARK, PORT208_MSIOF0L_SS2_MARK, D24_MARK,
	LCDD17_MARK, D25_MARK,
	LCDD18_MARK, DREQ2_MARK, PORT210_MSIOF0L_SS1_MARK, D26_MARK,
	LCDD19_MARK, PORT211_MSIOF0L_SS2_MARK, D27_MARK,
	LCDD20_MARK, TS_SPSYNC1_MARK, MSIOF0L_MCK0_MARK, D28_MARK,
	LCDD21_MARK, TS_SDAT1_MARK, MSIOF0L_MCK1_MARK, D29_MARK,
	LCDD22_MARK, TS_SDEN1_MARK, MSIOF0L_RSCK_MARK, D30_MARK,
	LCDD23_MARK, TS_SCK1_MARK, MSIOF0L_RSYNC_MARK, D31_MARK,
	LCDDCK_MARK, LCDWR__MARK,
	LCDRD__MARK, DACK2_MARK, PORT217_LCD2RS_MARK, MSIOF0L_TSYNC_MARK, \
	VIO2_FIELD3_MARK, PORT217_LCD2DISP_MARK,
	LCDHSYN_MARK, LCDCS__MARK, LCDCS2__MARK, DACK3_MARK, \
	PORT218_VIO_CKOR_MARK,
	LCDDISP_MARK, LCDRS_MARK, PORT219_LCD2WR__MARK, DREQ3_MARK, \
	MSIOF0L_TSCK_MARK, VIO2_CLK3_MARK, LCD2DCK_2_MARK,
	LCDVSYN_MARK, LCDVSYN2_MARK,
	LCDLCLK_MARK, DREQ1_MARK, PORT221_LCD2CS__MARK, PWEN_MARK, \
	MSIOF0L_RXD_MARK, VIO2_HD3_MARK, PORT221_LCD2HSYN_MARK,
	LCDDON_MARK, LCDDON2_MARK, DACK1_MARK, OVCN_MARK, MSIOF0L_TXD_MARK, \
	VIO2_VD3_MARK, PORT222_LCD2VSYN_MARK,

	SCIFA1_TXD_MARK, OVCN2_MARK,
	EXTLP_MARK, SCIFA1_SCK_MARK, PORT226_VIO_CKO2_MARK,
	SCIFA1_RTS__MARK, IDIN_MARK,
	SCIFA1_RXD_MARK,
	SCIFA1_CTS__MARK, MFG1_IN1_MARK,
	MSIOF1_TXD_MARK, SCIFA2_TXD2_MARK,
	MSIOF1_TSYNC_MARK, SCIFA2_CTS2__MARK,
	MSIOF1_TSCK_MARK, SCIFA2_SCK2_MARK,
	MSIOF1_RXD_MARK, SCIFA2_RXD2_MARK,
	MSIOF1_RSCK_MARK, SCIFA2_RTS2__MARK, VIO2_CLK2_MARK, LCD2D20_MARK,
	MSIOF1_RSYNC_MARK, MFG1_IN2_MARK, VIO2_VD2_MARK, LCD2D21_MARK,
	MSIOF1_MCK0_MARK, PORT236_I2C_SDA2_MARK,
	MSIOF1_MCK1_MARK, PORT237_I2C_SCL2_MARK,
	MSIOF1_SS1_MARK, VIO2_FIELD2_MARK, LCD2D22_MARK,
	MSIOF1_SS2_MARK, VIO2_HD2_MARK, LCD2D23_MARK,
	SCIFA6_TXD_MARK,
	PORT241_IRDA_OUT_MARK, PORT241_IROUT_MARK, MFG4_OUT1_MARK, TPU4TO0_MARK,
	PORT242_IRDA_IN_MARK, MFG4_IN2_MARK,
	PORT243_IRDA_FIRSEL_MARK, PORT243_VIO_CKO2_MARK,
	PORT244_SCIFA5_CTS__MARK, MFG2_IN1_MARK, PORT244_SCIFB_CTS__MARK, \
	MSIOF2R_RXD_MARK,
	PORT245_SCIFA5_RTS__MARK, MFG2_IN2_MARK, PORT245_SCIFB_RTS__MARK, \
	MSIOF2R_TXD_MARK,
	PORT246_SCIFA5_RXD_MARK, MFG1_OUT1_MARK, PORT246_SCIFB_RXD_MARK, \
	TPU1TO0_MARK,
	PORT247_SCIFA5_TXD_MARK, MFG3_OUT2_MARK, PORT247_SCIFB_TXD_MARK, \
	TPU3TO1_MARK,
	PORT248_SCIFA5_SCK_MARK, MFG2_OUT1_MARK, PORT248_SCIFB_SCK_MARK, \
	TPU2TO0_MARK, PORT248_I2C_SCL3_MARK, MSIOF2R_TSCK_MARK,
	PORT249_IROUT_MARK, MFG4_IN1_MARK, PORT249_I2C_SDA3_MARK, \
	MSIOF2R_TSYNC_MARK,
	SDHICLK0_MARK,
	SDHICD0_MARK,
	SDHID0_0_MARK,
	SDHID0_1_MARK,
	SDHID0_2_MARK,
	SDHID0_3_MARK,
	SDHICMD0_MARK,
	SDHIWP0_MARK,
	SDHICLK1_MARK,
	SDHID1_0_MARK, TS_SPSYNC2_MARK,
	SDHID1_1_MARK, TS_SDAT2_MARK,
	SDHID1_2_MARK, TS_SDEN2_MARK,
	SDHID1_3_MARK, TS_SCK2_MARK,
	SDHICMD1_MARK,
	SDHICLK2_MARK,
	SDHID2_0_MARK, TS_SPSYNC4_MARK,
	SDHID2_1_MARK, TS_SDAT4_MARK,
	SDHID2_2_MARK, TS_SDEN4_MARK,
	SDHID2_3_MARK, TS_SCK4_MARK,
	SDHICMD2_MARK,
	MMCCLK0_MARK,
	MMCD0_0_MARK,
	MMCD0_1_MARK,
	MMCD0_2_MARK,
	MMCD0_3_MARK,
	MMCD0_4_MARK, TS_SPSYNC5_MARK,
	MMCD0_5_MARK, TS_SDAT5_MARK,
	MMCD0_6_MARK, TS_SDEN5_MARK,
	MMCD0_7_MARK, TS_SCK5_MARK,
	MMCCMD0_MARK,
	RESETOUTS__MARK, EXTAL2OUT_MARK,
	MCP_WAIT__MCP_FRB_MARK,
	MCP_CKO_MARK, MMCCLK1_MARK,
	MCP_D15_MCP_NAF15_MARK,
	MCP_D14_MCP_NAF14_MARK,
	MCP_D13_MCP_NAF13_MARK,
	MCP_D12_MCP_NAF12_MARK,
	MCP_D11_MCP_NAF11_MARK,
	MCP_D10_MCP_NAF10_MARK,
	MCP_D9_MCP_NAF9_MARK,
	MCP_D8_MCP_NAF8_MARK, MMCCMD1_MARK,
	MCP_D7_MCP_NAF7_MARK, MMCD1_7_MARK,

	MCP_D6_MCP_NAF6_MARK, MMCD1_6_MARK,
	MCP_D5_MCP_NAF5_MARK, MMCD1_5_MARK,
	MCP_D4_MCP_NAF4_MARK, MMCD1_4_MARK,
	MCP_D3_MCP_NAF3_MARK, MMCD1_3_MARK,
	MCP_D2_MCP_NAF2_MARK, MMCD1_2_MARK,
	MCP_D1_MCP_NAF1_MARK, MMCD1_1_MARK,
	MCP_D0_MCP_NAF0_MARK, MMCD1_0_MARK,
	MCP_NBRSTOUT__MARK,
	MCP_WE0__MCP_FWE_MARK, MCP_RDWR_MCP_FWE_MARK,

	/* MSEL2 special cases */
	TSIF2_TS_XX1_MARK,
	TSIF2_TS_XX2_MARK,
	TSIF2_TS_XX3_MARK,
	TSIF2_TS_XX4_MARK,
	TSIF2_TS_XX5_MARK,
	TSIF1_TS_XX1_MARK,
	TSIF1_TS_XX2_MARK,
	TSIF1_TS_XX3_MARK,
	TSIF1_TS_XX4_MARK,
	TSIF1_TS_XX5_MARK,
	TSIF0_TS_XX1_MARK,
	TSIF0_TS_XX2_MARK,
	TSIF0_TS_XX3_MARK,
	TSIF0_TS_XX4_MARK,
	TSIF0_TS_XX5_MARK,
	MST1_TS_XX1_MARK,
	MST1_TS_XX2_MARK,
	MST1_TS_XX3_MARK,
	MST1_TS_XX4_MARK,
	MST1_TS_XX5_MARK,
	MST0_TS_XX1_MARK,
	MST0_TS_XX2_MARK,
	MST0_TS_XX3_MARK,
	MST0_TS_XX4_MARK,
	MST0_TS_XX5_MARK,

	/* MSEL3 special cases */
	SDHI0_VCCQ_MC0_ON_MARK,
	SDHI0_VCCQ_MC0_OFF_MARK,
	DEBUG_MON_VIO_MARK,
	DEBUG_MON_LCDD_MARK,
	LCDC_LCDC0_MARK,
	LCDC_LCDC1_MARK,

	/* MSEL4 special cases */
	IRQ9_MEM_INT_MARK,
	IRQ9_MCP_INT_MARK,
	A11_MARK,
	KEYOUT8_MARK,
	TPU4TO3_MARK,
	RESETA_N_PU_ON_MARK,
	RESETA_N_PU_OFF_MARK,
	EDBGREQ_PD_MARK,
	EDBGREQ_PU_MARK,

	/* Functions with pull-ups */
	KEYIN0_PU_MARK,
	KEYIN1_PU_MARK,
	KEYIN2_PU_MARK,
	KEYIN3_PU_MARK,
	KEYIN4_PU_MARK,
	KEYIN5_PU_MARK,
	KEYIN6_PU_MARK,
	KEYIN7_PU_MARK,
	SDHICD0_PU_MARK,
	SDHID0_0_PU_MARK,
	SDHID0_1_PU_MARK,
	SDHID0_2_PU_MARK,
	SDHID0_3_PU_MARK,
	SDHICMD0_PU_MARK,
	SDHIWP0_PU_MARK,
	SDHID1_0_PU_MARK,
	SDHID1_1_PU_MARK,
	SDHID1_2_PU_MARK,
	SDHID1_3_PU_MARK,
	SDHICMD1_PU_MARK,
	SDHID2_0_PU_MARK,
	SDHID2_1_PU_MARK,
	SDHID2_2_PU_MARK,
	SDHID2_3_PU_MARK,
	SDHICMD2_PU_MARK,
	MMCCMD0_PU_MARK,
	MMCCMD1_PU_MARK,
	MMCD0_0_PU_MARK,
	MMCD0_1_PU_MARK,
	MMCD0_2_PU_MARK,
	MMCD0_3_PU_MARK,
	MMCD0_4_PU_MARK,
	MMCD0_5_PU_MARK,
	MMCD0_6_PU_MARK,
	MMCD0_7_PU_MARK,
	FSIBISLD_PU_MARK,
	FSIACK_PU_MARK,
	FSIAILR_PU_MARK,
	FSIAIBT_PU_MARK,
	FSIAISLD_PU_MARK,

	PINMUX_MARK_END,
};

static unsigned short pinmux_data[] = {
	/* specify valid pin states for each pin in GPIO mode */

	/* Table 25-1 (I/O and Pull U/D) */
	PORT_DATA_I_PD(0),
	PORT_DATA_I_PU(1),
	PORT_DATA_I_PU(2),
	PORT_DATA_I_PU(3),
	PORT_DATA_I_PU(4),
	PORT_DATA_I_PU(5),
	PORT_DATA_I_PU(6),
	PORT_DATA_I_PU(7),
	PORT_DATA_I_PU(8),
	PORT_DATA_I_PD(9),
	PORT_DATA_I_PD(10),
	PORT_DATA_I_PU_PD(11),
	PORT_DATA_IO_PU_PD(12),
	PORT_DATA_IO_PU_PD(13),
	PORT_DATA_IO_PU_PD(14),
	PORT_DATA_IO_PU_PD(15),
	PORT_DATA_IO_PD(16),
	PORT_DATA_IO_PD(17),
	PORT_DATA_IO_PU(18),
	PORT_DATA_IO_PU(19),
	PORT_DATA_O(20),
	PORT_DATA_O(21),
	PORT_DATA_O(22),
	PORT_DATA_O(23),
	PORT_DATA_O(24),
	PORT_DATA_I_PD(25),
	PORT_DATA_I_PD(26),
	PORT_DATA_IO_PU(27),
	PORT_DATA_IO_PU(28),
	PORT_DATA_IO_PD(29),
	PORT_DATA_IO_PD(30),
	PORT_DATA_IO_PU(31),
	PORT_DATA_IO_PD(32),
	PORT_DATA_I_PU_PD(33),
	PORT_DATA_IO_PD(34),
	PORT_DATA_I_PU_PD(35),
	PORT_DATA_IO_PD(36),
	PORT_DATA_IO(37),
	PORT_DATA_O(38),
	PORT_DATA_I_PU(39),
	PORT_DATA_I_PU_PD(40),
	PORT_DATA_O(41),
	PORT_DATA_IO_PD(42),
	PORT_DATA_IO_PU_PD(43),
	PORT_DATA_IO_PU_PD(44),
	PORT_DATA_IO_PD(45),
	PORT_DATA_IO_PD(46),
	PORT_DATA_IO_PD(47),
	PORT_DATA_I_PD(48),
	PORT_DATA_IO_PU_PD(49),
	PORT_DATA_IO_PD(50),

	PORT_DATA_IO_PD(51),
	PORT_DATA_O(52),
	PORT_DATA_IO_PU_PD(53),
	PORT_DATA_IO_PU_PD(54),
	PORT_DATA_IO_PD(55),
	PORT_DATA_I_PU_PD(56),
	PORT_DATA_IO(57),
	PORT_DATA_IO(58),
	PORT_DATA_IO(59),
	PORT_DATA_IO(60),
	PORT_DATA_IO(61),
	PORT_DATA_IO_PD(62),
	PORT_DATA_IO_PD(63),
	PORT_DATA_IO_PU_PD(64),
	PORT_DATA_IO_PD(65),
	PORT_DATA_IO_PU_PD(66),
	PORT_DATA_IO_PU_PD(67),
	PORT_DATA_IO_PU_PD(68),
	PORT_DATA_IO_PU_PD(69),
	PORT_DATA_IO_PU_PD(70),
	PORT_DATA_IO_PU_PD(71),
	PORT_DATA_IO_PU_PD(72),
	PORT_DATA_I_PU_PD(73),
	PORT_DATA_IO_PU(74),
	PORT_DATA_IO_PU(75),
	PORT_DATA_IO_PU(76),
	PORT_DATA_IO_PU(77),
	PORT_DATA_IO_PU(78),
	PORT_DATA_IO_PU(79),
	PORT_DATA_IO_PU(80),
	PORT_DATA_IO_PU(81),
	PORT_DATA_IO_PU(82),
	PORT_DATA_IO_PU(83),
	PORT_DATA_IO_PU(84),
	PORT_DATA_IO_PU(85),
	PORT_DATA_IO_PU(86),
	PORT_DATA_IO_PU(87),
	PORT_DATA_IO_PU(88),
	PORT_DATA_IO_PU(89),
	PORT_DATA_O(90),
	PORT_DATA_IO_PU(91),
	PORT_DATA_O(92),
	PORT_DATA_IO_PU(93),
	PORT_DATA_O(94),
	PORT_DATA_I_PU_PD(95),
	PORT_DATA_IO(96),
	PORT_DATA_IO(97),
	PORT_DATA_IO(98),
	PORT_DATA_I_PU(99),
	PORT_DATA_O(100),
	PORT_DATA_O(101),
	PORT_DATA_I_PU(102),
	PORT_DATA_IO_PD(103),
	PORT_DATA_I_PU_PD(104),
	PORT_DATA_I_PD(105),
	PORT_DATA_I_PD(106),
	PORT_DATA_I_PU_PD(107),
	PORT_DATA_I_PU_PD(108),
	PORT_DATA_IO_PD(109),
	PORT_DATA_IO_PD(110),
	PORT_DATA_IO_PU_PD(111),
	PORT_DATA_IO_PU_PD(112),
	PORT_DATA_IO_PU_PD(113),
	PORT_DATA_IO_PD(114),
	PORT_DATA_IO_PU(115),
	PORT_DATA_IO_PU(116),
	PORT_DATA_IO_PU_PD(117),
	PORT_DATA_IO_PU_PD(118),
	PORT_DATA_IO_PD(128),

	PORT_DATA_IO_PD(129),
	PORT_DATA_IO_PU_PD(130),
	PORT_DATA_IO_PD(131),
	PORT_DATA_IO_PD(132),
	PORT_DATA_IO_PD(133),
	PORT_DATA_IO_PU_PD(134),
	PORT_DATA_IO_PU_PD(135),
	PORT_DATA_IO_PU_PD(136),
	PORT_DATA_IO_PU_PD(137),
	PORT_DATA_IO_PD(138),
	PORT_DATA_IO_PD(139),
	PORT_DATA_IO_PD(140),
	PORT_DATA_IO_PD(141),
	PORT_DATA_IO_PD(142),
	PORT_DATA_IO_PD(143),
	PORT_DATA_IO_PU_PD(144),
	PORT_DATA_IO_PD(145),
	PORT_DATA_IO_PU_PD(146),
	PORT_DATA_IO_PU_PD(147),
	PORT_DATA_IO_PU_PD(148),
	PORT_DATA_IO_PU_PD(149),
	PORT_DATA_I_PU_PD(150),
	PORT_DATA_IO_PU_PD(151),
	PORT_DATA_IO_PU_PD(152),
	PORT_DATA_IO_PD(153),
	PORT_DATA_IO_PD(154),
	PORT_DATA_I_PU_PD(155),
	PORT_DATA_IO_PU_PD(156),
	PORT_DATA_I_PD(157),
	PORT_DATA_IO_PD(158),
	PORT_DATA_IO_PU_PD(159),
	PORT_DATA_IO_PU_PD(160),
	PORT_DATA_I_PU_PD(161),
	PORT_DATA_I_PU_PD(162),
	PORT_DATA_IO_PU_PD(163),
	PORT_DATA_I_PU_PD(164),
	PORT_DATA_IO_PD(192),
	PORT_DATA_IO_PU_PD(193),
	PORT_DATA_IO_PD(194),
	PORT_DATA_IO_PU_PD(195),
	PORT_DATA_IO_PD(196),
	PORT_DATA_IO_PD(197),
	PORT_DATA_IO_PD(198),
	PORT_DATA_IO_PD(199),
	PORT_DATA_IO_PU_PD(200),
	PORT_DATA_IO_PU_PD(201),
	PORT_DATA_IO_PU_PD(202),
	PORT_DATA_IO_PU_PD(203),
	PORT_DATA_IO_PU_PD(204),
	PORT_DATA_IO_PU_PD(205),
	PORT_DATA_IO_PU_PD(206),
	PORT_DATA_IO_PD(207),
	PORT_DATA_IO_PD(208),
	PORT_DATA_IO_PD(209),
	PORT_DATA_IO_PD(210),
	PORT_DATA_IO_PD(211),
	PORT_DATA_IO_PD(212),
	PORT_DATA_IO_PD(213),
	PORT_DATA_IO_PU_PD(214),
	PORT_DATA_IO_PU_PD(215),
	PORT_DATA_IO_PD(216),
	PORT_DATA_IO_PD(217),
	PORT_DATA_O(218),
	PORT_DATA_IO_PD(219),
	PORT_DATA_IO_PD(220),
	PORT_DATA_IO_PU_PD(221),
	PORT_DATA_IO_PU_PD(222),
	PORT_DATA_I_PU_PD(223),
	PORT_DATA_I_PU_PD(224),

	PORT_DATA_IO_PU_PD(225),
	PORT_DATA_O(226),
	PORT_DATA_IO_PU_PD(227),
	PORT_DATA_I_PU_PD(228),
	PORT_DATA_I_PD(229),
	PORT_DATA_IO(230),
	PORT_DATA_IO_PU_PD(231),
	PORT_DATA_IO_PU_PD(232),
	PORT_DATA_I_PU_PD(233),
	PORT_DATA_IO_PU_PD(234),
	PORT_DATA_IO_PU_PD(235),
	PORT_DATA_IO_PU_PD(236),
	PORT_DATA_IO_PD(237),
	PORT_DATA_IO_PU_PD(238),
	PORT_DATA_IO_PU_PD(239),
	PORT_DATA_IO_PU_PD(240),
	PORT_DATA_O(241),
	PORT_DATA_I_PD(242),
	PORT_DATA_IO_PU_PD(243),
	PORT_DATA_IO_PU_PD(244),
	PORT_DATA_IO_PU_PD(245),
	PORT_DATA_IO_PU_PD(246),
	PORT_DATA_IO_PU_PD(247),
	PORT_DATA_IO_PU_PD(248),
	PORT_DATA_IO_PU_PD(249),
	PORT_DATA_IO_PU_PD(250),
	PORT_DATA_IO_PU_PD(251),
	PORT_DATA_IO_PU_PD(252),
	PORT_DATA_IO_PU_PD(253),
	PORT_DATA_IO_PU_PD(254),
	PORT_DATA_IO_PU_PD(255),
	PORT_DATA_IO_PU_PD(256),
	PORT_DATA_IO_PU_PD(257),
	PORT_DATA_IO_PU_PD(258),
	PORT_DATA_IO_PU_PD(259),
	PORT_DATA_IO_PU_PD(260),
	PORT_DATA_IO_PU_PD(261),
	PORT_DATA_IO_PU_PD(262),
	PORT_DATA_IO_PU_PD(263),
	PORT_DATA_IO_PU_PD(264),
	PORT_DATA_IO_PU_PD(265),
	PORT_DATA_IO_PU_PD(266),
	PORT_DATA_IO_PU_PD(267),
	PORT_DATA_IO_PU_PD(268),
	PORT_DATA_IO_PU_PD(269),
	PORT_DATA_IO_PU_PD(270),
	PORT_DATA_IO_PU_PD(271),
	PORT_DATA_IO_PU_PD(272),
	PORT_DATA_IO_PU_PD(273),
	PORT_DATA_IO_PU_PD(274),
	PORT_DATA_IO_PU_PD(275),
	PORT_DATA_IO_PU_PD(276),
	PORT_DATA_IO_PU_PD(277),
	PORT_DATA_IO_PU_PD(278),
	PORT_DATA_IO_PU_PD(279),
	PORT_DATA_IO_PU_PD(280),
	PORT_DATA_O(281),
	PORT_DATA_O(282),
	PORT_DATA_I_PU(288),
	PORT_DATA_IO_PU_PD(289),
	PORT_DATA_IO_PU_PD(290),
	PORT_DATA_IO_PU_PD(291),
	PORT_DATA_IO_PU_PD(292),
	PORT_DATA_IO_PU_PD(293),
	PORT_DATA_IO_PU_PD(294),
	PORT_DATA_IO_PU_PD(295),
	PORT_DATA_IO_PU_PD(296),
	PORT_DATA_IO_PU_PD(297),
	PORT_DATA_IO_PU_PD(298),

	PORT_DATA_IO_PU_PD(299),
	PORT_DATA_IO_PU_PD(300),
	PORT_DATA_IO_PU_PD(301),
	PORT_DATA_IO_PU_PD(302),
	PORT_DATA_IO_PU_PD(303),
	PORT_DATA_IO_PU_PD(304),
	PORT_DATA_IO_PU_PD(305),
	PORT_DATA_O(306),
	PORT_DATA_O(307),
	PORT_DATA_I_PU(308),
	PORT_DATA_O(309),

	/* Table 25-1 (Function 0-7) */
	PINMUX_DATA(VBUS_0_MARK, PORT0_FN1),
	PINMUX_DATA(GPI0_MARK, PORT1_FN1),
	PINMUX_DATA(GPI1_MARK, PORT2_FN1),
	PINMUX_DATA(GPI2_MARK, PORT3_FN1),
	PINMUX_DATA(GPI3_MARK, PORT4_FN1),
	PINMUX_DATA(GPI4_MARK, PORT5_FN1),
	PINMUX_DATA(GPI5_MARK, PORT6_FN1),
	PINMUX_DATA(GPI6_MARK, PORT7_FN1),
	PINMUX_DATA(GPI7_MARK, PORT8_FN1),
	PINMUX_DATA(SCIFA7_RXD_MARK, PORT12_FN2),
	PINMUX_DATA(SCIFA7_CTS__MARK, PORT13_FN2),
	PINMUX_DATA(GPO7_MARK, PORT14_FN1), \
	PINMUX_DATA(MFG0_OUT2_MARK, PORT14_FN4),
	PINMUX_DATA(GPO6_MARK, PORT15_FN1), \
	PINMUX_DATA(MFG1_OUT2_MARK, PORT15_FN4),
	PINMUX_DATA(GPO5_MARK, PORT16_FN1), \
	PINMUX_DATA(SCIFA0_SCK_MARK, PORT16_FN2), \
	PINMUX_DATA(FSICOSLDT3_MARK, PORT16_FN3), \
	PINMUX_DATA(PORT16_VIO_CKOR_MARK, PORT16_FN4),
	PINMUX_DATA(SCIFA0_TXD_MARK, PORT17_FN2),
	PINMUX_DATA(SCIFA7_TXD_MARK, PORT18_FN2),
	PINMUX_DATA(SCIFA7_RTS__MARK, PORT19_FN2), \
	PINMUX_DATA(PORT19_VIO_CKO2_MARK, PORT19_FN3),
	PINMUX_DATA(GPO0_MARK, PORT20_FN1),
	PINMUX_DATA(GPO1_MARK, PORT21_FN1),
	PINMUX_DATA(GPO2_MARK, PORT22_FN1), \
	PINMUX_DATA(STATUS0_MARK, PORT22_FN2),
	PINMUX_DATA(GPO3_MARK, PORT23_FN1), \
	PINMUX_DATA(STATUS1_MARK, PORT23_FN2),
	PINMUX_DATA(GPO4_MARK, PORT24_FN1), \
	PINMUX_DATA(STATUS2_MARK, PORT24_FN2),
	PINMUX_DATA(VINT_MARK, PORT25_FN1),
	PINMUX_DATA(TCKON_MARK, PORT26_FN1),
	PINMUX_DATA(XDVFS1_MARK, PORT27_FN1), \
	PINMUX_DATA(PORT27_I2C_SCL2_MARK, PORT27_FN2, MSEL2CR_MSEL17_0,
		MSEL2CR_MSEL16_1), \
	PINMUX_DATA(PORT27_I2C_SCL3_MARK, PORT27_FN3, MSEL2CR_MSEL19_0,
		MSEL2CR_MSEL18_1), \
	PINMUX_DATA(MFG0_OUT1_MARK, PORT27_FN4), \
	PINMUX_DATA(PORT27_IROUT_MARK, PORT27_FN7),
	PINMUX_DATA(XDVFS2_MARK, PORT28_FN1), \
	PINMUX_DATA(PORT28_I2C_SDA2_MARK, PORT28_FN2, MSEL2CR_MSEL17_0,
		MSEL2CR_MSEL16_1), \
	PINMUX_DATA(PORT28_I2C_SDA3_MARK, PORT28_FN3, MSEL2CR_MSEL19_0,
		MSEL2CR_MSEL18_1), \
	PINMUX_DATA(PORT28_TPU1TO1_MARK, PORT28_FN7),
	PINMUX_DATA(SIM_RST_MARK, PORT29_FN1), \
	PINMUX_DATA(PORT29_TPU1TO1_MARK, PORT29_FN4),
	PINMUX_DATA(SIM_CLK_MARK, PORT30_FN1), \
	PINMUX_DATA(PORT30_VIO_CKOR_MARK, PORT30_FN4),
	PINMUX_DATA(SIM_D_MARK, PORT31_FN1), \
	PINMUX_DATA(PORT31_IROUT_MARK, PORT31_FN4),
	PINMUX_DATA(SCIFA4_TXD_MARK, PORT32_FN2),
	PINMUX_DATA(SCIFA4_RXD_MARK, PORT33_FN2), \
	PINMUX_DATA(XWUP_MARK, PORT33_FN3),
	PINMUX_DATA(SCIFA4_RTS__MARK, PORT34_FN2),
	PINMUX_DATA(SCIFA4_CTS__MARK, PORT35_FN2),
	PINMUX_DATA(FSIBOBT_MARK, PORT36_FN1), \
	PINMUX_DATA(FSIBIBT_MARK, PORT36_FN2),
	PINMUX_DATA(FSIBOLR_MARK, PORT37_FN1), \
	PINMUX_DATA(FSIBILR_MARK, PORT37_FN2),
	PINMUX_DATA(FSIBOSLD_MARK, PORT38_FN1),
	PINMUX_DATA(FSIBISLD_MARK, PORT39_FN1),
	PINMUX_DATA(VACK_MARK, PORT40_FN1),
	PINMUX_DATA(XTAL1L_MARK, PORT41_FN1),
	PINMUX_DATA(SCIFA0_RTS__MARK, PORT42_FN2), \
	PINMUX_DATA(FSICOSLDT2_MARK, PORT42_FN3),
	PINMUX_DATA(SCIFA0_RXD_MARK, PORT43_FN2),
	PINMUX_DATA(SCIFA0_CTS__MARK, PORT44_FN2), \
	PINMUX_DATA(FSICOSLDT1_MARK, PORT44_FN3),
	PINMUX_DATA(FSICOBT_MARK, PORT45_FN1), \
	PINMUX_DATA(FSICIBT_MARK, PORT45_FN2), \
	PINMUX_DATA(FSIDOBT_MARK, PORT45_FN3), \
	PINMUX_DATA(FSIDIBT_MARK, PORT45_FN4),
	PINMUX_DATA(FSICOLR_MARK, PORT46_FN1), \
	PINMUX_DATA(FSICILR_MARK, PORT46_FN2), \
	PINMUX_DATA(FSIDOLR_MARK, PORT46_FN3), \
	PINMUX_DATA(FSIDILR_MARK, PORT46_FN4),
	PINMUX_DATA(FSICOSLD_MARK, PORT47_FN1), \
	PINMUX_DATA(PORT47_FSICSPDIF_MARK, PORT47_FN2),
	PINMUX_DATA(FSICISLD_MARK, PORT48_FN1), \
	PINMUX_DATA(FSIDISLD_MARK, PORT48_FN3),
	PINMUX_DATA(FSIACK_MARK, PORT49_FN1), \
	PINMUX_DATA(PORT49_IRDA_OUT_MARK, PORT49_FN2, MSEL4CR_MSEL19_1), \
	PINMUX_DATA(PORT49_IROUT_MARK, PORT49_FN4), \
	PINMUX_DATA(FSIAOMC_MARK, PORT49_FN5),
	PINMUX_DATA(FSIAOLR_MARK, PORT50_FN1), \
	PINMUX_DATA(BBIF2_TSYNC2_MARK, PORT50_FN2), \
	PINMUX_DATA(TPU2TO2_MARK, PORT50_FN3), \
	PINMUX_DATA(FSIAILR_MARK, PORT50_FN5),

	PINMUX_DATA(FSIAOBT_MARK, PORT51_FN1), \
	PINMUX_DATA(BBIF2_TSCK2_MARK, PORT51_FN2), \
	PINMUX_DATA(TPU2TO3_MARK, PORT51_FN3), \
	PINMUX_DATA(FSIAIBT_MARK, PORT51_FN5),
	PINMUX_DATA(FSIAOSLD_MARK, PORT52_FN1), \
	PINMUX_DATA(BBIF2_TXD2_MARK, PORT52_FN2),
	PINMUX_DATA(FSIASPDIF_MARK, PORT53_FN1), \
	PINMUX_DATA(PORT53_IRDA_IN_MARK, PORT53_FN2, MSEL4CR_MSEL19_1), \
	PINMUX_DATA(TPU3TO3_MARK, PORT53_FN3), \
	PINMUX_DATA(FSIBSPDIF_MARK, PORT53_FN5), \
	PINMUX_DATA(PORT53_FSICSPDIF_MARK, PORT53_FN6),
	PINMUX_DATA(FSIBCK_MARK, PORT54_FN1), \
	PINMUX_DATA(PORT54_IRDA_FIRSEL_MARK, PORT54_FN2, MSEL4CR_MSEL19_1), \
	PINMUX_DATA(TPU3TO2_MARK, PORT54_FN3), \
	PINMUX_DATA(FSIBOMC_MARK, PORT54_FN5), \
	PINMUX_DATA(FSICCK_MARK, PORT54_FN6), \
	PINMUX_DATA(FSICOMC_MARK, PORT54_FN7),
	PINMUX_DATA(FSIAISLD_MARK, PORT55_FN1), \
	PINMUX_DATA(TPU0TO0_MARK, PORT55_FN3),
	PINMUX_DATA(A0_MARK, PORT57_FN1), \
	PINMUX_DATA(BS__MARK, PORT57_FN2),
	PINMUX_DATA(A12_MARK, PORT58_FN1), \
	PINMUX_DATA(PORT58_KEYOUT7_MARK, PORT58_FN2), \
	PINMUX_DATA(TPU4TO2_MARK, PORT58_FN4),
	PINMUX_DATA(A13_MARK, PORT59_FN1), \
	PINMUX_DATA(PORT59_KEYOUT6_MARK, PORT59_FN2), \
	PINMUX_DATA(TPU0TO1_MARK, PORT59_FN4),
	PINMUX_DATA(A14_MARK, PORT60_FN1), \
	PINMUX_DATA(KEYOUT5_MARK, PORT60_FN2),
	PINMUX_DATA(A15_MARK, PORT61_FN1), \
	PINMUX_DATA(KEYOUT4_MARK, PORT61_FN2),
	PINMUX_DATA(A16_MARK, PORT62_FN1), \
	PINMUX_DATA(KEYOUT3_MARK, PORT62_FN2), \
	PINMUX_DATA(MSIOF0_SS1_MARK, PORT62_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A17_MARK, PORT63_FN1), \
	PINMUX_DATA(KEYOUT2_MARK, PORT63_FN2), \
	PINMUX_DATA(MSIOF0_TSYNC_MARK, PORT63_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A18_MARK, PORT64_FN1), \
	PINMUX_DATA(KEYOUT1_MARK, PORT64_FN2), \
	PINMUX_DATA(MSIOF0_TSCK_MARK, PORT64_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A19_MARK, PORT65_FN1), \
	PINMUX_DATA(KEYOUT0_MARK, PORT65_FN2), \
	PINMUX_DATA(MSIOF0_TXD_MARK, PORT65_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A20_MARK, PORT66_FN1), \
	PINMUX_DATA(KEYIN0_MARK, PORT66_FN2), \
	PINMUX_DATA(MSIOF0_RSCK_MARK, PORT66_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A21_MARK, PORT67_FN1), \
	PINMUX_DATA(KEYIN1_MARK, PORT67_FN2), \
	PINMUX_DATA(MSIOF0_RSYNC_MARK, PORT67_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A22_MARK, PORT68_FN1), \
	PINMUX_DATA(KEYIN2_MARK, PORT68_FN2), \
	PINMUX_DATA(MSIOF0_MCK0_MARK, PORT68_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A23_MARK, PORT69_FN1), \
	PINMUX_DATA(KEYIN3_MARK, PORT69_FN2), \
	PINMUX_DATA(MSIOF0_MCK1_MARK, PORT69_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A24_MARK, PORT70_FN1), \
	PINMUX_DATA(KEYIN4_MARK, PORT70_FN2), \
	PINMUX_DATA(MSIOF0_RXD_MARK, PORT70_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A25_MARK, PORT71_FN1), \
	PINMUX_DATA(KEYIN5_MARK, PORT71_FN2), \
	PINMUX_DATA(MSIOF0_SS2_MARK, PORT71_FN4, MSEL3CR_MSEL11_0),
	PINMUX_DATA(A26_MARK, PORT72_FN1), \
	PINMUX_DATA(KEYIN6_MARK, PORT72_FN2),
	PINMUX_DATA(KEYIN7_MARK, PORT73_FN2),
	PINMUX_DATA(D0_NAF0_MARK, PORT74_FN1),
	PINMUX_DATA(D1_NAF1_MARK, PORT75_FN1),
	PINMUX_DATA(D2_NAF2_MARK, PORT76_FN1),
	PINMUX_DATA(D3_NAF3_MARK, PORT77_FN1),
	PINMUX_DATA(D4_NAF4_MARK, PORT78_FN1),
	PINMUX_DATA(D5_NAF5_MARK, PORT79_FN1),
	PINMUX_DATA(D6_NAF6_MARK, PORT80_FN1),
	PINMUX_DATA(D7_NAF7_MARK, PORT81_FN1),
	PINMUX_DATA(D8_NAF8_MARK, PORT82_FN1),
	PINMUX_DATA(D9_NAF9_MARK, PORT83_FN1),
	PINMUX_DATA(D10_NAF10_MARK, PORT84_FN1),
	PINMUX_DATA(D11_NAF11_MARK, PORT85_FN1),
	PINMUX_DATA(D12_NAF12_MARK, PORT86_FN1),
	PINMUX_DATA(D13_NAF13_MARK, PORT87_FN1),
	PINMUX_DATA(D14_NAF14_MARK, PORT88_FN1),
	PINMUX_DATA(D15_NAF15_MARK, PORT89_FN1),
	PINMUX_DATA(CS4__MARK, PORT90_FN1),
	PINMUX_DATA(CS5A__MARK, PORT91_FN1), \
	PINMUX_DATA(PORT91_RDWR_MARK, PORT91_FN2),
	PINMUX_DATA(CS5B__MARK, PORT92_FN1), \
	PINMUX_DATA(FCE1__MARK, PORT92_FN2),
	PINMUX_DATA(CS6B__MARK, PORT93_FN1), \
	PINMUX_DATA(DACK0_MARK, PORT93_FN4),
	PINMUX_DATA(FCE0__MARK, PORT94_FN1), \
	PINMUX_DATA(CS6A__MARK, PORT94_FN2),
	PINMUX_DATA(WAIT__MARK, PORT95_FN1), \
	PINMUX_DATA(DREQ0_MARK, PORT95_FN2),
	PINMUX_DATA(RD__FSC_MARK, PORT96_FN1),
	PINMUX_DATA(WE0__FWE_MARK, PORT97_FN1), \
	PINMUX_DATA(RDWR_FWE_MARK, PORT97_FN2),
	PINMUX_DATA(WE1__MARK, PORT98_FN1),
	PINMUX_DATA(FRB_MARK, PORT99_FN1),
	PINMUX_DATA(CKO_MARK, PORT100_FN1),
	PINMUX_DATA(NBRSTOUT__MARK, PORT101_FN1),
	PINMUX_DATA(NBRST__MARK, PORT102_FN1),
	PINMUX_DATA(BBIF2_TXD_MARK, PORT103_FN3),
	PINMUX_DATA(BBIF2_RXD_MARK, PORT104_FN3),
	PINMUX_DATA(BBIF2_SYNC_MARK, PORT105_FN3),
	PINMUX_DATA(BBIF2_SCK_MARK, PORT106_FN3),
	PINMUX_DATA(SCIFA3_CTS__MARK, PORT107_FN3), \
	PINMUX_DATA(MFG3_IN2_MARK, PORT107_FN4),
	PINMUX_DATA(SCIFA3_RXD_MARK, PORT108_FN3), \
	PINMUX_DATA(MFG3_IN1_MARK, PORT108_FN4),
	PINMUX_DATA(BBIF1_SS2_MARK, PORT109_FN2), \
	PINMUX_DATA(SCIFA3_RTS__MARK, PORT109_FN3), \
	PINMUX_DATA(MFG3_OUT1_MARK, PORT109_FN4),
	PINMUX_DATA(SCIFA3_TXD_MARK, PORT110_FN3),
	PINMUX_DATA(HSI_RX_DATA_MARK, PORT111_FN1), \
	PINMUX_DATA(BBIF1_RXD_MARK, PORT111_FN3),
	PINMUX_DATA(HSI_TX_WAKE_MARK, PORT112_FN1), \
	PINMUX_DATA(BBIF1_TSCK_MARK, PORT112_FN3),
	PINMUX_DATA(HSI_TX_DATA_MARK, PORT113_FN1), \
	PINMUX_DATA(BBIF1_TSYNC_MARK, PORT113_FN3),
	PINMUX_DATA(HSI_TX_READY_MARK, PORT114_FN1), \
	PINMUX_DATA(BBIF1_TXD_MARK, PORT114_FN3),
	PINMUX_DATA(HSI_RX_READY_MARK, PORT115_FN1), \
	PINMUX_DATA(BBIF1_RSCK_MARK, PORT115_FN3), \
	PINMUX_DATA(PORT115_I2C_SCL2_MARK, PORT115_FN5, MSEL2CR_MSEL17_1), \
	PINMUX_DATA(PORT115_I2C_SCL3_MARK, PORT115_FN6, MSEL2CR_MSEL19_1),
	PINMUX_DATA(HSI_RX_WAKE_MARK, PORT116_FN1), \
	PINMUX_DATA(BBIF1_RSYNC_MARK, PORT116_FN3), \
	PINMUX_DATA(PORT116_I2C_SDA2_MARK, PORT116_FN5, MSEL2CR_MSEL17_1), \
	PINMUX_DATA(PORT116_I2C_SDA3_MARK, PORT116_FN6, MSEL2CR_MSEL19_1),
	PINMUX_DATA(HSI_RX_FLAG_MARK, PORT117_FN1), \
	PINMUX_DATA(BBIF1_SS1_MARK, PORT117_FN2), \
	PINMUX_DATA(BBIF1_FLOW_MARK, PORT117_FN3),
	PINMUX_DATA(HSI_TX_FLAG_MARK, PORT118_FN1),
	PINMUX_DATA(VIO_VD_MARK, PORT128_FN1), \
	PINMUX_DATA(PORT128_LCD2VSYN_MARK, PORT128_FN4, MSEL3CR_MSEL2_0), \
	PINMUX_DATA(VIO2_VD_MARK, PORT128_FN6, MSEL4CR_MSEL27_0), \
	PINMUX_DATA(LCD2D0_MARK, PORT128_FN7),

	PINMUX_DATA(VIO_HD_MARK, PORT129_FN1), \
	PINMUX_DATA(PORT129_LCD2HSYN_MARK, PORT129_FN4), \
	PINMUX_DATA(PORT129_LCD2CS__MARK, PORT129_FN5), \
	PINMUX_DATA(VIO2_HD_MARK, PORT129_FN6, MSEL4CR_MSEL27_0), \
	PINMUX_DATA(LCD2D1_MARK, PORT129_FN7),
	PINMUX_DATA(VIO_D0_MARK, PORT130_FN1), \
	PINMUX_DATA(PORT130_MSIOF2_RXD_MARK, PORT130_FN3, MSEL4CR_MSEL11_0,
		MSEL4CR_MSEL10_1), \
	PINMUX_DATA(LCD2D10_MARK, PORT130_FN7),
	PINMUX_DATA(VIO_D1_MARK, PORT131_FN1), \
	PINMUX_DATA(PORT131_KEYOUT6_MARK, PORT131_FN2), \
	PINMUX_DATA(PORT131_MSIOF2_SS1_MARK, PORT131_FN3), \
	PINMUX_DATA(PORT131_KEYOUT11_MARK, PORT131_FN4), \
	PINMUX_DATA(LCD2D11_MARK, PORT131_FN7),
	PINMUX_DATA(VIO_D2_MARK, PORT132_FN1), \
	PINMUX_DATA(PORT132_KEYOUT7_MARK, PORT132_FN2), \
	PINMUX_DATA(PORT132_MSIOF2_SS2_MARK, PORT132_FN3), \
	PINMUX_DATA(PORT132_KEYOUT10_MARK, PORT132_FN4), \
	PINMUX_DATA(LCD2D12_MARK, PORT132_FN7),
	PINMUX_DATA(VIO_D3_MARK, PORT133_FN1), \
	PINMUX_DATA(MSIOF2_TSYNC_MARK, PORT133_FN3, MSEL4CR_MSEL11_0), \
	PINMUX_DATA(LCD2D13_MARK, PORT133_FN7),
	PINMUX_DATA(VIO_D4_MARK, PORT134_FN1), \
	PINMUX_DATA(MSIOF2_TXD_MARK, PORT134_FN3, MSEL4CR_MSEL11_0), \
	PINMUX_DATA(LCD2D14_MARK, PORT134_FN7),
	PINMUX_DATA(VIO_D5_MARK, PORT135_FN1), \
	PINMUX_DATA(MSIOF2_TSCK_MARK, PORT135_FN3, MSEL4CR_MSEL11_0), \
	PINMUX_DATA(LCD2D15_MARK, PORT135_FN7),
	PINMUX_DATA(VIO_D6_MARK, PORT136_FN1), \
	PINMUX_DATA(PORT136_KEYOUT8_MARK, PORT136_FN2), \
	PINMUX_DATA(LCD2D16_MARK, PORT136_FN7),
	PINMUX_DATA(VIO_D7_MARK, PORT137_FN1), \
	PINMUX_DATA(PORT137_KEYOUT9_MARK, PORT137_FN2), \
	PINMUX_DATA(LCD2D17_MARK, PORT137_FN7),
	PINMUX_DATA(VIO_D8_MARK, PORT138_FN1), \
	PINMUX_DATA(PORT138_KEYOUT8_MARK, PORT138_FN2), \
	PINMUX_DATA(VIO2_D0_MARK, PORT138_FN6), \
	PINMUX_DATA(LCD2D6_MARK, PORT138_FN7),
	PINMUX_DATA(VIO_D9_MARK, PORT139_FN1), \
	PINMUX_DATA(PORT139_KEYOUT9_MARK, PORT139_FN2), \
	PINMUX_DATA(VIO2_D1_MARK, PORT139_FN6), \
	PINMUX_DATA(LCD2D7_MARK, PORT139_FN7),
	PINMUX_DATA(VIO_D10_MARK, PORT140_FN1), \
	PINMUX_DATA(TPU0TO2_MARK, PORT140_FN4), \
	PINMUX_DATA(VIO2_D2_MARK, PORT140_FN6), \
	PINMUX_DATA(LCD2D8_MARK, PORT140_FN7),
	PINMUX_DATA(VIO_D11_MARK, PORT141_FN1), \
	PINMUX_DATA(TPU0TO3_MARK, PORT141_FN4), \
	PINMUX_DATA(VIO2_D3_MARK, PORT141_FN6), \
	PINMUX_DATA(LCD2D9_MARK, PORT141_FN7),
	PINMUX_DATA(VIO_D12_MARK, PORT142_FN1), \
	PINMUX_DATA(PORT142_KEYOUT10_MARK, PORT142_FN2), \
	PINMUX_DATA(VIO2_D4_MARK, PORT142_FN6), \
	PINMUX_DATA(LCD2D2_MARK, PORT142_FN7),
	PINMUX_DATA(VIO_D13_MARK, PORT143_FN1), \
	PINMUX_DATA(PORT143_KEYOUT11_MARK, PORT143_FN2), \
	PINMUX_DATA(PORT143_KEYOUT6_MARK, PORT143_FN3), \
	PINMUX_DATA(VIO2_D5_MARK, PORT143_FN6), \
	PINMUX_DATA(LCD2D3_MARK, PORT143_FN7),
	PINMUX_DATA(VIO_D14_MARK, PORT144_FN1), \
	PINMUX_DATA(PORT144_KEYOUT7_MARK, PORT144_FN2), \
	PINMUX_DATA(VIO2_D6_MARK, PORT144_FN6), \
	PINMUX_DATA(LCD2D4_MARK, PORT144_FN7),
	PINMUX_DATA(VIO_D15_MARK, PORT145_FN1), \
	PINMUX_DATA(TPU1TO3_MARK, PORT145_FN3), \
	PINMUX_DATA(PORT145_LCD2DISP_MARK, PORT145_FN4), \
	PINMUX_DATA(PORT145_LCD2RS_MARK, PORT145_FN5), \
	PINMUX_DATA(VIO2_D7_MARK, PORT145_FN6), \
	PINMUX_DATA(LCD2D5_MARK, PORT145_FN7),
	PINMUX_DATA(VIO_CLK_MARK, PORT146_FN1), \
	PINMUX_DATA(LCD2DCK_MARK, PORT146_FN4), \
	PINMUX_DATA(PORT146_LCD2WR__MARK, PORT146_FN5), \
	PINMUX_DATA(VIO2_CLK_MARK, PORT146_FN6, MSEL4CR_MSEL27_0), \
	PINMUX_DATA(LCD2D18_MARK, PORT146_FN7),
	PINMUX_DATA(VIO_FIELD_MARK, PORT147_FN1), \
	PINMUX_DATA(LCD2RD__MARK, PORT147_FN4), \
	PINMUX_DATA(VIO2_FIELD_MARK, PORT147_FN6, MSEL4CR_MSEL27_0), \
	PINMUX_DATA(LCD2D19_MARK, PORT147_FN7),
	PINMUX_DATA(VIO_CKO_MARK, PORT148_FN1),
	PINMUX_DATA(A27_MARK, PORT149_FN1), \
	PINMUX_DATA(PORT149_RDWR_MARK, PORT149_FN2), \
	PINMUX_DATA(MFG0_IN1_MARK, PORT149_FN3), \
	PINMUX_DATA(PORT149_KEYOUT9_MARK, PORT149_FN4),
	PINMUX_DATA(MFG0_IN2_MARK, PORT150_FN3),
	PINMUX_DATA(TS_SPSYNC3_MARK, PORT151_FN4), \
	PINMUX_DATA(MSIOF2_RSCK_MARK, PORT151_FN5),
	PINMUX_DATA(TS_SDAT3_MARK, PORT152_FN4), \
	PINMUX_DATA(MSIOF2_RSYNC_MARK, PORT152_FN5),
	PINMUX_DATA(TPU1TO2_MARK, PORT153_FN3), \
	PINMUX_DATA(TS_SDEN3_MARK, PORT153_FN4), \
	PINMUX_DATA(PORT153_MSIOF2_SS1_MARK, PORT153_FN5),
	PINMUX_DATA(SCIFA2_TXD1_MARK, PORT154_FN2, MSEL3CR_MSEL9_0), \
	PINMUX_DATA(MSIOF2_MCK0_MARK, PORT154_FN5),
	PINMUX_DATA(SCIFA2_RXD1_MARK, PORT155_FN2, MSEL3CR_MSEL9_0), \
	PINMUX_DATA(MSIOF2_MCK1_MARK, PORT155_FN5),
	PINMUX_DATA(SCIFA2_RTS1__MARK, PORT156_FN2, MSEL3CR_MSEL9_0), \
	PINMUX_DATA(PORT156_MSIOF2_SS2_MARK, PORT156_FN5),
	PINMUX_DATA(SCIFA2_CTS1__MARK, PORT157_FN2, MSEL3CR_MSEL9_0), \
	PINMUX_DATA(PORT157_MSIOF2_RXD_MARK, PORT157_FN5, MSEL4CR_MSEL11_0,
		MSEL4CR_MSEL10_0),
	PINMUX_DATA(DINT__MARK, PORT158_FN1), \
	PINMUX_DATA(SCIFA2_SCK1_MARK, PORT158_FN2, MSEL3CR_MSEL9_0), \
	PINMUX_DATA(TS_SCK3_MARK, PORT158_FN4),
	PINMUX_DATA(PORT159_SCIFB_SCK_MARK, PORT159_FN1, MSEL4CR_MSEL22_0), \
	PINMUX_DATA(PORT159_SCIFA5_SCK_MARK, PORT159_FN2, MSEL4CR_MSEL21_1), \
	PINMUX_DATA(NMI_MARK, PORT159_FN3),
	PINMUX_DATA(PORT160_SCIFB_TXD_MARK, PORT160_FN1, MSEL4CR_MSEL22_0), \
	PINMUX_DATA(PORT160_SCIFA5_TXD_MARK, PORT160_FN2, MSEL4CR_MSEL21_1),
	PINMUX_DATA(PORT161_SCIFB_CTS__MARK, PORT161_FN1, MSEL4CR_MSEL22_0), \
	PINMUX_DATA(PORT161_SCIFA5_CTS__MARK, PORT161_FN2, MSEL4CR_MSEL21_1),
	PINMUX_DATA(PORT162_SCIFB_RXD_MARK, PORT162_FN1, MSEL4CR_MSEL22_0), \
	PINMUX_DATA(PORT162_SCIFA5_RXD_MARK, PORT162_FN2, MSEL4CR_MSEL21_1),
	PINMUX_DATA(PORT163_SCIFB_RTS__MARK, PORT163_FN1, MSEL4CR_MSEL22_0), \
	PINMUX_DATA(PORT163_SCIFA5_RTS__MARK, PORT163_FN2, MSEL4CR_MSEL21_1), \
	PINMUX_DATA(TPU3TO0_MARK, PORT163_FN5),
	PINMUX_DATA(LCDD0_MARK, PORT192_FN1),
	PINMUX_DATA(LCDD1_MARK, PORT193_FN1), \
	PINMUX_DATA(PORT193_SCIFA5_CTS__MARK, PORT193_FN3, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_1), \
	PINMUX_DATA(BBIF2_TSYNC1_MARK, PORT193_FN5),
	PINMUX_DATA(LCDD2_MARK, PORT194_FN1), \
	PINMUX_DATA(PORT194_SCIFA5_RTS__MARK, PORT194_FN3, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_1), \
	PINMUX_DATA(BBIF2_TSCK1_MARK, PORT194_FN5),
	PINMUX_DATA(LCDD3_MARK, PORT195_FN1), \
	PINMUX_DATA(PORT195_SCIFA5_RXD_MARK, PORT195_FN3, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_1), \
	PINMUX_DATA(BBIF2_TXD1_MARK, PORT195_FN5),
	PINMUX_DATA(LCDD4_MARK, PORT196_FN1), \
	PINMUX_DATA(PORT196_SCIFA5_TXD_MARK, PORT196_FN3, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_1),
	PINMUX_DATA(LCDD5_MARK, PORT197_FN1), \
	PINMUX_DATA(PORT197_SCIFA5_SCK_MARK, PORT197_FN3, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_1), \
	PINMUX_DATA(MFG2_OUT2_MARK, PORT197_FN5), \
	PINMUX_DATA(TPU2TO1_MARK, PORT197_FN7),
	PINMUX_DATA(LCDD6_MARK, PORT198_FN1),
	PINMUX_DATA(LCDD7_MARK, PORT199_FN1), \
	PINMUX_DATA(TPU4TO1_MARK, PORT199_FN2), \
	PINMUX_DATA(MFG4_OUT2_MARK, PORT199_FN5),
	PINMUX_DATA(LCDD8_MARK, PORT200_FN1), \
	PINMUX_DATA(D16_MARK, PORT200_FN6),
	PINMUX_DATA(LCDD9_MARK, PORT201_FN1), \
	PINMUX_DATA(D17_MARK, PORT201_FN6),
	PINMUX_DATA(LCDD10_MARK, PORT202_FN1), \
	PINMUX_DATA(D18_MARK, PORT202_FN6),
	PINMUX_DATA(LCDD11_MARK, PORT203_FN1), \
	PINMUX_DATA(D19_MARK, PORT203_FN6),
	PINMUX_DATA(LCDD12_MARK, PORT204_FN1), \
	PINMUX_DATA(D20_MARK, PORT204_FN6),
	PINMUX_DATA(LCDD13_MARK, PORT205_FN1), \
	PINMUX_DATA(D21_MARK, PORT205_FN6),
	PINMUX_DATA(LCDD14_MARK, PORT206_FN1), \
	PINMUX_DATA(D22_MARK, PORT206_FN6),
	PINMUX_DATA(LCDD15_MARK, PORT207_FN1), \
	PINMUX_DATA(PORT207_MSIOF0L_SS1_MARK, PORT207_FN2, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D23_MARK, PORT207_FN6),
	PINMUX_DATA(LCDD16_MARK, PORT208_FN1), \
	PINMUX_DATA(PORT208_MSIOF0L_SS2_MARK, PORT208_FN2, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D24_MARK, PORT208_FN6),
	PINMUX_DATA(LCDD17_MARK, PORT209_FN1), \
	PINMUX_DATA(D25_MARK, PORT209_FN6),
	PINMUX_DATA(LCDD18_MARK, PORT210_FN1), \
	PINMUX_DATA(DREQ2_MARK, PORT210_FN2), \
	PINMUX_DATA(PORT210_MSIOF0L_SS1_MARK, PORT210_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D26_MARK, PORT210_FN6),
	PINMUX_DATA(LCDD19_MARK, PORT211_FN1), \
	PINMUX_DATA(PORT211_MSIOF0L_SS2_MARK, PORT211_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D27_MARK, PORT211_FN6),
	PINMUX_DATA(LCDD20_MARK, PORT212_FN1), \
	PINMUX_DATA(TS_SPSYNC1_MARK, PORT212_FN2), \
	PINMUX_DATA(MSIOF0L_MCK0_MARK, PORT212_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D28_MARK, PORT212_FN6),
	PINMUX_DATA(LCDD21_MARK, PORT213_FN1), \
	PINMUX_DATA(TS_SDAT1_MARK, PORT213_FN2), \
	PINMUX_DATA(MSIOF0L_MCK1_MARK, PORT213_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D29_MARK, PORT213_FN6),
	PINMUX_DATA(LCDD22_MARK, PORT214_FN1), \
	PINMUX_DATA(TS_SDEN1_MARK, PORT214_FN2), \
	PINMUX_DATA(MSIOF0L_RSCK_MARK, PORT214_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D30_MARK, PORT214_FN6),
	PINMUX_DATA(LCDD23_MARK, PORT215_FN1), \
	PINMUX_DATA(TS_SCK1_MARK, PORT215_FN2), \
	PINMUX_DATA(MSIOF0L_RSYNC_MARK, PORT215_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(D31_MARK, PORT215_FN6),
	PINMUX_DATA(LCDDCK_MARK, PORT216_FN1), \
	PINMUX_DATA(LCDWR__MARK, PORT216_FN2),
	PINMUX_DATA(LCDRD__MARK, PORT217_FN1), \
	PINMUX_DATA(DACK2_MARK, PORT217_FN2), \
	PINMUX_DATA(PORT217_LCD2RS_MARK, PORT217_FN3), \
	PINMUX_DATA(MSIOF0L_TSYNC_MARK, PORT217_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(VIO2_FIELD3_MARK, PORT217_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_1), \
	PINMUX_DATA(PORT217_LCD2DISP_MARK, PORT217_FN7),
	PINMUX_DATA(LCDHSYN_MARK, PORT218_FN1), \
	PINMUX_DATA(LCDCS__MARK, PORT218_FN2), \
	PINMUX_DATA(LCDCS2__MARK, PORT218_FN3), \
	PINMUX_DATA(DACK3_MARK, PORT218_FN4), \
	PINMUX_DATA(PORT218_VIO_CKOR_MARK, PORT218_FN5),
	PINMUX_DATA(LCDDISP_MARK, PORT219_FN1), \
	PINMUX_DATA(LCDRS_MARK, PORT219_FN2), \
	PINMUX_DATA(PORT219_LCD2WR__MARK, PORT219_FN3), \
	PINMUX_DATA(DREQ3_MARK, PORT219_FN4), \
	PINMUX_DATA(MSIOF0L_TSCK_MARK, PORT219_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(VIO2_CLK3_MARK, PORT219_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_1), \
	PINMUX_DATA(LCD2DCK_2_MARK, PORT219_FN7),
	PINMUX_DATA(LCDVSYN_MARK, PORT220_FN1), \
	PINMUX_DATA(LCDVSYN2_MARK, PORT220_FN2),
	PINMUX_DATA(LCDLCLK_MARK, PORT221_FN1), \
	PINMUX_DATA(DREQ1_MARK, PORT221_FN2), \
	PINMUX_DATA(PORT221_LCD2CS__MARK, PORT221_FN3), \
	PINMUX_DATA(PWEN_MARK, PORT221_FN4), \
	PINMUX_DATA(MSIOF0L_RXD_MARK, PORT221_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(VIO2_HD3_MARK, PORT221_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_1), \
	PINMUX_DATA(PORT221_LCD2HSYN_MARK, PORT221_FN7),
	PINMUX_DATA(LCDDON_MARK, PORT222_FN1), \
	PINMUX_DATA(LCDDON2_MARK, PORT222_FN2), \
	PINMUX_DATA(DACK1_MARK, PORT222_FN3), \
	PINMUX_DATA(OVCN_MARK, PORT222_FN4), \
	PINMUX_DATA(MSIOF0L_TXD_MARK, PORT222_FN5, MSEL3CR_MSEL11_1), \
	PINMUX_DATA(VIO2_VD3_MARK, PORT222_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_1), \
	PINMUX_DATA(PORT222_LCD2VSYN_MARK, PORT222_FN7, MSEL3CR_MSEL2_1),

	PINMUX_DATA(SCIFA1_TXD_MARK, PORT225_FN2), \
	PINMUX_DATA(OVCN2_MARK, PORT225_FN4),
	PINMUX_DATA(EXTLP_MARK, PORT226_FN1), \
	PINMUX_DATA(SCIFA1_SCK_MARK, PORT226_FN2), \
	PINMUX_DATA(PORT226_VIO_CKO2_MARK, PORT226_FN5),
	PINMUX_DATA(SCIFA1_RTS__MARK, PORT227_FN2), \
	PINMUX_DATA(IDIN_MARK, PORT227_FN4),
	PINMUX_DATA(SCIFA1_RXD_MARK, PORT228_FN2),
	PINMUX_DATA(SCIFA1_CTS__MARK, PORT229_FN2), \
	PINMUX_DATA(MFG1_IN1_MARK, PORT229_FN3),
	PINMUX_DATA(MSIOF1_TXD_MARK, PORT230_FN1), \
	PINMUX_DATA(SCIFA2_TXD2_MARK, PORT230_FN2, MSEL3CR_MSEL9_1),
	PINMUX_DATA(MSIOF1_TSYNC_MARK, PORT231_FN1), \
	PINMUX_DATA(SCIFA2_CTS2__MARK, PORT231_FN2, MSEL3CR_MSEL9_1),
	PINMUX_DATA(MSIOF1_TSCK_MARK, PORT232_FN1), \
	PINMUX_DATA(SCIFA2_SCK2_MARK, PORT232_FN2, MSEL3CR_MSEL9_1),
	PINMUX_DATA(MSIOF1_RXD_MARK, PORT233_FN1), \
	PINMUX_DATA(SCIFA2_RXD2_MARK, PORT233_FN2, MSEL3CR_MSEL9_1),
	PINMUX_DATA(MSIOF1_RSCK_MARK, PORT234_FN1), \
	PINMUX_DATA(SCIFA2_RTS2__MARK, PORT234_FN2, MSEL3CR_MSEL9_1), \
	PINMUX_DATA(VIO2_CLK2_MARK, PORT234_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_0), \
	PINMUX_DATA(LCD2D20_MARK, PORT234_FN7),
	PINMUX_DATA(MSIOF1_RSYNC_MARK, PORT235_FN1), \
	PINMUX_DATA(MFG1_IN2_MARK, PORT235_FN3), \
	PINMUX_DATA(VIO2_VD2_MARK, PORT235_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_0), \
	PINMUX_DATA(LCD2D21_MARK, PORT235_FN7),
	PINMUX_DATA(MSIOF1_MCK0_MARK, PORT236_FN1), \
	PINMUX_DATA(PORT236_I2C_SDA2_MARK, PORT236_FN2, MSEL2CR_MSEL17_0,
		MSEL2CR_MSEL16_0),
	PINMUX_DATA(MSIOF1_MCK1_MARK, PORT237_FN1), \
	PINMUX_DATA(PORT237_I2C_SCL2_MARK, PORT237_FN2, MSEL2CR_MSEL17_0,
		MSEL2CR_MSEL16_0),
	PINMUX_DATA(MSIOF1_SS1_MARK, PORT238_FN1), \
	PINMUX_DATA(VIO2_FIELD2_MARK, PORT238_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_0), \
	PINMUX_DATA(LCD2D22_MARK, PORT238_FN7),
	PINMUX_DATA(MSIOF1_SS2_MARK, PORT239_FN1), \
	PINMUX_DATA(VIO2_HD2_MARK, PORT239_FN6, MSEL4CR_MSEL27_1,
		MSEL4CR_MSEL26_0), \
	PINMUX_DATA(LCD2D23_MARK, PORT239_FN7),
	PINMUX_DATA(SCIFA6_TXD_MARK, PORT240_FN1),
	PINMUX_DATA(PORT241_IRDA_OUT_MARK, PORT241_FN1, MSEL4CR_MSEL19_0), \
	PINMUX_DATA(PORT241_IROUT_MARK, PORT241_FN2), \
	PINMUX_DATA(MFG4_OUT1_MARK, PORT241_FN3), \
	PINMUX_DATA(TPU4TO0_MARK, PORT241_FN4),
	PINMUX_DATA(PORT242_IRDA_IN_MARK, PORT242_FN1, MSEL4CR_MSEL19_0), \
	PINMUX_DATA(MFG4_IN2_MARK, PORT242_FN3),
	PINMUX_DATA(PORT243_IRDA_FIRSEL_MARK, PORT243_FN1, MSEL4CR_MSEL19_0), \
	PINMUX_DATA(PORT243_VIO_CKO2_MARK, PORT243_FN2),
	PINMUX_DATA(PORT244_SCIFA5_CTS__MARK, PORT244_FN1, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_0), \
	PINMUX_DATA(MFG2_IN1_MARK, PORT244_FN2), \
	PINMUX_DATA(PORT244_SCIFB_CTS__MARK, PORT244_FN3, MSEL4CR_MSEL22_1), \
	PINMUX_DATA(MSIOF2R_RXD_MARK, PORT244_FN7, MSEL4CR_MSEL11_1),
	PINMUX_DATA(PORT245_SCIFA5_RTS__MARK, PORT245_FN1, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_0), \
	PINMUX_DATA(MFG2_IN2_MARK, PORT245_FN2), \
	PINMUX_DATA(PORT245_SCIFB_RTS__MARK, PORT245_FN3, MSEL4CR_MSEL22_1), \
	PINMUX_DATA(MSIOF2R_TXD_MARK, PORT245_FN7, MSEL4CR_MSEL11_1),
	PINMUX_DATA(PORT246_SCIFA5_RXD_MARK, PORT246_FN1, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_0), \
	PINMUX_DATA(MFG1_OUT1_MARK, PORT246_FN2), \
	PINMUX_DATA(PORT246_SCIFB_RXD_MARK, PORT246_FN3, MSEL4CR_MSEL22_1), \
	PINMUX_DATA(TPU1TO0_MARK, PORT246_FN4),
	PINMUX_DATA(PORT247_SCIFA5_TXD_MARK, PORT247_FN1, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_0), \
	PINMUX_DATA(MFG3_OUT2_MARK, PORT247_FN2), \
	PINMUX_DATA(PORT247_SCIFB_TXD_MARK, PORT247_FN3, MSEL4CR_MSEL22_1), \
	PINMUX_DATA(TPU3TO1_MARK, PORT247_FN4),
	PINMUX_DATA(PORT248_SCIFA5_SCK_MARK, PORT248_FN1, MSEL4CR_MSEL21_0,
		MSEL4CR_MSEL20_0), \
	PINMUX_DATA(MFG2_OUT1_MARK, PORT248_FN2), \
	PINMUX_DATA(PORT248_SCIFB_SCK_MARK, PORT248_FN3, MSEL4CR_MSEL22_1), \
	PINMUX_DATA(TPU2TO0_MARK, PORT248_FN4), \
	PINMUX_DATA(PORT248_I2C_SCL3_MARK, PORT248_FN5, MSEL2CR_MSEL19_0,
		MSEL2CR_MSEL18_0), \
	PINMUX_DATA(MSIOF2R_TSCK_MARK, PORT248_FN7, MSEL4CR_MSEL11_1),
	PINMUX_DATA(PORT249_IROUT_MARK, PORT249_FN1), \
	PINMUX_DATA(MFG4_IN1_MARK, PORT249_FN2), \
	PINMUX_DATA(PORT249_I2C_SDA3_MARK, PORT249_FN5, MSEL2CR_MSEL19_0,
		MSEL2CR_MSEL18_0), \
	PINMUX_DATA(MSIOF2R_TSYNC_MARK, PORT249_FN7, MSEL4CR_MSEL11_1),
	PINMUX_DATA(SDHICLK0_MARK, PORT250_FN1),
	PINMUX_DATA(SDHICD0_MARK, PORT251_FN1),
	PINMUX_DATA(SDHID0_0_MARK, PORT252_FN1),
	PINMUX_DATA(SDHID0_1_MARK, PORT253_FN1),
	PINMUX_DATA(SDHID0_2_MARK, PORT254_FN1),
	PINMUX_DATA(SDHID0_3_MARK, PORT255_FN1),
	PINMUX_DATA(SDHICMD0_MARK, PORT256_FN1),
	PINMUX_DATA(SDHIWP0_MARK, PORT257_FN1),
	PINMUX_DATA(SDHICLK1_MARK, PORT258_FN1),
	PINMUX_DATA(SDHID1_0_MARK, PORT259_FN1), \
	PINMUX_DATA(TS_SPSYNC2_MARK, PORT259_FN3),
	PINMUX_DATA(SDHID1_1_MARK, PORT260_FN1), \
	PINMUX_DATA(TS_SDAT2_MARK, PORT260_FN3),
	PINMUX_DATA(SDHID1_2_MARK, PORT261_FN1), \
	PINMUX_DATA(TS_SDEN2_MARK, PORT261_FN3),
	PINMUX_DATA(SDHID1_3_MARK, PORT262_FN1), \
	PINMUX_DATA(TS_SCK2_MARK, PORT262_FN3),
	PINMUX_DATA(SDHICMD1_MARK, PORT263_FN1),
	PINMUX_DATA(SDHICLK2_MARK, PORT264_FN1),
	PINMUX_DATA(SDHID2_0_MARK, PORT265_FN1), \
	PINMUX_DATA(TS_SPSYNC4_MARK, PORT265_FN3),
	PINMUX_DATA(SDHID2_1_MARK, PORT266_FN1), \
	PINMUX_DATA(TS_SDAT4_MARK, PORT266_FN3),
	PINMUX_DATA(SDHID2_2_MARK, PORT267_FN1), \
	PINMUX_DATA(TS_SDEN4_MARK, PORT267_FN3),
	PINMUX_DATA(SDHID2_3_MARK, PORT268_FN1), \
	PINMUX_DATA(TS_SCK4_MARK, PORT268_FN3),
	PINMUX_DATA(SDHICMD2_MARK, PORT269_FN1),
	PINMUX_DATA(MMCCLK0_MARK, PORT270_FN1, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_0_MARK, PORT271_FN1, PORT271_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_1_MARK, PORT272_FN1, PORT272_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_2_MARK, PORT273_FN1, PORT273_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_3_MARK, PORT274_FN1, PORT274_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_4_MARK, PORT275_FN1, PORT275_IN_PU,
		MSEL4CR_MSEL15_0), \
	PINMUX_DATA(TS_SPSYNC5_MARK, PORT275_FN3),
	PINMUX_DATA(MMCD0_5_MARK, PORT276_FN1, PORT276_IN_PU,
		MSEL4CR_MSEL15_0), \
	PINMUX_DATA(TS_SDAT5_MARK, PORT276_FN3),
	PINMUX_DATA(MMCD0_6_MARK, PORT277_FN1, PORT277_IN_PU,
		MSEL4CR_MSEL15_0), \
	PINMUX_DATA(TS_SDEN5_MARK, PORT277_FN3),
	PINMUX_DATA(MMCD0_7_MARK, PORT278_FN1, PORT278_IN_PU,
		MSEL4CR_MSEL15_0), \
	PINMUX_DATA(TS_SCK5_MARK, PORT278_FN3),
	PINMUX_DATA(MMCCMD0_MARK, PORT279_FN1, PORT279_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(RESETOUTS__MARK, PORT281_FN1), \
	PINMUX_DATA(EXTAL2OUT_MARK, PORT281_FN2),
	PINMUX_DATA(MCP_WAIT__MCP_FRB_MARK, PORT288_FN1),
	PINMUX_DATA(MCP_CKO_MARK, PORT289_FN1), \
	PINMUX_DATA(MMCCLK1_MARK, PORT289_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D15_MCP_NAF15_MARK, PORT290_FN1),
	PINMUX_DATA(MCP_D14_MCP_NAF14_MARK, PORT291_FN1),
	PINMUX_DATA(MCP_D13_MCP_NAF13_MARK, PORT292_FN1),
	PINMUX_DATA(MCP_D12_MCP_NAF12_MARK, PORT293_FN1),
	PINMUX_DATA(MCP_D11_MCP_NAF11_MARK, PORT294_FN1),
	PINMUX_DATA(MCP_D10_MCP_NAF10_MARK, PORT295_FN1),
	PINMUX_DATA(MCP_D9_MCP_NAF9_MARK, PORT296_FN1),
	PINMUX_DATA(MCP_D8_MCP_NAF8_MARK, PORT297_FN1), \
	PINMUX_DATA(MMCCMD1_MARK, PORT297_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D7_MCP_NAF7_MARK, PORT298_FN1), \
	PINMUX_DATA(MMCD1_7_MARK, PORT298_FN2, MSEL4CR_MSEL15_1),

	PINMUX_DATA(MCP_D6_MCP_NAF6_MARK, PORT299_FN1), \
	PINMUX_DATA(MMCD1_6_MARK, PORT299_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D5_MCP_NAF5_MARK, PORT300_FN1), \
	PINMUX_DATA(MMCD1_5_MARK, PORT300_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D4_MCP_NAF4_MARK, PORT301_FN1), \
	PINMUX_DATA(MMCD1_4_MARK, PORT301_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D3_MCP_NAF3_MARK, PORT302_FN1), \
	PINMUX_DATA(MMCD1_3_MARK, PORT302_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D2_MCP_NAF2_MARK, PORT303_FN1), \
	PINMUX_DATA(MMCD1_2_MARK, PORT303_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D1_MCP_NAF1_MARK, PORT304_FN1), \
	PINMUX_DATA(MMCD1_1_MARK, PORT304_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_D0_MCP_NAF0_MARK, PORT305_FN1), \
	PINMUX_DATA(MMCD1_0_MARK, PORT305_FN2, MSEL4CR_MSEL15_1),
	PINMUX_DATA(MCP_NBRSTOUT__MARK, PORT306_FN1),
	PINMUX_DATA(MCP_WE0__MCP_FWE_MARK, PORT309_FN1), \
	PINMUX_DATA(MCP_RDWR_MCP_FWE_MARK, PORT309_FN2),

	/* MSEL2 special cases */
	PINMUX_DATA(TSIF2_TS_XX1_MARK, MSEL2CR_MSEL14_0, MSEL2CR_MSEL13_0,
		MSEL2CR_MSEL12_0),
	PINMUX_DATA(TSIF2_TS_XX2_MARK, MSEL2CR_MSEL14_0, MSEL2CR_MSEL13_0,
		MSEL2CR_MSEL12_1),
	PINMUX_DATA(TSIF2_TS_XX3_MARK, MSEL2CR_MSEL14_0, MSEL2CR_MSEL13_1,
		MSEL2CR_MSEL12_0),
	PINMUX_DATA(TSIF2_TS_XX4_MARK, MSEL2CR_MSEL14_0, MSEL2CR_MSEL13_1,
		MSEL2CR_MSEL12_1),
	PINMUX_DATA(TSIF2_TS_XX5_MARK, MSEL2CR_MSEL14_1, MSEL2CR_MSEL13_0,
		MSEL2CR_MSEL12_0),
	PINMUX_DATA(TSIF1_TS_XX1_MARK, MSEL2CR_MSEL11_0, MSEL2CR_MSEL10_0,
		MSEL2CR_MSEL9_0),
	PINMUX_DATA(TSIF1_TS_XX2_MARK, MSEL2CR_MSEL11_0, MSEL2CR_MSEL10_0,
		MSEL2CR_MSEL9_1),
	PINMUX_DATA(TSIF1_TS_XX3_MARK, MSEL2CR_MSEL11_0, MSEL2CR_MSEL10_1,
		MSEL2CR_MSEL9_0),
	PINMUX_DATA(TSIF1_TS_XX4_MARK, MSEL2CR_MSEL11_0, MSEL2CR_MSEL10_1,
		MSEL2CR_MSEL9_1),
	PINMUX_DATA(TSIF1_TS_XX5_MARK, MSEL2CR_MSEL11_1, MSEL2CR_MSEL10_0,
		MSEL2CR_MSEL9_0),
	PINMUX_DATA(TSIF0_TS_XX1_MARK, MSEL2CR_MSEL8_0, MSEL2CR_MSEL7_0,
		MSEL2CR_MSEL6_0),
	PINMUX_DATA(TSIF0_TS_XX2_MARK, MSEL2CR_MSEL8_0, MSEL2CR_MSEL7_0,
		MSEL2CR_MSEL6_1),
	PINMUX_DATA(TSIF0_TS_XX3_MARK, MSEL2CR_MSEL8_0, MSEL2CR_MSEL7_1,
		MSEL2CR_MSEL6_0),
	PINMUX_DATA(TSIF0_TS_XX4_MARK, MSEL2CR_MSEL8_0, MSEL2CR_MSEL7_1,
		MSEL2CR_MSEL6_1),
	PINMUX_DATA(TSIF0_TS_XX5_MARK, MSEL2CR_MSEL8_1, MSEL2CR_MSEL7_0,
		MSEL2CR_MSEL6_0),
	PINMUX_DATA(MST1_TS_XX1_MARK, MSEL2CR_MSEL5_0, MSEL2CR_MSEL4_0,
		MSEL2CR_MSEL3_0),
	PINMUX_DATA(MST1_TS_XX2_MARK, MSEL2CR_MSEL5_0, MSEL2CR_MSEL4_0,
		MSEL2CR_MSEL3_1),
	PINMUX_DATA(MST1_TS_XX3_MARK, MSEL2CR_MSEL5_0, MSEL2CR_MSEL4_1,
		MSEL2CR_MSEL3_0),
	PINMUX_DATA(MST1_TS_XX4_MARK, MSEL2CR_MSEL5_0, MSEL2CR_MSEL4_1,
		MSEL2CR_MSEL3_1),
	PINMUX_DATA(MST1_TS_XX5_MARK, MSEL2CR_MSEL5_1, MSEL2CR_MSEL4_0,
		MSEL2CR_MSEL3_0),
	PINMUX_DATA(MST0_TS_XX1_MARK, MSEL2CR_MSEL2_0, MSEL2CR_MSEL1_0,
		MSEL2CR_MSEL0_0),
	PINMUX_DATA(MST0_TS_XX2_MARK, MSEL2CR_MSEL2_0, MSEL2CR_MSEL1_0,
		MSEL2CR_MSEL0_1),
	PINMUX_DATA(MST0_TS_XX3_MARK, MSEL2CR_MSEL2_0, MSEL2CR_MSEL1_1,
		MSEL2CR_MSEL0_0),
	PINMUX_DATA(MST0_TS_XX4_MARK, MSEL2CR_MSEL2_0, MSEL2CR_MSEL1_1,
		MSEL2CR_MSEL0_1),
	PINMUX_DATA(MST0_TS_XX5_MARK, MSEL2CR_MSEL2_1, MSEL2CR_MSEL1_0,
		MSEL2CR_MSEL0_0),

	/* MSEL3 special cases */
	PINMUX_DATA(SDHI0_VCCQ_MC0_ON_MARK, MSEL3CR_MSEL28_1),
	PINMUX_DATA(SDHI0_VCCQ_MC0_OFF_MARK, MSEL3CR_MSEL28_0),
	PINMUX_DATA(DEBUG_MON_VIO_MARK, MSEL3CR_MSEL15_0),
	PINMUX_DATA(DEBUG_MON_LCDD_MARK, MSEL3CR_MSEL15_1),
	PINMUX_DATA(LCDC_LCDC0_MARK, MSEL3CR_MSEL6_0),
	PINMUX_DATA(LCDC_LCDC1_MARK, MSEL3CR_MSEL6_1),

	/* MSEL4 special cases */
	PINMUX_DATA(IRQ9_MEM_INT_MARK, MSEL4CR_MSEL29_0),
	PINMUX_DATA(IRQ9_MCP_INT_MARK, MSEL4CR_MSEL29_1),
	PINMUX_DATA(A11_MARK, MSEL4CR_MSEL13_0, MSEL4CR_MSEL12_0),
	PINMUX_DATA(KEYOUT8_MARK, MSEL4CR_MSEL13_0, MSEL4CR_MSEL12_1),
	PINMUX_DATA(TPU4TO3_MARK, MSEL4CR_MSEL13_1, MSEL4CR_MSEL12_0),
	PINMUX_DATA(RESETA_N_PU_ON_MARK, MSEL4CR_MSEL4_0),
	PINMUX_DATA(RESETA_N_PU_OFF_MARK, MSEL4CR_MSEL4_1),
	PINMUX_DATA(EDBGREQ_PD_MARK, MSEL4CR_MSEL1_0),
	PINMUX_DATA(EDBGREQ_PU_MARK, MSEL4CR_MSEL1_1),

	/* Functions with pull-ups */
	PINMUX_DATA(KEYIN0_PU_MARK, PORT66_FN2, PORT66_IN_PU),
	PINMUX_DATA(KEYIN1_PU_MARK, PORT67_FN2, PORT67_IN_PU),
	PINMUX_DATA(KEYIN2_PU_MARK, PORT68_FN2, PORT68_IN_PU),
	PINMUX_DATA(KEYIN3_PU_MARK, PORT69_FN2, PORT69_IN_PU),
	PINMUX_DATA(KEYIN4_PU_MARK, PORT70_FN2, PORT70_IN_PU),
	PINMUX_DATA(KEYIN5_PU_MARK, PORT71_FN2, PORT71_IN_PU),
	PINMUX_DATA(KEYIN6_PU_MARK, PORT72_FN2, PORT72_IN_PU),
	PINMUX_DATA(KEYIN7_PU_MARK, PORT73_FN2, PORT73_IN_PU),

	PINMUX_DATA(SDHICD0_PU_MARK,  PORT251_FN1, PORT251_IN_PU),
	PINMUX_DATA(SDHID0_0_PU_MARK, PORT252_FN1, PORT252_IN_PU),
	PINMUX_DATA(SDHID0_1_PU_MARK, PORT253_FN1, PORT253_IN_PU),
	PINMUX_DATA(SDHID0_2_PU_MARK, PORT254_FN1, PORT254_IN_PU),
	PINMUX_DATA(SDHID0_3_PU_MARK, PORT255_FN1, PORT255_IN_PU),
	PINMUX_DATA(SDHICMD0_PU_MARK, PORT256_FN1, PORT256_IN_PU),
	PINMUX_DATA(SDHIWP0_PU_MARK,  PORT257_FN1, PORT256_IN_PU),
	PINMUX_DATA(SDHID1_0_PU_MARK, PORT259_FN1, PORT259_IN_PU),
	PINMUX_DATA(SDHID1_1_PU_MARK, PORT260_FN1, PORT260_IN_PU),
	PINMUX_DATA(SDHID1_2_PU_MARK, PORT261_FN1, PORT261_IN_PU),
	PINMUX_DATA(SDHID1_3_PU_MARK, PORT262_FN1, PORT262_IN_PU),
	PINMUX_DATA(SDHICMD1_PU_MARK, PORT263_FN1, PORT263_IN_PU),
	PINMUX_DATA(SDHID2_0_PU_MARK, PORT265_FN1, PORT265_IN_PU),
	PINMUX_DATA(SDHID2_1_PU_MARK, PORT266_FN1, PORT266_IN_PU),
	PINMUX_DATA(SDHID2_2_PU_MARK, PORT267_FN1, PORT267_IN_PU),
	PINMUX_DATA(SDHID2_3_PU_MARK, PORT268_FN1, PORT268_IN_PU),
	PINMUX_DATA(SDHICMD2_PU_MARK, PORT269_FN1, PORT269_IN_PU),

	PINMUX_DATA(MMCCMD0_PU_MARK, PORT279_FN1, PORT279_IN_PU,
		MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCCMD1_PU_MARK, PORT297_FN2, PORT297_IN_PU,
		MSEL4CR_MSEL15_1),

	PINMUX_DATA(MMCD0_0_PU_MARK,
		    PORT271_FN1, PORT271_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_1_PU_MARK,
		    PORT272_FN1, PORT272_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_2_PU_MARK,
		    PORT273_FN1, PORT273_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_3_PU_MARK,
		    PORT274_FN1, PORT274_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_4_PU_MARK,
		    PORT275_FN1, PORT275_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_5_PU_MARK,
		    PORT276_FN1, PORT276_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_6_PU_MARK,
		    PORT277_FN1, PORT277_IN_PU, MSEL4CR_MSEL15_0),
	PINMUX_DATA(MMCD0_7_PU_MARK,
		    PORT278_FN1, PORT278_IN_PU, MSEL4CR_MSEL15_0),

	PINMUX_DATA(FSIBISLD_PU_MARK, PORT39_FN1, PORT39_IN_PU),
	PINMUX_DATA(FSIACK_PU_MARK, PORT49_FN1, PORT49_IN_PU),
	PINMUX_DATA(FSIAILR_PU_MARK, PORT50_FN5, PORT50_IN_PU),
	PINMUX_DATA(FSIAIBT_PU_MARK, PORT51_FN5, PORT51_IN_PU),
	PINMUX_DATA(FSIAISLD_PU_MARK, PORT55_FN1, PORT55_IN_PU),
};

static struct pinmux_gpio pinmux_gpios[] = {
	GPIO_PORT_ALL(),

	/* Table 25-1 (Functions 0-7) */
	GPIO_FN(VBUS_0),
	GPIO_FN(GPI0),
	GPIO_FN(GPI1),
	GPIO_FN(GPI2),
	GPIO_FN(GPI3),
	GPIO_FN(GPI4),
	GPIO_FN(GPI5),
	GPIO_FN(GPI6),
	GPIO_FN(GPI7),
	GPIO_FN(SCIFA7_RXD),
	GPIO_FN(SCIFA7_CTS_),
	GPIO_FN(GPO7), \
	GPIO_FN(MFG0_OUT2),
	GPIO_FN(GPO6), \
	GPIO_FN(MFG1_OUT2),
	GPIO_FN(GPO5), \
	GPIO_FN(SCIFA0_SCK), \
	GPIO_FN(FSICOSLDT3), \
	GPIO_FN(PORT16_VIO_CKOR),
	GPIO_FN(SCIFA0_TXD),
	GPIO_FN(SCIFA7_TXD),
	GPIO_FN(SCIFA7_RTS_), \
	GPIO_FN(PORT19_VIO_CKO2),
	GPIO_FN(GPO0),
	GPIO_FN(GPO1),
	GPIO_FN(GPO2), \
	GPIO_FN(STATUS0),
	GPIO_FN(GPO3), \
	GPIO_FN(STATUS1),
	GPIO_FN(GPO4), \
	GPIO_FN(STATUS2),
	GPIO_FN(VINT),
	GPIO_FN(TCKON),
	GPIO_FN(XDVFS1), \
	GPIO_FN(PORT27_I2C_SCL2), \
	GPIO_FN(PORT27_I2C_SCL3), \
	GPIO_FN(MFG0_OUT1), \
	GPIO_FN(PORT27_IROUT),
	GPIO_FN(XDVFS2), \
	GPIO_FN(PORT28_I2C_SDA2), \
	GPIO_FN(PORT28_I2C_SDA3), \
	GPIO_FN(PORT28_TPU1TO1),
	GPIO_FN(SIM_RST), \
	GPIO_FN(PORT29_TPU1TO1),
	GPIO_FN(SIM_CLK), \
	GPIO_FN(PORT30_VIO_CKOR),
	GPIO_FN(SIM_D), \
	GPIO_FN(PORT31_IROUT),
	GPIO_FN(SCIFA4_TXD),
	GPIO_FN(SCIFA4_RXD), \
	GPIO_FN(XWUP),
	GPIO_FN(SCIFA4_RTS_),
	GPIO_FN(SCIFA4_CTS_),
	GPIO_FN(FSIBOBT), \
	GPIO_FN(FSIBIBT),
	GPIO_FN(FSIBOLR), \
	GPIO_FN(FSIBILR),
	GPIO_FN(FSIBOSLD),
	GPIO_FN(FSIBISLD),
	GPIO_FN(VACK),
	GPIO_FN(XTAL1L),
	GPIO_FN(SCIFA0_RTS_), \
	GPIO_FN(FSICOSLDT2),
	GPIO_FN(SCIFA0_RXD),
	GPIO_FN(SCIFA0_CTS_), \
	GPIO_FN(FSICOSLDT1),
	GPIO_FN(FSICOBT), \
	GPIO_FN(FSICIBT), \
	GPIO_FN(FSIDOBT), \
	GPIO_FN(FSIDIBT),
	GPIO_FN(FSICOLR), \
	GPIO_FN(FSICILR), \
	GPIO_FN(FSIDOLR), \
	GPIO_FN(FSIDILR),
	GPIO_FN(FSICOSLD), \
	GPIO_FN(PORT47_FSICSPDIF),
	GPIO_FN(FSICISLD), \
	GPIO_FN(FSIDISLD),
	GPIO_FN(FSIACK), \
	GPIO_FN(PORT49_IRDA_OUT), \
	GPIO_FN(PORT49_IROUT), \
	GPIO_FN(FSIAOMC),
	GPIO_FN(FSIAOLR), \
	GPIO_FN(BBIF2_TSYNC2), \
	GPIO_FN(TPU2TO2), \
	GPIO_FN(FSIAILR),

	GPIO_FN(FSIAOBT), \
	GPIO_FN(BBIF2_TSCK2), \
	GPIO_FN(TPU2TO3), \
	GPIO_FN(FSIAIBT),
	GPIO_FN(FSIAOSLD), \
	GPIO_FN(BBIF2_TXD2),
	GPIO_FN(FSIASPDIF), \
	GPIO_FN(PORT53_IRDA_IN), \
	GPIO_FN(TPU3TO3), \
	GPIO_FN(FSIBSPDIF), \
	GPIO_FN(PORT53_FSICSPDIF),
	GPIO_FN(FSIBCK), \
	GPIO_FN(PORT54_IRDA_FIRSEL), \
	GPIO_FN(TPU3TO2), \
	GPIO_FN(FSIBOMC), \
	GPIO_FN(FSICCK), \
	GPIO_FN(FSICOMC),
	GPIO_FN(FSIAISLD), \
	GPIO_FN(TPU0TO0),
	GPIO_FN(A0), \
	GPIO_FN(BS_),
	GPIO_FN(A12), \
	GPIO_FN(PORT58_KEYOUT7), \
	GPIO_FN(TPU4TO2),
	GPIO_FN(A13), \
	GPIO_FN(PORT59_KEYOUT6), \
	GPIO_FN(TPU0TO1),
	GPIO_FN(A14), \
	GPIO_FN(KEYOUT5),
	GPIO_FN(A15), \
	GPIO_FN(KEYOUT4),
	GPIO_FN(A16), \
	GPIO_FN(KEYOUT3), \
	GPIO_FN(MSIOF0_SS1),
	GPIO_FN(A17), \
	GPIO_FN(KEYOUT2), \
	GPIO_FN(MSIOF0_TSYNC),
	GPIO_FN(A18), \
	GPIO_FN(KEYOUT1), \
	GPIO_FN(MSIOF0_TSCK),
	GPIO_FN(A19), \
	GPIO_FN(KEYOUT0), \
	GPIO_FN(MSIOF0_TXD),
	GPIO_FN(A20), \
	GPIO_FN(KEYIN0), \
	GPIO_FN(MSIOF0_RSCK),
	GPIO_FN(A21), \
	GPIO_FN(KEYIN1), \
	GPIO_FN(MSIOF0_RSYNC),
	GPIO_FN(A22), \
	GPIO_FN(KEYIN2), \
	GPIO_FN(MSIOF0_MCK0),
	GPIO_FN(A23), \
	GPIO_FN(KEYIN3), \
	GPIO_FN(MSIOF0_MCK1),
	GPIO_FN(A24), \
	GPIO_FN(KEYIN4), \
	GPIO_FN(MSIOF0_RXD),
	GPIO_FN(A25), \
	GPIO_FN(KEYIN5), \
	GPIO_FN(MSIOF0_SS2),
	GPIO_FN(A26), \
	GPIO_FN(KEYIN6),
	GPIO_FN(KEYIN7),
	GPIO_FN(D0_NAF0),
	GPIO_FN(D1_NAF1),
	GPIO_FN(D2_NAF2),
	GPIO_FN(D3_NAF3),
	GPIO_FN(D4_NAF4),
	GPIO_FN(D5_NAF5),
	GPIO_FN(D6_NAF6),
	GPIO_FN(D7_NAF7),
	GPIO_FN(D8_NAF8),
	GPIO_FN(D9_NAF9),
	GPIO_FN(D10_NAF10),
	GPIO_FN(D11_NAF11),
	GPIO_FN(D12_NAF12),
	GPIO_FN(D13_NAF13),
	GPIO_FN(D14_NAF14),
	GPIO_FN(D15_NAF15),
	GPIO_FN(CS4_),
	GPIO_FN(CS5A_), \
	GPIO_FN(PORT91_RDWR),
	GPIO_FN(CS5B_), \
	GPIO_FN(FCE1_),
	GPIO_FN(CS6B_), \
	GPIO_FN(DACK0),
	GPIO_FN(FCE0_), \
	GPIO_FN(CS6A_),
	GPIO_FN(WAIT_), \
	GPIO_FN(DREQ0),
	GPIO_FN(RD__FSC),
	GPIO_FN(WE0__FWE), \
	GPIO_FN(RDWR_FWE),
	GPIO_FN(WE1_),
	GPIO_FN(FRB),
	GPIO_FN(CKO),
	GPIO_FN(NBRSTOUT_),
	GPIO_FN(NBRST_),
	GPIO_FN(BBIF2_TXD),
	GPIO_FN(BBIF2_RXD),
	GPIO_FN(BBIF2_SYNC),
	GPIO_FN(BBIF2_SCK),
	GPIO_FN(SCIFA3_CTS_), \
	GPIO_FN(MFG3_IN2),
	GPIO_FN(SCIFA3_RXD), \
	GPIO_FN(MFG3_IN1),
	GPIO_FN(BBIF1_SS2), \
	GPIO_FN(SCIFA3_RTS_), \
	GPIO_FN(MFG3_OUT1),
	GPIO_FN(SCIFA3_TXD),
	GPIO_FN(HSI_RX_DATA), \
	GPIO_FN(BBIF1_RXD),
	GPIO_FN(HSI_TX_WAKE), \
	GPIO_FN(BBIF1_TSCK),
	GPIO_FN(HSI_TX_DATA), \
	GPIO_FN(BBIF1_TSYNC),
	GPIO_FN(HSI_TX_READY), \
	GPIO_FN(BBIF1_TXD),
	GPIO_FN(HSI_RX_READY), \
	GPIO_FN(BBIF1_RSCK), \
	GPIO_FN(PORT115_I2C_SCL2), \
	GPIO_FN(PORT115_I2C_SCL3),
	GPIO_FN(HSI_RX_WAKE), \
	GPIO_FN(BBIF1_RSYNC), \
	GPIO_FN(PORT116_I2C_SDA2), \
	GPIO_FN(PORT116_I2C_SDA3),
	GPIO_FN(HSI_RX_FLAG), \
	GPIO_FN(BBIF1_SS1), \
	GPIO_FN(BBIF1_FLOW),
	GPIO_FN(HSI_TX_FLAG),
	GPIO_FN(VIO_VD), \
	GPIO_FN(PORT128_LCD2VSYN), \
	GPIO_FN(VIO2_VD), \
	GPIO_FN(LCD2D0),

	GPIO_FN(VIO_HD), \
	GPIO_FN(PORT129_LCD2HSYN), \
	GPIO_FN(PORT129_LCD2CS_), \
	GPIO_FN(VIO2_HD), \
	GPIO_FN(LCD2D1),
	GPIO_FN(VIO_D0), \
	GPIO_FN(PORT130_MSIOF2_RXD), \
	GPIO_FN(LCD2D10),
	GPIO_FN(VIO_D1), \
	GPIO_FN(PORT131_KEYOUT6), \
	GPIO_FN(PORT131_MSIOF2_SS1), \
	GPIO_FN(PORT131_KEYOUT11), \
	GPIO_FN(LCD2D11),
	GPIO_FN(VIO_D2), \
	GPIO_FN(PORT132_KEYOUT7), \
	GPIO_FN(PORT132_MSIOF2_SS2), \
	GPIO_FN(PORT132_KEYOUT10), \
	GPIO_FN(LCD2D12),
	GPIO_FN(VIO_D3), \
	GPIO_FN(MSIOF2_TSYNC), \
	GPIO_FN(LCD2D13),
	GPIO_FN(VIO_D4), \
	GPIO_FN(MSIOF2_TXD), \
	GPIO_FN(LCD2D14),
	GPIO_FN(VIO_D5), \
	GPIO_FN(MSIOF2_TSCK), \
	GPIO_FN(LCD2D15),
	GPIO_FN(VIO_D6), \
	GPIO_FN(PORT136_KEYOUT8), \
	GPIO_FN(LCD2D16),
	GPIO_FN(VIO_D7), \
	GPIO_FN(PORT137_KEYOUT9), \
	GPIO_FN(LCD2D17),
	GPIO_FN(VIO_D8), \
	GPIO_FN(PORT138_KEYOUT8), \
	GPIO_FN(VIO2_D0), \
	GPIO_FN(LCD2D6),
	GPIO_FN(VIO_D9), \
	GPIO_FN(PORT139_KEYOUT9), \
	GPIO_FN(VIO2_D1), \
	GPIO_FN(LCD2D7),
	GPIO_FN(VIO_D10), \
	GPIO_FN(TPU0TO2), \
	GPIO_FN(VIO2_D2), \
	GPIO_FN(LCD2D8),
	GPIO_FN(VIO_D11), \
	GPIO_FN(TPU0TO3), \
	GPIO_FN(VIO2_D3), \
	GPIO_FN(LCD2D9),
	GPIO_FN(VIO_D12), \
	GPIO_FN(PORT142_KEYOUT10), \
	GPIO_FN(VIO2_D4), \
	GPIO_FN(LCD2D2),
	GPIO_FN(VIO_D13), \
	GPIO_FN(PORT143_KEYOUT11), \
	GPIO_FN(PORT143_KEYOUT6), \
	GPIO_FN(VIO2_D5), \
	GPIO_FN(LCD2D3),
	GPIO_FN(VIO_D14), \
	GPIO_FN(PORT144_KEYOUT7), \
	GPIO_FN(VIO2_D6), \
	GPIO_FN(LCD2D4),
	GPIO_FN(VIO_D15), \
	GPIO_FN(TPU1TO3), \
	GPIO_FN(PORT145_LCD2DISP), \
	GPIO_FN(PORT145_LCD2RS), \
	GPIO_FN(VIO2_D7), \
	GPIO_FN(LCD2D5),
	GPIO_FN(VIO_CLK), \
	GPIO_FN(LCD2DCK), \
	GPIO_FN(PORT146_LCD2WR_), \
	GPIO_FN(VIO2_CLK), \
	GPIO_FN(LCD2D18),
	GPIO_FN(VIO_FIELD), \
	GPIO_FN(LCD2RD_), \
	GPIO_FN(VIO2_FIELD), \
	GPIO_FN(LCD2D19),
	GPIO_FN(VIO_CKO),
	GPIO_FN(A27), \
	GPIO_FN(PORT149_RDWR), \
	GPIO_FN(MFG0_IN1), \
	GPIO_FN(PORT149_KEYOUT9),
	GPIO_FN(MFG0_IN2),
	GPIO_FN(TS_SPSYNC3), \
	GPIO_FN(MSIOF2_RSCK),
	GPIO_FN(TS_SDAT3), \
	GPIO_FN(MSIOF2_RSYNC),
	GPIO_FN(TPU1TO2), \
	GPIO_FN(TS_SDEN3), \
	GPIO_FN(PORT153_MSIOF2_SS1),
	GPIO_FN(SCIFA2_TXD1), \
	GPIO_FN(MSIOF2_MCK0),
	GPIO_FN(SCIFA2_RXD1), \
	GPIO_FN(MSIOF2_MCK1),
	GPIO_FN(SCIFA2_RTS1_), \
	GPIO_FN(PORT156_MSIOF2_SS2),
	GPIO_FN(SCIFA2_CTS1_), \
	GPIO_FN(PORT157_MSIOF2_RXD),
	GPIO_FN(DINT_), \
	GPIO_FN(SCIFA2_SCK1), \
	GPIO_FN(TS_SCK3),
	GPIO_FN(PORT159_SCIFB_SCK), \
	GPIO_FN(PORT159_SCIFA5_SCK), \
	GPIO_FN(NMI),
	GPIO_FN(PORT160_SCIFB_TXD), \
	GPIO_FN(PORT160_SCIFA5_TXD),
	GPIO_FN(PORT161_SCIFB_CTS_), \
	GPIO_FN(PORT161_SCIFA5_CTS_),
	GPIO_FN(PORT162_SCIFB_RXD), \
	GPIO_FN(PORT162_SCIFA5_RXD),
	GPIO_FN(PORT163_SCIFB_RTS_), \
	GPIO_FN(PORT163_SCIFA5_RTS_), \
	GPIO_FN(TPU3TO0),
	GPIO_FN(LCDD0),
	GPIO_FN(LCDD1), \
	GPIO_FN(PORT193_SCIFA5_CTS_), \
	GPIO_FN(BBIF2_TSYNC1),
	GPIO_FN(LCDD2), \
	GPIO_FN(PORT194_SCIFA5_RTS_), \
	GPIO_FN(BBIF2_TSCK1),
	GPIO_FN(LCDD3), \
	GPIO_FN(PORT195_SCIFA5_RXD), \
	GPIO_FN(BBIF2_TXD1),
	GPIO_FN(LCDD4), \
	GPIO_FN(PORT196_SCIFA5_TXD),
	GPIO_FN(LCDD5), \
	GPIO_FN(PORT197_SCIFA5_SCK), \
	GPIO_FN(MFG2_OUT2), \
	GPIO_FN(TPU2TO1),
	GPIO_FN(LCDD6),
	GPIO_FN(LCDD7), \
	GPIO_FN(TPU4TO1), \
	GPIO_FN(MFG4_OUT2),
	GPIO_FN(LCDD8), \
	GPIO_FN(D16),
	GPIO_FN(LCDD9), \
	GPIO_FN(D17),
	GPIO_FN(LCDD10), \
	GPIO_FN(D18),
	GPIO_FN(LCDD11), \
	GPIO_FN(D19),
	GPIO_FN(LCDD12), \
	GPIO_FN(D20),
	GPIO_FN(LCDD13), \
	GPIO_FN(D21),
	GPIO_FN(LCDD14), \
	GPIO_FN(D22),
	GPIO_FN(LCDD15), \
	GPIO_FN(PORT207_MSIOF0L_SS1), \
	GPIO_FN(D23),
	GPIO_FN(LCDD16), \
	GPIO_FN(PORT208_MSIOF0L_SS2), \
	GPIO_FN(D24),
	GPIO_FN(LCDD17), \
	GPIO_FN(D25),
	GPIO_FN(LCDD18), \
	GPIO_FN(DREQ2), \
	GPIO_FN(PORT210_MSIOF0L_SS1), \
	GPIO_FN(D26),
	GPIO_FN(LCDD19), \
	GPIO_FN(PORT211_MSIOF0L_SS2), \
	GPIO_FN(D27),
	GPIO_FN(LCDD20), \
	GPIO_FN(TS_SPSYNC1), \
	GPIO_FN(MSIOF0L_MCK0), \
	GPIO_FN(D28),
	GPIO_FN(LCDD21), \
	GPIO_FN(TS_SDAT1), \
	GPIO_FN(MSIOF0L_MCK1), \
	GPIO_FN(D29),
	GPIO_FN(LCDD22), \
	GPIO_FN(TS_SDEN1), \
	GPIO_FN(MSIOF0L_RSCK), \
	GPIO_FN(D30),
	GPIO_FN(LCDD23), \
	GPIO_FN(TS_SCK1), \
	GPIO_FN(MSIOF0L_RSYNC), \
	GPIO_FN(D31),
	GPIO_FN(LCDDCK), \
	GPIO_FN(LCDWR_),
	GPIO_FN(LCDRD_), \
	GPIO_FN(DACK2), \
	GPIO_FN(PORT217_LCD2RS), \
	GPIO_FN(MSIOF0L_TSYNC), \
	GPIO_FN(VIO2_FIELD3), \
	GPIO_FN(PORT217_LCD2DISP),
	GPIO_FN(LCDHSYN), \
	GPIO_FN(LCDCS_), \
	GPIO_FN(LCDCS2_), \
	GPIO_FN(DACK3), \
	GPIO_FN(PORT218_VIO_CKOR),
	GPIO_FN(LCDDISP), \
	GPIO_FN(LCDRS), \
	GPIO_FN(PORT219_LCD2WR_), \
	GPIO_FN(DREQ3), \
	GPIO_FN(MSIOF0L_TSCK), \
	GPIO_FN(VIO2_CLK3), \
	GPIO_FN(LCD2DCK_2),
	GPIO_FN(LCDVSYN), \
	GPIO_FN(LCDVSYN2),
	GPIO_FN(LCDLCLK), \
	GPIO_FN(DREQ1), \
	GPIO_FN(PORT221_LCD2CS_), \
	GPIO_FN(PWEN), \
	GPIO_FN(MSIOF0L_RXD), \
	GPIO_FN(VIO2_HD3), \
	GPIO_FN(PORT221_LCD2HSYN),
	GPIO_FN(LCDDON), \
	GPIO_FN(LCDDON2), \
	GPIO_FN(DACK1), \
	GPIO_FN(OVCN), \
	GPIO_FN(MSIOF0L_TXD), \
	GPIO_FN(VIO2_VD3), \
	GPIO_FN(PORT222_LCD2VSYN),

	GPIO_FN(SCIFA1_TXD), \
	GPIO_FN(OVCN2),
	GPIO_FN(EXTLP), \
	GPIO_FN(SCIFA1_SCK), \
	GPIO_FN(PORT226_VIO_CKO2),
	GPIO_FN(SCIFA1_RTS_), \
	GPIO_FN(IDIN),
	GPIO_FN(SCIFA1_RXD),
	GPIO_FN(SCIFA1_CTS_), \
	GPIO_FN(MFG1_IN1),
	GPIO_FN(MSIOF1_TXD), \
	GPIO_FN(SCIFA2_TXD2),
	GPIO_FN(MSIOF1_TSYNC), \
	GPIO_FN(SCIFA2_CTS2_),
	GPIO_FN(MSIOF1_TSCK), \
	GPIO_FN(SCIFA2_SCK2),
	GPIO_FN(MSIOF1_RXD), \
	GPIO_FN(SCIFA2_RXD2),
	GPIO_FN(MSIOF1_RSCK), \
	GPIO_FN(SCIFA2_RTS2_), \
	GPIO_FN(VIO2_CLK2), \
	GPIO_FN(LCD2D20),
	GPIO_FN(MSIOF1_RSYNC), \
	GPIO_FN(MFG1_IN2), \
	GPIO_FN(VIO2_VD2), \
	GPIO_FN(LCD2D21),
	GPIO_FN(MSIOF1_MCK0), \
	GPIO_FN(PORT236_I2C_SDA2),
	GPIO_FN(MSIOF1_MCK1), \
	GPIO_FN(PORT237_I2C_SCL2),
	GPIO_FN(MSIOF1_SS1), \
	GPIO_FN(VIO2_FIELD2), \
	GPIO_FN(LCD2D22),
	GPIO_FN(MSIOF1_SS2), \
	GPIO_FN(VIO2_HD2), \
	GPIO_FN(LCD2D23),
	GPIO_FN(SCIFA6_TXD),
	GPIO_FN(PORT241_IRDA_OUT), \
	GPIO_FN(PORT241_IROUT), \
	GPIO_FN(MFG4_OUT1), \
	GPIO_FN(TPU4TO0),
	GPIO_FN(PORT242_IRDA_IN), \
	GPIO_FN(MFG4_IN2),
	GPIO_FN(PORT243_IRDA_FIRSEL), \
	GPIO_FN(PORT243_VIO_CKO2),
	GPIO_FN(PORT244_SCIFA5_CTS_), \
	GPIO_FN(MFG2_IN1), \
	GPIO_FN(PORT244_SCIFB_CTS_), \
	GPIO_FN(MSIOF2R_RXD),
	GPIO_FN(PORT245_SCIFA5_RTS_), \
	GPIO_FN(MFG2_IN2), \
	GPIO_FN(PORT245_SCIFB_RTS_), \
	GPIO_FN(MSIOF2R_TXD),
	GPIO_FN(PORT246_SCIFA5_RXD), \
	GPIO_FN(MFG1_OUT1), \
	GPIO_FN(PORT246_SCIFB_RXD), \
	GPIO_FN(TPU1TO0),
	GPIO_FN(PORT247_SCIFA5_TXD), \
	GPIO_FN(MFG3_OUT2), \
	GPIO_FN(PORT247_SCIFB_TXD), \
	GPIO_FN(TPU3TO1),
	GPIO_FN(PORT248_SCIFA5_SCK), \
	GPIO_FN(MFG2_OUT1), \
	GPIO_FN(PORT248_SCIFB_SCK), \
	GPIO_FN(TPU2TO0), \
	GPIO_FN(PORT248_I2C_SCL3), \
	GPIO_FN(MSIOF2R_TSCK),
	GPIO_FN(PORT249_IROUT), \
	GPIO_FN(MFG4_IN1), \
	GPIO_FN(PORT249_I2C_SDA3), \
	GPIO_FN(MSIOF2R_TSYNC),
	GPIO_FN(SDHICLK0),
	GPIO_FN(SDHICD0),
	GPIO_FN(SDHID0_0),
	GPIO_FN(SDHID0_1),
	GPIO_FN(SDHID0_2),
	GPIO_FN(SDHID0_3),
	GPIO_FN(SDHICMD0),
	GPIO_FN(SDHIWP0),
	GPIO_FN(SDHICLK1),
	GPIO_FN(SDHID1_0), \
	GPIO_FN(TS_SPSYNC2),
	GPIO_FN(SDHID1_1), \
	GPIO_FN(TS_SDAT2),
	GPIO_FN(SDHID1_2), \
	GPIO_FN(TS_SDEN2),
	GPIO_FN(SDHID1_3), \
	GPIO_FN(TS_SCK2),
	GPIO_FN(SDHICMD1),
	GPIO_FN(SDHICLK2),
	GPIO_FN(SDHID2_0), \
	GPIO_FN(TS_SPSYNC4),
	GPIO_FN(SDHID2_1), \
	GPIO_FN(TS_SDAT4),
	GPIO_FN(SDHID2_2), \
	GPIO_FN(TS_SDEN4),
	GPIO_FN(SDHID2_3), \
	GPIO_FN(TS_SCK4),
	GPIO_FN(SDHICMD2),
	GPIO_FN(MMCCLK0),
	GPIO_FN(MMCD0_0),
	GPIO_FN(MMCD0_1),
	GPIO_FN(MMCD0_2),
	GPIO_FN(MMCD0_3),
	GPIO_FN(MMCD0_4), \
	GPIO_FN(TS_SPSYNC5),
	GPIO_FN(MMCD0_5), \
	GPIO_FN(TS_SDAT5),
	GPIO_FN(MMCD0_6), \
	GPIO_FN(TS_SDEN5),
	GPIO_FN(MMCD0_7), \
	GPIO_FN(TS_SCK5),
	GPIO_FN(MMCCMD0),
	GPIO_FN(RESETOUTS_), \
	GPIO_FN(EXTAL2OUT),
	GPIO_FN(MCP_WAIT__MCP_FRB),
	GPIO_FN(MCP_CKO), \
	GPIO_FN(MMCCLK1),
	GPIO_FN(MCP_D15_MCP_NAF15),
	GPIO_FN(MCP_D14_MCP_NAF14),
	GPIO_FN(MCP_D13_MCP_NAF13),
	GPIO_FN(MCP_D12_MCP_NAF12),
	GPIO_FN(MCP_D11_MCP_NAF11),
	GPIO_FN(MCP_D10_MCP_NAF10),
	GPIO_FN(MCP_D9_MCP_NAF9),
	GPIO_FN(MCP_D8_MCP_NAF8), \
	GPIO_FN(MMCCMD1),
	GPIO_FN(MCP_D7_MCP_NAF7), \
	GPIO_FN(MMCD1_7),

	GPIO_FN(MCP_D6_MCP_NAF6), \
	GPIO_FN(MMCD1_6),
	GPIO_FN(MCP_D5_MCP_NAF5), \
	GPIO_FN(MMCD1_5),
	GPIO_FN(MCP_D4_MCP_NAF4), \
	GPIO_FN(MMCD1_4),
	GPIO_FN(MCP_D3_MCP_NAF3), \
	GPIO_FN(MMCD1_3),
	GPIO_FN(MCP_D2_MCP_NAF2), \
	GPIO_FN(MMCD1_2),
	GPIO_FN(MCP_D1_MCP_NAF1), \
	GPIO_FN(MMCD1_1),
	GPIO_FN(MCP_D0_MCP_NAF0), \
	GPIO_FN(MMCD1_0),
	GPIO_FN(MCP_NBRSTOUT_),
	GPIO_FN(MCP_WE0__MCP_FWE), \
	GPIO_FN(MCP_RDWR_MCP_FWE),

	/* MSEL2 special cases */
	GPIO_FN(TSIF2_TS_XX1),
	GPIO_FN(TSIF2_TS_XX2),
	GPIO_FN(TSIF2_TS_XX3),
	GPIO_FN(TSIF2_TS_XX4),
	GPIO_FN(TSIF2_TS_XX5),
	GPIO_FN(TSIF1_TS_XX1),
	GPIO_FN(TSIF1_TS_XX2),
	GPIO_FN(TSIF1_TS_XX3),
	GPIO_FN(TSIF1_TS_XX4),
	GPIO_FN(TSIF1_TS_XX5),
	GPIO_FN(TSIF0_TS_XX1),
	GPIO_FN(TSIF0_TS_XX2),
	GPIO_FN(TSIF0_TS_XX3),
	GPIO_FN(TSIF0_TS_XX4),
	GPIO_FN(TSIF0_TS_XX5),
	GPIO_FN(MST1_TS_XX1),
	GPIO_FN(MST1_TS_XX2),
	GPIO_FN(MST1_TS_XX3),
	GPIO_FN(MST1_TS_XX4),
	GPIO_FN(MST1_TS_XX5),
	GPIO_FN(MST0_TS_XX1),
	GPIO_FN(MST0_TS_XX2),
	GPIO_FN(MST0_TS_XX3),
	GPIO_FN(MST0_TS_XX4),
	GPIO_FN(MST0_TS_XX5),

	/* MSEL3 special cases */
	GPIO_FN(SDHI0_VCCQ_MC0_ON),
	GPIO_FN(SDHI0_VCCQ_MC0_OFF),
	GPIO_FN(DEBUG_MON_VIO),
	GPIO_FN(DEBUG_MON_LCDD),
	GPIO_FN(LCDC_LCDC0),
	GPIO_FN(LCDC_LCDC1),

	/* MSEL4 special cases */
	GPIO_FN(IRQ9_MEM_INT),
	GPIO_FN(IRQ9_MCP_INT),
	GPIO_FN(A11),
	GPIO_FN(KEYOUT8),
	GPIO_FN(TPU4TO3),
	GPIO_FN(RESETA_N_PU_ON),
	GPIO_FN(RESETA_N_PU_OFF),
	GPIO_FN(EDBGREQ_PD),
	GPIO_FN(EDBGREQ_PU),

	/* Functions with pull-ups */
	GPIO_FN(KEYIN0_PU),
	GPIO_FN(KEYIN1_PU),
	GPIO_FN(KEYIN2_PU),
	GPIO_FN(KEYIN3_PU),
	GPIO_FN(KEYIN4_PU),
	GPIO_FN(KEYIN5_PU),
	GPIO_FN(KEYIN6_PU),
	GPIO_FN(KEYIN7_PU),
	GPIO_FN(SDHICD0_PU),
	GPIO_FN(SDHID0_0_PU),
	GPIO_FN(SDHID0_1_PU),
	GPIO_FN(SDHID0_2_PU),
	GPIO_FN(SDHID0_3_PU),
	GPIO_FN(SDHICMD0_PU),
	GPIO_FN(SDHIWP0_PU),
	GPIO_FN(SDHID1_0_PU),
	GPIO_FN(SDHID1_1_PU),
	GPIO_FN(SDHID1_2_PU),
	GPIO_FN(SDHID1_3_PU),
	GPIO_FN(SDHICMD1_PU),
	GPIO_FN(SDHID2_0_PU),
	GPIO_FN(SDHID2_1_PU),
	GPIO_FN(SDHID2_2_PU),
	GPIO_FN(SDHID2_3_PU),
	GPIO_FN(SDHICMD2_PU),
	GPIO_FN(MMCCMD0_PU),
	GPIO_FN(MMCCMD1_PU),
	GPIO_FN(MMCD0_0_PU),
	GPIO_FN(MMCD0_1_PU),
	GPIO_FN(MMCD0_2_PU),
	GPIO_FN(MMCD0_3_PU),
	GPIO_FN(MMCD0_4_PU),
	GPIO_FN(MMCD0_5_PU),
	GPIO_FN(MMCD0_6_PU),
	GPIO_FN(MMCD0_7_PU),
	GPIO_FN(FSIACK_PU),
	GPIO_FN(FSIAILR_PU),
	GPIO_FN(FSIAIBT_PU),
	GPIO_FN(FSIAISLD_PU),
};

static struct pinmux_cfg_reg pinmux_config_regs[] = {
	PORTCR(0, 0xe6050000), /* PORT0CR */
	PORTCR(1, 0xe6050001), /* PORT1CR */
	PORTCR(2, 0xe6050002), /* PORT2CR */
	PORTCR(3, 0xe6050003), /* PORT3CR */
	PORTCR(4, 0xe6050004), /* PORT4CR */
	PORTCR(5, 0xe6050005), /* PORT5CR */
	PORTCR(6, 0xe6050006), /* PORT6CR */
	PORTCR(7, 0xe6050007), /* PORT7CR */
	PORTCR(8, 0xe6050008), /* PORT8CR */
	PORTCR(9, 0xe6050009), /* PORT9CR */

	PORTCR(10, 0xe605000a), /* PORT10CR */
	PORTCR(11, 0xe605000b), /* PORT11CR */
	PORTCR(12, 0xe605000c), /* PORT12CR */
	PORTCR(13, 0xe605000d), /* PORT13CR */
	PORTCR(14, 0xe605000e), /* PORT14CR */
	PORTCR(15, 0xe605000f), /* PORT15CR */
	PORTCR(16, 0xe6050010), /* PORT16CR */
	PORTCR(17, 0xe6050011), /* PORT17CR */
	PORTCR(18, 0xe6050012), /* PORT18CR */
	PORTCR(19, 0xe6050013), /* PORT19CR */

	PORTCR(20, 0xe6050014), /* PORT20CR */
	PORTCR(21, 0xe6050015), /* PORT21CR */
	PORTCR(22, 0xe6050016), /* PORT22CR */
	PORTCR(23, 0xe6050017), /* PORT23CR */
	PORTCR(24, 0xe6050018), /* PORT24CR */
	PORTCR(25, 0xe6050019), /* PORT25CR */
	PORTCR(26, 0xe605001a), /* PORT26CR */
	PORTCR(27, 0xe605001b), /* PORT27CR */
	PORTCR(28, 0xe605001c), /* PORT28CR */
	PORTCR(29, 0xe605001d), /* PORT29CR */

	PORTCR(30, 0xe605001e), /* PORT30CR */
	PORTCR(31, 0xe605001f), /* PORT31CR */
	PORTCR(32, 0xe6051020), /* PORT32CR */
	PORTCR(33, 0xe6051021), /* PORT33CR */
	PORTCR(34, 0xe6051022), /* PORT34CR */
	PORTCR(35, 0xe6051023), /* PORT35CR */
	PORTCR(36, 0xe6051024), /* PORT36CR */
	PORTCR(37, 0xe6051025), /* PORT37CR */
	PORTCR(38, 0xe6051026), /* PORT38CR */
	PORTCR(39, 0xe6051027), /* PORT39CR */

	PORTCR(40, 0xe6051028), /* PORT40CR */
	PORTCR(41, 0xe6051029), /* PORT41CR */
	PORTCR(42, 0xe605102a), /* PORT42CR */
	PORTCR(43, 0xe605102b), /* PORT43CR */
	PORTCR(44, 0xe605102c), /* PORT44CR */
	PORTCR(45, 0xe605102d), /* PORT45CR */
	PORTCR(46, 0xe605102e), /* PORT46CR */
	PORTCR(47, 0xe605102f), /* PORT47CR */
	PORTCR(48, 0xe6051030), /* PORT48CR */
	PORTCR(49, 0xe6051031), /* PORT49CR */

	PORTCR(50, 0xe6051032), /* PORT50CR */
	PORTCR(51, 0xe6051033), /* PORT51CR */
	PORTCR(52, 0xe6051034), /* PORT52CR */
	PORTCR(53, 0xe6051035), /* PORT53CR */
	PORTCR(54, 0xe6051036), /* PORT54CR */
	PORTCR(55, 0xe6051037), /* PORT55CR */
	PORTCR(56, 0xe6051038), /* PORT56CR */
	PORTCR(57, 0xe6051039), /* PORT57CR */
	PORTCR(58, 0xe605103a), /* PORT58CR */
	PORTCR(59, 0xe605103b), /* PORT59CR */

	PORTCR(60, 0xe605103c), /* PORT60CR */
	PORTCR(61, 0xe605103d), /* PORT61CR */
	PORTCR(62, 0xe605103e), /* PORT62CR */
	PORTCR(63, 0xe605103f), /* PORT63CR */
	PORTCR(64, 0xe6051040), /* PORT64CR */
	PORTCR(65, 0xe6051041), /* PORT65CR */
	PORTCR(66, 0xe6051042), /* PORT66CR */
	PORTCR(67, 0xe6051043), /* PORT67CR */
	PORTCR(68, 0xe6051044), /* PORT68CR */
	PORTCR(69, 0xe6051045), /* PORT69CR */

	PORTCR(70, 0xe6051046), /* PORT70CR */
	PORTCR(71, 0xe6051047), /* PORT71CR */
	PORTCR(72, 0xe6051048), /* PORT72CR */
	PORTCR(73, 0xe6051049), /* PORT73CR */
	PORTCR(74, 0xe605104a), /* PORT74CR */
	PORTCR(75, 0xe605104b), /* PORT75CR */
	PORTCR(76, 0xe605104c), /* PORT76CR */
	PORTCR(77, 0xe605104d), /* PORT77CR */
	PORTCR(78, 0xe605104e), /* PORT78CR */
	PORTCR(79, 0xe605104f), /* PORT79CR */

	PORTCR(80, 0xe6051050), /* PORT80CR */
	PORTCR(81, 0xe6051051), /* PORT81CR */
	PORTCR(82, 0xe6051052), /* PORT82CR */
	PORTCR(83, 0xe6051053), /* PORT83CR */
	PORTCR(84, 0xe6051054), /* PORT84CR */
	PORTCR(85, 0xe6051055), /* PORT85CR */
	PORTCR(86, 0xe6051056), /* PORT86CR */
	PORTCR(87, 0xe6051057), /* PORT87CR */
	PORTCR(88, 0xe6051058), /* PORT88CR */
	PORTCR(89, 0xe6051059), /* PORT89CR */

	PORTCR(90, 0xe605105a), /* PORT90CR */
	PORTCR(91, 0xe605105b), /* PORT91CR */
	PORTCR(92, 0xe605105c), /* PORT92CR */
	PORTCR(93, 0xe605105d), /* PORT93CR */
	PORTCR(94, 0xe605105e), /* PORT94CR */
	PORTCR(95, 0xe605105f), /* PORT95CR */
	PORTCR(96, 0xe6052060), /* PORT96CR */
	PORTCR(97, 0xe6052061), /* PORT97CR */
	PORTCR(98, 0xe6052062), /* PORT98CR */
	PORTCR(99, 0xe6052063), /* PORT99CR */

	PORTCR(100, 0xe6052064), /* PORT100CR */
	PORTCR(101, 0xe6052065), /* PORT101CR */
	PORTCR(102, 0xe6052066), /* PORT102CR */
	PORTCR(103, 0xe6052067), /* PORT103CR */
	PORTCR(104, 0xe6052068), /* PORT104CR */
	PORTCR(105, 0xe6052069), /* PORT105CR */
	PORTCR(106, 0xe605206a), /* PORT106CR */
	PORTCR(107, 0xe605206b), /* PORT107CR */
	PORTCR(108, 0xe605206c), /* PORT108CR */
	PORTCR(109, 0xe605206d), /* PORT109CR */

	PORTCR(110, 0xe605206e), /* PORT110CR */
	PORTCR(111, 0xe605206f), /* PORT111CR */
	PORTCR(112, 0xe6052070), /* PORT112CR */
	PORTCR(113, 0xe6052071), /* PORT113CR */
	PORTCR(114, 0xe6052072), /* PORT114CR */
	PORTCR(115, 0xe6052073), /* PORT115CR */
	PORTCR(116, 0xe6052074), /* PORT116CR */
	PORTCR(117, 0xe6052075), /* PORT117CR */
	PORTCR(118, 0xe6052076), /* PORT118CR */

	PORTCR(128, 0xe6052080), /* PORT128CR */
	PORTCR(129, 0xe6052081), /* PORT129CR */

	PORTCR(130, 0xe6052082), /* PORT130CR */
	PORTCR(131, 0xe6052083), /* PORT131CR */
	PORTCR(132, 0xe6052084), /* PORT132CR */
	PORTCR(133, 0xe6052085), /* PORT133CR */
	PORTCR(134, 0xe6052086), /* PORT134CR */
	PORTCR(135, 0xe6052087), /* PORT135CR */
	PORTCR(136, 0xe6052088), /* PORT136CR */
	PORTCR(137, 0xe6052089), /* PORT137CR */
	PORTCR(138, 0xe605208a), /* PORT138CR */
	PORTCR(139, 0xe605208b), /* PORT139CR */

	PORTCR(140, 0xe605208c), /* PORT140CR */
	PORTCR(141, 0xe605208d), /* PORT141CR */
	PORTCR(142, 0xe605208e), /* PORT142CR */
	PORTCR(143, 0xe605208f), /* PORT143CR */
	PORTCR(144, 0xe6052090), /* PORT144CR */
	PORTCR(145, 0xe6052091), /* PORT145CR */
	PORTCR(146, 0xe6052092), /* PORT146CR */
	PORTCR(147, 0xe6052093), /* PORT147CR */
	PORTCR(148, 0xe6052094), /* PORT148CR */
	PORTCR(149, 0xe6052095), /* PORT149CR */

	PORTCR(150, 0xe6052096), /* PORT150CR */
	PORTCR(151, 0xe6052097), /* PORT151CR */
	PORTCR(152, 0xe6052098), /* PORT152CR */
	PORTCR(153, 0xe6052099), /* PORT153CR */
	PORTCR(154, 0xe605209a), /* PORT154CR */
	PORTCR(155, 0xe605209b), /* PORT155CR */
	PORTCR(156, 0xe605209c), /* PORT156CR */
	PORTCR(157, 0xe605209d), /* PORT157CR */
	PORTCR(158, 0xe605209e), /* PORT158CR */
	PORTCR(159, 0xe605209f), /* PORT159CR */

	PORTCR(160, 0xe60520a0), /* PORT160CR */
	PORTCR(161, 0xe60520a1), /* PORT161CR */
	PORTCR(162, 0xe60520a2), /* PORT162CR */
	PORTCR(163, 0xe60520a3), /* PORT163CR */
	PORTCR(164, 0xe60520a4), /* PORT164CR */

	PORTCR(192, 0xe60520c0), /* PORT192CR */
	PORTCR(193, 0xe60520c1), /* PORT193CR */
	PORTCR(194, 0xe60520c2), /* PORT194CR */
	PORTCR(195, 0xe60520c3), /* PORT195CR */
	PORTCR(196, 0xe60520c4), /* PORT196CR */
	PORTCR(197, 0xe60520c5), /* PORT197CR */
	PORTCR(198, 0xe60520c6), /* PORT198CR */
	PORTCR(199, 0xe60520c7), /* PORT199CR */

	PORTCR(200, 0xe60520c8), /* PORT200CR */
	PORTCR(201, 0xe60520c9), /* PORT201CR */
	PORTCR(202, 0xe60520ca), /* PORT202CR */
	PORTCR(203, 0xe60520cb), /* PORT203CR */
	PORTCR(204, 0xe60520cc), /* PORT204CR */
	PORTCR(205, 0xe60520cd), /* PORT205CR */
	PORTCR(206, 0xe60520ce), /* PORT206CR */
	PORTCR(207, 0xe60520cf), /* PORT207CR */
	PORTCR(208, 0xe60520d0), /* PORT208CR */
	PORTCR(209, 0xe60520d1), /* PORT209CR */

	PORTCR(210, 0xe60520d2), /* PORT210CR */
	PORTCR(211, 0xe60520d3), /* PORT211CR */
	PORTCR(212, 0xe60520d4), /* PORT212CR */
	PORTCR(213, 0xe60520d5), /* PORT213CR */
	PORTCR(214, 0xe60520d6), /* PORT214CR */
	PORTCR(215, 0xe60520d7), /* PORT215CR */
	PORTCR(216, 0xe60520d8), /* PORT216CR */
	PORTCR(217, 0xe60520d9), /* PORT217CR */
	PORTCR(218, 0xe60520da), /* PORT218CR */
	PORTCR(219, 0xe60520db), /* PORT219CR */

	PORTCR(220, 0xe60520dc), /* PORT220CR */
	PORTCR(221, 0xe60520dd), /* PORT221CR */
	PORTCR(222, 0xe60520de), /* PORT222CR */
	PORTCR(223, 0xe60520df), /* PORT223CR */
	PORTCR(224, 0xe60530e0), /* PORT224CR */
	PORTCR(225, 0xe60530e1), /* PORT225CR */
	PORTCR(226, 0xe60530e2), /* PORT226CR */
	PORTCR(227, 0xe60530e3), /* PORT227CR */
	PORTCR(228, 0xe60530e4), /* PORT228CR */
	PORTCR(229, 0xe60530e5), /* PORT229CR */

	PORTCR(230, 0xe60530e6), /* PORT230CR */
	PORTCR(231, 0xe60530e7), /* PORT231CR */
	PORTCR(232, 0xe60530e8), /* PORT232CR */
	PORTCR(233, 0xe60530e9), /* PORT233CR */
	PORTCR(234, 0xe60530ea), /* PORT234CR */
	PORTCR(235, 0xe60530eb), /* PORT235CR */
	PORTCR(236, 0xe60530ec), /* PORT236CR */
	PORTCR(237, 0xe60530ed), /* PORT237CR */
	PORTCR(238, 0xe60530ee), /* PORT238CR */
	PORTCR(239, 0xe60530ef), /* PORT239CR */

	PORTCR(240, 0xe60530f0), /* PORT240CR */
	PORTCR(241, 0xe60530f1), /* PORT241CR */
	PORTCR(242, 0xe60530f2), /* PORT242CR */
	PORTCR(243, 0xe60530f3), /* PORT243CR */
	PORTCR(244, 0xe60530f4), /* PORT244CR */
	PORTCR(245, 0xe60530f5), /* PORT245CR */
	PORTCR(246, 0xe60530f6), /* PORT246CR */
	PORTCR(247, 0xe60530f7), /* PORT247CR */
	PORTCR(248, 0xe60530f8), /* PORT248CR */
	PORTCR(249, 0xe60530f9), /* PORT249CR */

	PORTCR(250, 0xe60530fa), /* PORT250CR */
	PORTCR(251, 0xe60530fb), /* PORT251CR */
	PORTCR(252, 0xe60530fc), /* PORT252CR */
	PORTCR(253, 0xe60530fd), /* PORT253CR */
	PORTCR(254, 0xe60530fe), /* PORT254CR */
	PORTCR(255, 0xe60530ff), /* PORT255CR */
	PORTCR(256, 0xe6053100), /* PORT256CR */
	PORTCR(257, 0xe6053101), /* PORT257CR */
	PORTCR(258, 0xe6053102), /* PORT258CR */
	PORTCR(259, 0xe6053103), /* PORT259CR */

	PORTCR(260, 0xe6053104), /* PORT260CR */
	PORTCR(261, 0xe6053105), /* PORT261CR */
	PORTCR(262, 0xe6053106), /* PORT262CR */
	PORTCR(263, 0xe6053107), /* PORT263CR */
	PORTCR(264, 0xe6053108), /* PORT264CR */
	PORTCR(265, 0xe6053109), /* PORT265CR */
	PORTCR(266, 0xe605310a), /* PORT266CR */
	PORTCR(267, 0xe605310b), /* PORT267CR */
	PORTCR(268, 0xe605310c), /* PORT268CR */
	PORTCR(269, 0xe605310d), /* PORT269CR */

	PORTCR(270, 0xe605310e), /* PORT270CR */
	PORTCR(271, 0xe605310f), /* PORT271CR */
	PORTCR(272, 0xe6053110), /* PORT272CR */
	PORTCR(273, 0xe6053111), /* PORT273CR */
	PORTCR(274, 0xe6053112), /* PORT274CR */
	PORTCR(275, 0xe6053113), /* PORT275CR */
	PORTCR(276, 0xe6053114), /* PORT276CR */
	PORTCR(277, 0xe6053115), /* PORT277CR */
	PORTCR(278, 0xe6053116), /* PORT278CR */
	PORTCR(279, 0xe6053117), /* PORT279CR */

	PORTCR(280, 0xe6053118), /* PORT280CR */
	PORTCR(281, 0xe6053119), /* PORT281CR */
	PORTCR(282, 0xe605311a), /* PORT282CR */

	PORTCR(288, 0xe6052120), /* PORT288CR */
	PORTCR(289, 0xe6052121), /* PORT289CR */

	PORTCR(290, 0xe6052122), /* PORT290CR */
	PORTCR(291, 0xe6052123), /* PORT291CR */
	PORTCR(292, 0xe6052124), /* PORT292CR */
	PORTCR(293, 0xe6052125), /* PORT293CR */
	PORTCR(294, 0xe6052126), /* PORT294CR */
	PORTCR(295, 0xe6052127), /* PORT295CR */
	PORTCR(296, 0xe6052128), /* PORT296CR */
	PORTCR(297, 0xe6052129), /* PORT297CR */
	PORTCR(298, 0xe605212a), /* PORT298CR */
	PORTCR(299, 0xe605212b), /* PORT299CR */

	PORTCR(300, 0xe605212c), /* PORT300CR */
	PORTCR(301, 0xe605212d), /* PORT301CR */
	PORTCR(302, 0xe605212e), /* PORT302CR */
	PORTCR(303, 0xe605212f), /* PORT303CR */
	PORTCR(304, 0xe6052130), /* PORT304CR */
	PORTCR(305, 0xe6052131), /* PORT305CR */
	PORTCR(306, 0xe6052132), /* PORT306CR */
	PORTCR(307, 0xe6052133), /* PORT307CR */
	PORTCR(308, 0xe6052134), /* PORT308CR */
	PORTCR(309, 0xe6052135), /* PORT309CR */

	{ PINMUX_CFG_REG("MSEL2CR", 0xe605801c, 32, 1) {
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			MSEL2CR_MSEL19_0, MSEL2CR_MSEL19_1,
			MSEL2CR_MSEL18_0, MSEL2CR_MSEL18_1,
			MSEL2CR_MSEL17_0, MSEL2CR_MSEL17_1,
			MSEL2CR_MSEL16_0, MSEL2CR_MSEL16_1,
			0, 0,
			MSEL2CR_MSEL14_0, MSEL2CR_MSEL14_1,
			MSEL2CR_MSEL13_0, MSEL2CR_MSEL13_1,
			MSEL2CR_MSEL12_0, MSEL2CR_MSEL12_1,
			MSEL2CR_MSEL11_0, MSEL2CR_MSEL11_1,
			MSEL2CR_MSEL10_0, MSEL2CR_MSEL10_1,
			MSEL2CR_MSEL9_0, MSEL2CR_MSEL9_1,
			MSEL2CR_MSEL8_0, MSEL2CR_MSEL8_1,
			MSEL2CR_MSEL7_0, MSEL2CR_MSEL7_1,
			MSEL2CR_MSEL6_0, MSEL2CR_MSEL6_1,
			MSEL2CR_MSEL5_0, MSEL2CR_MSEL5_1,
			MSEL2CR_MSEL4_0, MSEL2CR_MSEL4_1,
			MSEL2CR_MSEL3_0, MSEL2CR_MSEL3_1,
			MSEL2CR_MSEL2_0, MSEL2CR_MSEL2_1,
			MSEL2CR_MSEL1_0, MSEL2CR_MSEL1_1,
			MSEL2CR_MSEL0_0, MSEL2CR_MSEL0_1,
		}
	},
	{ PINMUX_CFG_REG("MSEL3CR", 0xe6058020, 32, 1) {
			0, 0,
			0, 0,
			0, 0,
			MSEL3CR_MSEL28_0, MSEL3CR_MSEL28_1,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			0, 0,
			MSEL3CR_MSEL15_0, MSEL3CR_MSEL15_1,
			0, 0,
			0, 0,
			0, 0,
			MSEL3CR_MSEL11_0, MSEL3CR_MSEL11_1,
			0, 0,
			MSEL3CR_MSEL9_0, MSEL3CR_MSEL9_1,
			0, 0,
			0, 0,
			MSEL3CR_MSEL6_0, MSEL3CR_MSEL6_1,
			0, 0,
			0, 0,
			0, 0,
			MSEL3CR_MSEL2_0, MSEL3CR_MSEL2_1,
			0, 0,
			0, 0,
		}
	},
	{ PINMUX_CFG_REG("MSEL4CR", 0xe6058024, 32, 1) {
			0, 0,
			0, 0,
			MSEL4CR_MSEL29_0, MSEL4CR_MSEL29_1,
			0, 0,
			MSEL4CR_MSEL27_0, MSEL4CR_MSEL27_1,
			MSEL4CR_MSEL26_0, MSEL4CR_MSEL26_1,
			0, 0,
			0, 0,
			0, 0,
			MSEL4CR_MSEL22_0, MSEL4CR_MSEL22_1,
			MSEL4CR_MSEL21_0, MSEL4CR_MSEL21_1,
			MSEL4CR_MSEL20_0, MSEL4CR_MSEL20_1,
			MSEL4CR_MSEL19_0, MSEL4CR_MSEL19_1,
			0, 0,
			0, 0,
			0, 0,
			MSEL4CR_MSEL15_0, MSEL4CR_MSEL15_1,
			0, 0,
			MSEL4CR_MSEL13_0, MSEL4CR_MSEL13_1,
			MSEL4CR_MSEL12_0, MSEL4CR_MSEL12_1,
			MSEL4CR_MSEL11_0, MSEL4CR_MSEL11_1,
			MSEL4CR_MSEL10_0, MSEL4CR_MSEL10_1,
			MSEL4CR_MSEL9_0, MSEL4CR_MSEL9_1,
			MSEL4CR_MSEL8_0, MSEL4CR_MSEL8_1,
			MSEL4CR_MSEL7_0, MSEL4CR_MSEL7_1,
			0, 0,
			0, 0,
			MSEL4CR_MSEL4_0, MSEL4CR_MSEL4_1,
			0, 0,
			0, 0,
			MSEL4CR_MSEL1_0, MSEL4CR_MSEL1_1,
			0, 0,
		}
	},
	{ },
};

static struct pinmux_data_reg pinmux_data_regs[] = {
	{ PINMUX_DATA_REG("PORTL031_000DR", 0xe6054000, 32) {
			PORT31_DATA, PORT30_DATA, PORT29_DATA, PORT28_DATA,
			PORT27_DATA, PORT26_DATA, PORT25_DATA, PORT24_DATA,
			PORT23_DATA, PORT22_DATA, PORT21_DATA, PORT20_DATA,
			PORT19_DATA, PORT18_DATA, PORT17_DATA, PORT16_DATA,
			PORT15_DATA, PORT14_DATA, PORT13_DATA, PORT12_DATA,
			PORT11_DATA, PORT10_DATA, PORT9_DATA, PORT8_DATA,
			PORT7_DATA, PORT6_DATA, PORT5_DATA, PORT4_DATA,
			PORT3_DATA, PORT2_DATA, PORT1_DATA, PORT0_DATA }
	},
	{ PINMUX_DATA_REG("PORTD063_032DR", 0xe6055000, 32) {
			PORT63_DATA, PORT62_DATA, PORT61_DATA, PORT60_DATA,
			PORT59_DATA, PORT58_DATA, PORT57_DATA, PORT56_DATA,
			PORT55_DATA, PORT54_DATA, PORT53_DATA, PORT52_DATA,
			PORT51_DATA, PORT50_DATA, PORT49_DATA, PORT48_DATA,
			PORT47_DATA, PORT46_DATA, PORT45_DATA, PORT44_DATA,
			PORT43_DATA, PORT42_DATA, PORT41_DATA, PORT40_DATA,
			PORT39_DATA, PORT38_DATA, PORT37_DATA, PORT36_DATA,
			PORT35_DATA, PORT34_DATA, PORT33_DATA, PORT32_DATA }
	},
	{ PINMUX_DATA_REG("PORTD095_064DR", 0xe6055004, 32) {
			PORT95_DATA, PORT94_DATA, PORT93_DATA, PORT92_DATA,
			PORT91_DATA, PORT90_DATA, PORT89_DATA, PORT88_DATA,
			PORT87_DATA, PORT86_DATA, PORT85_DATA, PORT84_DATA,
			PORT83_DATA, PORT82_DATA, PORT81_DATA, PORT80_DATA,
			PORT79_DATA, PORT78_DATA, PORT77_DATA, PORT76_DATA,
			PORT75_DATA, PORT74_DATA, PORT73_DATA, PORT72_DATA,
			PORT71_DATA, PORT70_DATA, PORT69_DATA, PORT68_DATA,
			PORT67_DATA, PORT66_DATA, PORT65_DATA, PORT64_DATA }
	},
	{ PINMUX_DATA_REG("PORTR127_096DR", 0xe6056000, 32) {
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, PORT118_DATA, PORT117_DATA, PORT116_DATA,
			PORT115_DATA, PORT114_DATA, PORT113_DATA, PORT112_DATA,
			PORT111_DATA, PORT110_DATA, PORT109_DATA, PORT108_DATA,
			PORT107_DATA, PORT106_DATA, PORT105_DATA, PORT104_DATA,
			PORT103_DATA, PORT102_DATA, PORT101_DATA, PORT100_DATA,
			PORT99_DATA, PORT98_DATA, PORT97_DATA, PORT96_DATA }
	},
	{ PINMUX_DATA_REG("PORTR159_128DR", 0xe6056004, 32) {
			PORT159_DATA, PORT158_DATA, PORT157_DATA, PORT156_DATA,
			PORT155_DATA, PORT154_DATA, PORT153_DATA, PORT152_DATA,
			PORT151_DATA, PORT150_DATA, PORT149_DATA, PORT148_DATA,
			PORT147_DATA, PORT146_DATA, PORT145_DATA, PORT144_DATA,
			PORT143_DATA, PORT142_DATA, PORT141_DATA, PORT140_DATA,
			PORT139_DATA, PORT138_DATA, PORT137_DATA, PORT136_DATA,
			PORT135_DATA, PORT134_DATA, PORT133_DATA, PORT132_DATA,
			PORT131_DATA, PORT130_DATA, PORT129_DATA, PORT128_DATA }
	},
	{ PINMUX_DATA_REG("PORTR191_160DR", 0xe6056008, 32) {
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, 0, PORT164_DATA,
			PORT163_DATA, PORT162_DATA, PORT161_DATA, PORT160_DATA }
	},
	{ PINMUX_DATA_REG("PORTR223_192DR", 0xe605600C, 32) {
			PORT223_DATA, PORT222_DATA, PORT221_DATA, PORT220_DATA,
			PORT219_DATA, PORT218_DATA, PORT217_DATA, PORT216_DATA,
			PORT215_DATA, PORT214_DATA, PORT213_DATA, PORT212_DATA,
			PORT211_DATA, PORT210_DATA, PORT209_DATA, PORT208_DATA,
			PORT207_DATA, PORT206_DATA, PORT205_DATA, PORT204_DATA,
			PORT203_DATA, PORT202_DATA, PORT201_DATA, PORT200_DATA,
			PORT199_DATA, PORT198_DATA, PORT197_DATA, PORT196_DATA,
			PORT195_DATA, PORT194_DATA, PORT193_DATA, PORT192_DATA }
	},
	{ PINMUX_DATA_REG("PORTU255_224DR", 0xe6057000, 32) {
			PORT255_DATA, PORT254_DATA, PORT253_DATA, PORT252_DATA,
			PORT251_DATA, PORT250_DATA, PORT249_DATA, PORT248_DATA,
			PORT247_DATA, PORT246_DATA, PORT245_DATA, PORT244_DATA,
			PORT243_DATA, PORT242_DATA, PORT241_DATA, PORT240_DATA,
			PORT239_DATA, PORT238_DATA, PORT237_DATA, PORT236_DATA,
			PORT235_DATA, PORT234_DATA, PORT233_DATA, PORT232_DATA,
			PORT231_DATA, PORT230_DATA, PORT229_DATA, PORT228_DATA,
			PORT227_DATA, PORT226_DATA, PORT225_DATA, PORT224_DATA }
	},
	{ PINMUX_DATA_REG("PORTU287_256DR", 0xe6057004, 32) {
			0, 0, 0, 0,
			0, PORT282_DATA, PORT281_DATA, PORT280_DATA,
			PORT279_DATA, PORT278_DATA, PORT277_DATA, PORT276_DATA,
			PORT275_DATA, PORT274_DATA, PORT273_DATA, PORT272_DATA,
			PORT271_DATA, PORT270_DATA, PORT269_DATA, PORT268_DATA,
			PORT267_DATA, PORT266_DATA, PORT265_DATA, PORT264_DATA,
			PORT263_DATA, PORT262_DATA, PORT261_DATA, PORT260_DATA,
			PORT259_DATA, PORT258_DATA, PORT257_DATA, PORT256_DATA }
	},
	{ PINMUX_DATA_REG("PORTR319_288DR", 0xe6056010, 32) {
			0, 0, 0, 0,
			0, 0, 0, 0,
			0, 0, PORT309_DATA, PORT308_DATA,
			PORT307_DATA, PORT306_DATA, PORT305_DATA, PORT304_DATA,
			PORT303_DATA, PORT302_DATA, PORT301_DATA, PORT300_DATA,
			PORT299_DATA, PORT298_DATA, PORT297_DATA, PORT296_DATA,
			PORT295_DATA, PORT294_DATA, PORT293_DATA, PORT292_DATA,
			PORT291_DATA, PORT290_DATA, PORT289_DATA, PORT288_DATA }
	},
	{ },
};

#if 0
/* IRQ pins through INTCS with IRQ0->15 from 0x200 and IRQ16-31 from 0x3200 */
#define EXT_IRQ16L(n) intcs_evt2irq(0x200 + ((n) << 5))
#define EXT_IRQ16H(n) intcs_evt2irq(0x3200 + ((n - 16) << 5))
#else
#define EXT_IRQ16L(n) (n)
#define EXT_IRQ16H(n) (n)
#endif

static struct pinmux_irq pinmux_irqs[] = {
	PINMUX_IRQ(EXT_IRQ16H(19), PORT9_FN0),
	PINMUX_IRQ(EXT_IRQ16L(1), PORT10_FN0),
	PINMUX_IRQ(EXT_IRQ16L(0), PORT11_FN0),
	PINMUX_IRQ(EXT_IRQ16H(18), PORT13_FN0),
	PINMUX_IRQ(EXT_IRQ16H(20), PORT14_FN0),
	PINMUX_IRQ(EXT_IRQ16H(21), PORT15_FN0),
	PINMUX_IRQ(EXT_IRQ16H(31), PORT26_FN0),
	PINMUX_IRQ(EXT_IRQ16H(30), PORT27_FN0),
	PINMUX_IRQ(EXT_IRQ16H(29), PORT28_FN0),
	PINMUX_IRQ(EXT_IRQ16H(22), PORT40_FN0),
	PINMUX_IRQ(EXT_IRQ16H(23), PORT53_FN0),
	PINMUX_IRQ(EXT_IRQ16L(10), PORT54_FN0),
	PINMUX_IRQ(EXT_IRQ16L(9), PORT56_FN0),
	PINMUX_IRQ(EXT_IRQ16H(26), PORT115_FN0),
	PINMUX_IRQ(EXT_IRQ16H(27), PORT116_FN0),
	PINMUX_IRQ(EXT_IRQ16H(28), PORT117_FN0),
	PINMUX_IRQ(EXT_IRQ16H(24), PORT118_FN0),
	PINMUX_IRQ(EXT_IRQ16L(6), PORT147_FN0),
	PINMUX_IRQ(EXT_IRQ16L(2), PORT149_FN0),
	PINMUX_IRQ(EXT_IRQ16L(7), PORT150_FN0),
	PINMUX_IRQ(EXT_IRQ16L(12), PORT156_FN0),
	PINMUX_IRQ(EXT_IRQ16L(4), PORT159_FN0),
	PINMUX_IRQ(EXT_IRQ16H(25), PORT164_FN0),
	PINMUX_IRQ(EXT_IRQ16L(8), PORT223_FN0),
	PINMUX_IRQ(EXT_IRQ16L(3), PORT224_FN0),
	PINMUX_IRQ(EXT_IRQ16L(5), PORT227_FN0),
	PINMUX_IRQ(EXT_IRQ16H(17), PORT234_FN0),
	PINMUX_IRQ(EXT_IRQ16L(11), PORT238_FN0),
	PINMUX_IRQ(EXT_IRQ16L(13), PORT239_FN0),
	PINMUX_IRQ(EXT_IRQ16H(16), PORT249_FN0),
	PINMUX_IRQ(EXT_IRQ16L(14), PORT251_FN0),
	PINMUX_IRQ(EXT_IRQ16L(9), PORT308_FN0),
};

static struct pinmux_info sh73a0_pinmux_info = {
	.name = "sh73a0_pfc",
	.reserved_id = PINMUX_RESERVED,
	.data = { PINMUX_DATA_BEGIN, PINMUX_DATA_END },
	.input = { PINMUX_INPUT_BEGIN, PINMUX_INPUT_END },
	.input_pu = { PINMUX_INPUT_PULLUP_BEGIN, PINMUX_INPUT_PULLUP_END },
	.input_pd = { PINMUX_INPUT_PULLDOWN_BEGIN, PINMUX_INPUT_PULLDOWN_END },
	.output = { PINMUX_OUTPUT_BEGIN, PINMUX_OUTPUT_END },
	.mark = { PINMUX_MARK_BEGIN, PINMUX_MARK_END },
	.function = { PINMUX_FUNCTION_BEGIN, PINMUX_FUNCTION_END },

	.first_gpio = GPIO_PORT0,
	.last_gpio = GPIO_FN_FSIAISLD_PU,

	.gpios = pinmux_gpios,
	.cfg_regs = pinmux_config_regs,
	.data_regs = pinmux_data_regs,

	.gpio_data = pinmux_data,
	.gpio_data_size = ARRAY_SIZE(pinmux_data),

	.gpio_irq = pinmux_irqs,
	.gpio_irq_size = ARRAY_SIZE(pinmux_irqs),
};

void sh73a0_pinmux_init(void)
{
	register_pinmux(&sh73a0_pinmux_info);
}
