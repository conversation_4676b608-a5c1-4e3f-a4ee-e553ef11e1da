From b4288a3e52b84fc359712317c49bfe1294bfdd4e Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 17 Jul 2025 12:40:57 -0700
Subject: [PATCH] jool: Upgrading cryptographically weaker MD5 to secure
 SHA-256 algorithm

Change-Id: I87d00b7e4f695ae588c89cda8c9f837c1fa74887
---
 .../jool/src/mod/common/db/pool4/rfc6056.c         | 14 +++++++-------
 1 file changed, 7 insertions(+), 7 deletions(-)

diff --git a/kernel_modules/jool/src/mod/common/db/pool4/rfc6056.c b/kernel_modules/jool/src/mod/common/db/pool4/rfc6056.c
index 0c5b37e..2189ca6 100644
--- a/kernel_modules/jool/src/mod/common/db/pool4/rfc6056.c
+++ b/kernel_modules/jool/src/mod/common/db/pool4/rfc6056.c
@@ -92,10 +92,10 @@ int rfc6056_setup(void)
 	get_random_bytes(secret_key, secret_key_len);
 
 	/* TFC stuff */
-	shash = crypto_alloc_shash("md5", 0, CRYPTO_ALG_ASYNC);
+	shash = crypto_alloc_shash("sha256", 0, CRYPTO_ALG_ASYNC);
 	if (IS_ERR(shash)) {
 		error = PTR_ERR(shash);
-		log_warn_once("Failed to load transform for MD5; errcode %d",
+		log_warn_once("Failed to load transform for SHA-256; errcode %d",
 				error);
 		__wkfree("Secret key", secret_key);
 		return error;
@@ -154,9 +154,9 @@ static int hash_tuple(struct shash_desc *desc, __u8 fields,
 int rfc6056_f(struct xlation *state, unsigned int *result)
 {
 	union {
-		__be32 as32[4];
-		__u8 as8[16];
-	} md5_result;
+		__be32 as32[8];
+		__u8 as8[32];
+	} sha256_result;
 	struct shash_desc *desc;
 	int error = 0;
 
@@ -184,13 +184,13 @@ int rfc6056_f(struct xlation *state, unsigned int *result)
 		goto end;
 	}
 
-	error = crypto_shash_final(desc, md5_result.as8);
+	error = crypto_shash_final(desc, sha256_result.as8);
 	if (error) {
 		log_debug(state, "crypto_hash_digest() error: %d", error);
 		goto end;
 	}
 
-	*result = (__force __u32)md5_result.as32[3];
+	*result = (__force __u32)sha256_result.as32[7];
 	/* Fall through. */
 
 end:
-- 
2.39.1

